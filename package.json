{"name": "carnot", "version": "1.0.0", "scripts": {"ng": "ng", "build": "ng build", "start": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "prettier": "npx prettier --write ."}, "browserslist": [], "private": true, "dependencies": {"@angular/animations": "^17.0.8", "@angular/cdk": "^17.0.4", "@angular/common": "^17.0.8", "@angular/compiler": "^17.0.8", "@angular/core": "^17.0.8", "@angular/flex-layout": "15.0.0-beta.42", "@angular/forms": "^17.0.8", "@angular/material": "^17.0.4", "@angular/platform-browser": "^17.0.8", "@angular/platform-browser-dynamic": "^17.0.8", "@angular/router": "^17.0.8", "@optometristpritam/leaflet-height": "^1.0.0", "angular-plotly.js": "^5.2.2", "bootstrap": "^5.3.2", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "idb": "^8.0.0", "jquery": "^3.7.1", "keycloak-angular": "^15.1.0", "keycloak-js": "^23.0.6", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-kml": "^1.0.1", "leaflet-side-by-side": "^2.2.0", "mat-select-search": "^1.2.21", "ngx-toastr": "^18.0.0", "node-gyp": "^10.0.1", "plotly.js-dist-min": "^2.28.0", "rxjs": "~7.8.1", "zone.js": "^0.14.3"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.0", "@angular-devkit/build-angular": "^17.0.9", "@angular-eslint/eslint-plugin": "^17.1.1", "@angular/cli": "~17.0.9", "@angular/compiler-cli": "^17.0.8", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "css-loader": "^6.9.1", "dotenv-webpack": "^8.0.1", "eslint": "^8.56.0", "eslint-plugin-import": "^2.29.1", "jasmine-core": "~5.1.1", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "leaflet-i18n": "^0.3.4", "node-sass": "^9.0.0", "prettier": "^3.2.2", "sass-loader": "^13.3.3", "style-loader": "^3.3.4", "ts-loader": "^9.5.1", "typescript": "^5.2.2", "webpack": "^5.89.0"}}