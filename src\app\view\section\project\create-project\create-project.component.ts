import { Component, ViewChild } from '@angular/core';
import { MatStepper } from '@angular/material/stepper';

@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.css'],
})
export class CreateProjectComponent {
  @ViewChild('stepper') private stepper: MatStepper;

  goToStep(index: number) {
    this.stepper.selectedIndex = index;
  }
}
