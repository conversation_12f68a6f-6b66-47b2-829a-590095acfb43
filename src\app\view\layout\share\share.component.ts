import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-share',
  templateUrl: './share.component.html',
  styleUrls: ['./share.component.css'],
})
export class ShareComponent implements OnInit {
  usersData: any = [];
  Isworking = false;
  constructor(
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private toastr: ToastrService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ShareComponent>
  ) {}

  ngOnInit(): void {
    const usersWithAccessSet = new Set(this.data.usersWithAccess.map(user => user.db_id));
    this.data.allUsers.forEach(user => {
      user.access = usersWithAccessSet.has(user.db_id);
    });
    this.usersData = this.data.allUsers;
  }
  updateRole(user) {
    this.Isworking = true;
    const user_data = {
      db_id: user.db_id,
      project_id: this.data.projectId,
      access: user.access,
    };
    this.apiConfigService.addProjectUser(user_data).subscribe(
      (data: any) => {
        if (data.status == 'success') {
          this.toastr.success(data.message);
          this.sharedDataService.remove('get_project_by_category');
          this.Isworking = false;
        }
      },
      err => {
        this.Isworking = false;
        this.toastr.error(err.message, 'Error Occured');
      }
    );
  }
  onClose() {
    this.dialogRef.close();
  }
}
