import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { KeycloakInitOptions } from 'keycloak-js';
import { Subscription, interval } from 'rxjs';
import { environment } from 'src/environments/environment';

export function initializeKeycloak(keycloak: KeycloakService) {
  return async () => {
    const initOptions: KeycloakInitOptions = {
      onLoad: 'check-sso',
      flow: 'hybrid',
      checkLoginIframe: false,
    };
    try {
      await keycloak.init({
        config: environment.keycloak,
        initOptions,
        enableBearerInterceptor: true,
      });
    } catch (error) {
      console.error('Error initializing Keycloak', error);
    }
  };
}

@Injectable({
  providedIn: 'root',
})
export class AuthService implements OnDestroy {
  private tokenRefreshInterval: Subscription;
  private readonly customTokenKey = 'token';

  constructor(
    private keycloak: KeycloakService,
    private http: HttpClient
  ) {
    this.startTokenRefresh();
  }

  // Custom login method via Django API
  loginWithEmail(data: any): Promise<boolean> {
    return this.http
      .post<any>(`${environment.carnot_url}accounts/login/`, data)
      .toPromise()
      .then(response => {
        const customToken = response.token;
        if (customToken) {
          this.setCustomToken(customToken);
        }
        return response;
      })
      .catch(error => {
        return error.error;
      });
  }

  // Logout handling both Keycloak and custom token
  async logout(): Promise<void> {
    this.stopTokenRefresh();
    if (this.getCustomToken()) {
      this.clearCustomToken();
      return Promise.resolve();
    }
    return this.keycloak.logout();
  }

  // Login using Keycloak Social API
  loginWithSocial(provider: string): Promise<void> {
    return this.keycloak.login({ idpHint: provider });
  }

  // Check if user is authenticated
  isLoggedIn(): boolean {
    return !!this.getCustomToken() || this.keycloak.isLoggedIn();
  }

  // Get the token (Keycloak or custom)
  async getToken(): Promise<string> {
    const customToken = this.getCustomToken();
    if (customToken) {
      return Promise.resolve(customToken['access_token']);
    }
    return this.keycloak.getToken();
  }

  // Refresh the Keycloak token if it will expire in 60 seconds or less
  private refreshKeycloakToken(): Promise<boolean> {
    return this.keycloak.updateToken(60);
  }

  private async refreshCustomToken(): Promise<boolean> {
    try {
      const customToken = this.getCustomToken();
      const response = await this.http
        .post<any>(`${environment.carnot_url}accounts/refresh_token/`, {
          refresh_token: customToken['refresh_token'],
        })
        .toPromise();
      if (response && response['token']) {
        this.setCustomToken(response['token']);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to refresh custom token:', error);
      return false;
    }
  }

  // Start token refresh interval
  private startTokenRefresh(): void {
    const refreshIntervalInSeconds = 240; // the interval to 4 minutes (240 seconds)
    this.tokenRefreshInterval = interval(refreshIntervalInSeconds * 1000).subscribe(async () => {
      try {
        if (this.getCustomToken()) {
          await this.refreshCustomToken();
        } else {
          await this.refreshKeycloakToken();
        }
      } catch (error) {
        console.error('Failed to refresh token', error);
      }
    });
  }

  // Stop token refresh interval
  private stopTokenRefresh(): void {
    if (this.tokenRefreshInterval) {
      this.tokenRefreshInterval.unsubscribe();
    }
  }

  // Private methods to handle custom token securely
  setCustomToken(token: string): void {
    localStorage.setItem(this.customTokenKey, JSON.stringify(token));
  }

  getCustomToken(): string | null {
    return JSON.parse(localStorage.getItem(this.customTokenKey));
  }

  private clearCustomToken(): void {
    localStorage.removeItem(this.customTokenKey);
  }

  ngOnDestroy(): void {
    this.stopTokenRefresh();
  }
}
