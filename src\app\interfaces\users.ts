export interface Company {
  name: string;
  id: string;
  // logo: string;
}
export interface Session {
  ipAddress: string;
  start: number;
  lastAccess: number;
  rememberMe: boolean;
}
export interface Users {
  id: string;
  db_id: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  company: Company;
  sessions: Session[];
  contact: number;
  country: string;
  state: string;
  city: string;
  pincode: number;
}
export class UsersC {
  id: string;
  db_id: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  company: Company;
  sessions: Session[];
  contact: number;
  country: string;
  state: string;
  city: string;
  pincode: number;
  constructor(obj?: UsersC) {
    this.id = (obj && obj.id) || '';
    this.db_id = (obj && obj.db_id) || '';
    this.username = (obj && obj.username) || '';
    this.first_name = (obj && obj.first_name) || '';
    this.last_name = (obj && obj.last_name) || '';
    this.email = (obj && obj.email) || '';
    this.role = (obj && obj.role) || '';
    this.company = (obj && obj.company) || null;
    this.sessions = (obj && obj.sessions) || [];
    this.contact = (obj && obj.contact) || null;
    this.country = (obj && obj.country) || '';
    this.state = (obj && obj.state) || '';
    this.city = (obj && obj.city) || '';
    this.pincode = (obj && obj.pincode) || null;
  }
}
