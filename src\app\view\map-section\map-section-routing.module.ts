import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MapSectionComponent } from './map-section.component';
import { ComparisionComponent } from './sub-components/comparision/comparision.component';
import { CompChartDashboardComponent } from './comp-chart-dashboard/comp-chart-dashboard.component';

const routes: Routes = [
  {
    path: '',
    component: MapSectionComponent,
  },
  {
    path: 'compare-map',
    component: ComparisionComponent,
  },
  {
    path: 'dem-dashboard',
    component: CompChartDashboardComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MapSectionRoutingModule {}
