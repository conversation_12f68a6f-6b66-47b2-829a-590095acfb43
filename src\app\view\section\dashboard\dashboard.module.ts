import { SharedModule } from '@/directives/shared.module';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { PlotlyModule } from 'angular-plotly.js';
import * as PlotlyJS from 'plotly.js-dist-min';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { DashboardMapComponent } from './dashboard-map/dashboard-map.component';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { ProjectDetailsComponent } from './project-details/project-details.component';

PlotlyModule.plotlyjs = PlotlyJS;

@NgModule({
  declarations: [DashboardComponent, DashboardMapComponent, ProjectDetailsComponent],
  imports: [CommonModule, DashboardRoutingModule, MaterialLibrary, PlotlyModule, SharedModule],
})
export class DashboardModule {}
