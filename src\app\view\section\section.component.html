<mat-sidenav-container class="layout-container">
  <navbar [isMobileMenuOpen]="isMobileSidebarOpen" (toggleSidebar)="onToggleSidebar()"></navbar>

  <!-- Desktop Sidebar -->
  <mat-sidenav #sidenav [opened]="!isMobile" [mode]="sidenavMode" class="sidenav" *ngIf="!isMobile">
    <sidebar></sidebar>
  </mat-sidenav>

  <!-- Mobile Sidebar -->
  <div class="mobile-sidebar-wrapper" [class.active]="isMobileSidebarOpen" *ngIf="isMobile">
    <div
      class="mobile-overlay"
      [class.active]="isMobileSidebarOpen"
      (click)="closeMobileSidebar()"
      *ngIf="isMobileSidebarOpen"></div>

    <div class="mobile-sidebar" [class.open]="isMobileSidebarOpen">
      <sidebar [isMobileOpen]="isMobileSidebarOpen" (closeMobile)="closeMobileSidebar()"></sidebar>
    </div>
  </div>

  <mat-sidenav-content class="content-area">
    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>
