import { AuthService } from '@/controller/auth.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
})
export class NavbarComponent {
  @Input() isMobileMenuOpen = false;
  @Output() toggleSidebar = new EventEmitter<void>();

  constructor(
    private sharedDataService: SharedDataService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  toggleMobileMenu(): void {
    this.toggleSidebar.emit();
  }

  logout(): void {
    this.authService.logout();
    this.toastr.success('You have been successfully logged out', 'Logout');
    this.sharedDataService.clearData();
  }
}
