import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-section',
  templateUrl: './section.component.html',
  styleUrls: ['./section.component.css'],
})
export class SectionComponent implements OnInit, OnDestroy {
  isMobile = false;
  isMobileSidebarOpen = false;
  sidenavMode: 'side' | 'over' = 'side';
  private breakpointSubscription: Subscription;

  constructor(private breakpointObserver: BreakpointObserver) {}

  ngOnInit(): void {
    this.setupBreakpointObserver();
  }

  ngOnDestroy(): void {
    this.breakpointSubscription?.unsubscribe();
  }

  private setupBreakpointObserver(): void {
    this.breakpointSubscription = this.breakpointObserver
      .observe([Breakpoints.Handset, Breakpoints.TabletPortrait])
      .subscribe(result => {
        this.isMobile = result.matches;
        this.sidenavMode = this.isMobile ? 'over' : 'side';
        if (!this.isMobile) {
          this.isMobileSidebarOpen = false;
        }
      });
  }
  onToggleSidebar(): void {
    if (this.isMobile) {
      this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    }
  }
  closeMobileSidebar(): void {
    this.isMobileSidebarOpen = false;
  }
}
