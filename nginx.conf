# user  nginx;
# worker_processes  auto;
# error_log  /var/log/nginx/error.log warn;
# pid        /var/run/nginx.pid;

# events {
#     worker_connections  1024;
# }
#
# http {
#     include       /etc/nginx/mime.types;
#     default_type  application/octet-stream;
#
#     log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
#                       '$status $body_bytes_sent "$http_referer" '
#                       '"$http_user_agent" "$http_x_forwarded_for"';
#
#     access_log  /var/log/nginx/access.log  main;
#
#     sendfile        on;
#
#     keepalive_timeout  65;

    ####################################################################
    # Commented out the above as its taken from default.conf of nginx  #
    ####################################################################

    server {
            listen 8080 default_server;
            listen [::]:8080 default_server ipv6only=on;


            listen 443 ssl;
            listen [::]:443 ssl;

            server_name carnot.datasee.ai;
            root   /usr/share/nginx/html;

            #####################################
            #         SSL Settings              #
            #####################################
            ssl_certificate /etc/letsencrypt/live/carnot.datasee.ai/fullchain.pem;
            ssl_certificate_key /etc/letsencrypt/live/carnot.datasee.ai/privkey.pem;

            # include /etc/letsencrypt/options-ssl-nginx.conf;
            # ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;


            ssl_session_timeout 1d;
            ssl_session_cache shared:SSL:50m;
            ssl_session_tickets off;

            ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
            ssl_prefer_server_ciphers on;
            ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

            #####################################
            #        Gzip Settings              #
            #####################################
             gzip on;
             gzip_http_version 1.1;
             gzip_disable      "MSIE [1-6]\.";
             gzip_min_length   256;
             gzip_vary         on;
             gzip_proxied      expired no-cache no-store private auth;
             gzip_types        text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
             gzip_comp_level   9;
             gzip_buffers 32 8k;

            server_tokens off;

            location ~ /index.html|.*\.json$ {
              expires -1;
              add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
              include /etc/nginx/security-headers.conf;
            }

            location ~ .*\.css$|.*\.js$ {
              add_header Cache-Control 'max-age=31449600'; # one year
              include /etc/nginx/security-headers.conf;
            }

            location / {
            # proxy_pass http://localhost:8001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;

              try_files $uri$args $uri$args/ /index.html;
              add_header Set-Cookie "name=value; SameSite=Strict;";
              add_header Cache-Control "public, max-age=31536000";
              include /etc/nginx/security-headers.conf;
            }

            error_page   500 502 503 504  /50x.html;
            location = /50x.html {
                root   /usr/share/nginx/html;
            }
        }

    server {
        if ($host = carnot.datasee.ai) {
            return 301 https://$host$request_uri;
        } # managed by Certbot


      listen 80 ;
      listen [::]:80 ;
        server_name carnot.datasee.ai;
        return 404; # managed by Certbot
    }

# }
