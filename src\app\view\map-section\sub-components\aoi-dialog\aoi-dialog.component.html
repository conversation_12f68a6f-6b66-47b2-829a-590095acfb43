<div class="data">
  <p class="title mb-2">Area of Interest</p>
  <form #drawtoolForm="ngForm">
    <div class="row">
      <div class="col-lg-12">
        <mat-form-field appearance="fill" class="w-100">
          <mat-label>Label</mat-label>
          <input type="text" matInput name="label" [(ngModel)]="data.drawtool.label" required />
        </mat-form-field>
      </div>
      <div class="col-lg-12">
        <mat-form-field appearance="fill" class="w-100">
          <mat-label>Description</mat-label>
          <textarea
            type="text"
            matInput
            name="description"
            [(ngModel)]="data.drawtool.description"
            required></textarea>
        </mat-form-field>
      </div>
    </div>
    <mat-dialog-actions style="justify-content: space-evenly">
      <button mat-button (click)="onCancel()" class="w-25 text-uppercase">Cancel</button>
      <button
        mat-button
        (click)="onSave()"
        [disabled]="!drawtoolForm.valid"
        class="w-25 text-uppercase">
        Save
      </button>
    </mat-dialog-actions>
  </form>
</div>
