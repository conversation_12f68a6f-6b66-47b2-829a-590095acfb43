<div class="data">
  <p class="title mb-2">Edit User</p>
  <mat-form-field appearance="outline" class="w-100 mt-2">
    <mat-label>Name</mat-label>
    <input matInput type="text" disabled name="name" [(ngModel)]="data.user.name" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="w-100 mt-2">
    <mat-label>Email</mat-label>
    <input matInput type="text" disabled name="email" [(ngModel)]="data.user.email" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="w-100 mt-2">
    <mat-label>Created On</mat-label>
    <input
      matInput
      type="text"
      disabled
      name="created_on"
      [ngModel]="data.user.created_on | date: 'dd-MM-yyyy HH:mm:ss a'" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="w-100 mt-2">
    <mat-label>Role</mat-label>
    <mat-select name="role" [(ngModel)]="data.user.role">
      <mat-option [value]="null" disabled>Please select an option</mat-option>
      <mat-option *ngFor="let option of data.role" [value]="option">
        {{ option | uppercase }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-dialog-actions style="justify-content: space-evenly">
    <button mat-button class="w-25 text-uppercase" [disabled]="Isworking" (click)="onUpdate()">
      <span *ngIf="!Isworking">Submit</span>
      <span *ngIf="Isworking">
        <div class="spinner-border" role="status" *ngIf="Isworking"></div>
      </span>
    </button>
    <button mat-button (click)="onClose()" class="w-25 text-uppercase">Close</button>
  </mat-dialog-actions>
</div>
