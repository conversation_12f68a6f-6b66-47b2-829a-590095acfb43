import { ApiConfigService } from '@/controller/api-config.service';
import { Component } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css'],
})
export class ResetPasswordComponent {
  resetForm: FormGroup;
  Isworking = false;

  constructor(
    private router: Router,
    private apiConfigService: ApiConfigService,
    private toastr: ToastrService
  ) {
    this.resetForm = new FormGroup({
      email: new FormControl('', [
        Validators.required,
        Validators.email,
        Validators.pattern('[a-z0-9.]+@[a-z]+.[a-z]{2,3}'),
      ]),
    });
  }
  hasError = (controlast_name: string, errorName: string) => {
    return this.resetForm.controls[controlast_name].hasError(errorName);
  };
  submit() {
    this.Isworking = true;
    this.apiConfigService.generateLink(this.resetForm.getRawValue()).subscribe(
      (res: any) => {
        if (res['status'] == 'success') {
          this.toastr.success(res.message);
          this.router.navigate(['/app', 'home']);
          this.Isworking = false;
        }
      },
      err => {
        this.toastr.error(err.error.message);
        this.Isworking = false;
      }
    );
  }
}
