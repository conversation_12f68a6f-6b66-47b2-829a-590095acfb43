import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { ProfileModalComponent } from '../profile-modal/profile-modal.component';

@Component({
  selector: 'app-manageuser',
  templateUrl: './manageuser.component.html',
  styleUrls: ['./manageuser.component.css'],
})
export class ManageuserComponent implements OnInit {
  users_data: any = [];
  roles: [] = [];

  constructor(
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private dialog: MatDialog,
    private userService: UserService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.apiCall();
  }

  apiCall() {
    const role = this.userService.getRole();
    const group_id = this.userService.getGroupId();

    if (role === 'admin' && group_id) {
      this.sharedDataService
        .fetchData(this.apiConfigService.getManageUsers(group_id), 'manage_users')
        .subscribe(data => {
          if (data) {
            this.users_data = data;
            this.sharedDataService
              .fetchData(this.apiConfigService.getRoles(), 'roles')
              .subscribe(data => {
                if (data) {
                  this.roles = data;
                }
              });
          }
        });
    }
  }
  openDialog(user) {
    const dialogRef = this.dialog.open(ProfileModalComponent, {
      data: { user: user, role: this.roles },
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.dataModified) {
          this.sharedDataService.remove('manage_users');
          this.ngOnInit();
        }
      }
    });
  }
  toggleUser(user) {
    const data = {
      id: user.id,
      enabled: user.enabled,
      email: user.email,
    };
    this.apiConfigService.activeAccess(data).subscribe(
      (data: any) => {
        if (data.status == 'success') {
          this.toastr.success(data.message);
          this.sharedDataService.remove('manage_users');
          this.ngOnInit();
        }
      },
      err => {
        this.toastr.error(err.message, 'Error Occured');
      }
    );
  }
}
