<div
  id="compact-sidebar"
  class="sidebar-base"
  [class.sidebar-mobile]="isMobileOpen"
  [class.mobile-open]="isMobileOpen">
  <!-- Mobile Close Button -->
  <button
    class="close-btn"
    *ngIf="isMobileOpen"
    (click)="closeSidebar()"
    aria-label="Close Sidebar"
    mat-icon-button>
    <mat-icon>close</mat-icon>
  </button>

  <!-- Top Navigation -->
  <nav class="fix-top">
    <mat-card class="logo-img">
      <img class="p-1" src="{{ sidebar_logo }}" *ngIf="sidebar_logo" alt="Logo" />
    </mat-card>
    <ul class="nav-list p-0 m-0">
      <li
        [routerLink]="['/app', 'home']"
        class="nav-item"
        [class.active]="selectedIcon === 'home'"
        (click)="selectItem('home')"
        matTooltip="Home"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">home</mat-icon>
        <span class="nav-text">Home</span>
      </li>

      <li
        *appHasRole="['admin', 'manager']"
        [routerLink]="['/app', 'project', 'create']"
        class="nav-item"
        [class.active]="selectedIcon === 'create'"
        (click)="selectItem('create')"
        matTooltip="Create New Project"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">create_new_folder</mat-icon>
        <span class="nav-text">Create Project</span>
      </li>

      <li
        [routerLink]="['/app', 'project', 'all']"
        class="nav-item"
        [class.active]="selectedIcon === 'projects'"
        (click)="selectItem('projects')"
        matTooltip="All Projects"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">space_dashboard</mat-icon>
        <span class="nav-text">Projects</span>
      </li>

      <li
        *ngIf="isAnalytics"
        [routerLink]="['/map']"
        class="nav-item"
        [class.active]="selectedIcon === 'map'"
        (click)="selectItem('map')"
        matTooltip="Map Page"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">map</mat-icon>
        <span class="nav-text">Map</span>
      </li>
    </ul>
  </nav>

  <!-- Bottom Navigation -->
  <nav class="fix-bottom">
    <ul class="nav-list p-0 m-0">
      <li
        *appHasRole="'admin'"
        [routerLink]="['/app', 'manage-users']"
        class="nav-item"
        [class.active]="selectedIcon === 'manage-users'"
        (click)="selectItem('manage-users')"
        matTooltip="Manage Users"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">supervised_user_circle</mat-icon>
        <span class="nav-text">Manage Users</span>
      </li>

      <li
        [routerLink]="['/app', 'my-profile']"
        class="nav-item"
        [class.active]="selectedIcon === 'my-profile'"
        (click)="selectItem('my-profile')"
        matTooltip="My Profile"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">account_circle</mat-icon>
        <span class="nav-text">Profile</span>
      </li>

      <li
        class="nav-item"
        [class.active]="selectedIcon === 'help'"
        (click)="selectItem('help'); openHelpCenter()"
        matTooltip="Help Center"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">question_answer</mat-icon>
        <span class="nav-text">Help Center</span>
      </li>

      <li
        class="nav-item"
        [class.active]="selectedIcon === 'logout'"
        (click)="selectItem('logout'); logout()"
        matTooltip="Log Out"
        matTooltipPosition="right"
        matTooltipDisabled="{{ isMobileOpen }}">
        <mat-icon class="nav-icon">logout</mat-icon>
        <span class="nav-text">Log Out</span>
      </li>
    </ul>
  </nav>
</div>
