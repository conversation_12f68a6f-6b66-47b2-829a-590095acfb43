import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';

@Component({
  selector: 'app-dialog-example',
  templateUrl: './dialog-example.component.html',
  styleUrls: ['./dialog-example.component.css'],
})
export class DialogExampleComponent implements OnInit {
  dateOfBirth = new FormControl();
  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {}
  studentForm = this.formBuilder.group({
    name: '',
    dateOfBirth: '',
    admissionDate: '',
  });
}
