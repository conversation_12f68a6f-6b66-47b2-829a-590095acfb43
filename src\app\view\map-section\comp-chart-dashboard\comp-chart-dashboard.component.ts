import { Component, OnInit, HostListener } from '@angular/core';
import { ApiConfigService } from '@/controller/api-config.service';

@Component({
  selector: 'app-comp-chart-dashboard',
  templateUrl: './comp-chart-dashboard.component.html',
  styleUrls: ['./comp-chart-dashboard.component.css'],
})
export class CompChartDashboardComponent implements OnInit {
  slideIndex = 0;
  slideIndexdot = 0;
  base_path: string = '';
  slides = [];

  constructor(private apiConfigService: ApiConfigService) {}

  ngOnInit(): void {
    this.apiConfigService.getProjectData().subscribe(data => {
      const date = localStorage.getItem('date');
      const report_path = data['data']['processed_data'][date]['report_path']['DEM'];
      this.slides = report_path.slides;
      this.base_path = report_path.base_url;
      this.updateSlideNumber();
    });
  }

  get currentSlide() {
    return this.slides[this.slideIndex];
  }

  updateSlideNumber() {
    const slideNumber = document.querySelector('.slide-number');
    if (slideNumber) {
      slideNumber.textContent = `${this.slideIndex + 1} / ${this.slides.length}`;
    }
  }

  changeSlide(n: number) {
    this.slideIndex = (this.slideIndex + n + this.slides.length) % this.slides.length;
    this.slideIndexdot = this.slideIndex;
    this.updateSlideNumber();
  }

  toggleFullscreen() {
    const elem = document.querySelector('.slideshow-container') as HTMLElement;
    if (!document.fullscreenElement && elem) {
      elem.requestFullscreen().catch(err => {
        alert(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      document.exitFullscreen();
    }
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'ArrowLeft') this.changeSlide(-1);
    if (event.key === 'ArrowRight') this.changeSlide(1);
    if (event.key === 'f' || event.key === 'F') this.toggleFullscreen();
  }
}
