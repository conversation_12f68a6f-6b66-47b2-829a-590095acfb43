<div class="compact-sidebar">
  <div class="sidebar-menu">
    <mat-card class="menu-img">
      <img class="p-1" src="{{ sidebar_logo }}" *ngIf="sidebar_logo" />
    </mat-card>
    <div class="menu" [routerLink]="['/app/home']">
      <div class="menu-active"></div>
      <mat-icon matTooltip="Home">home</mat-icon>
    </div>
    <div class="menu" [routerLink]="['/app/project/all']">
      <div class="menu-active"></div>
      <mat-icon matTooltip="All projects">dashboard</mat-icon>
    </div>
    <div class="top-section" *appHasRole="['admin', 'manager']">
      <div
        class="menu"
        (click)="activateMenu('summary'); openSidbar('summarySidebar', 'summary')"
        *ngIf="category === 'thermography'">
        <div class="menu-active" id="summary"></div>
        <mat-icon matTooltip="Summary">pie_chart</mat-icon>
      </div>
      <div
        class="menu"
        (click)="activateMenu('invertor'); openSidbar('summarySidebar', 'invertor')"
        *ngIf="category === 'thermography'">
        <div class="menu-active" id="invertor"></div>
        <mat-icon matTooltip="Inverter">all_inbox</mat-icon>
      </div>
      <div
        class="menu"
        (click)="activateMenu('Topography'); openSidbar('summarySidebar', 'Topography')"
        *ngIf="category === 'topography'">
        <div class="menu-active" id="Topography"></div>
        <mat-icon matTooltip="Topography">settings_input_composite</mat-icon>
      </div>
      <div
        class="menu"
        (click)="activateMenu('Cadastral'); openSidbar('summarySidebar', 'Cadastral')"
        *ngIf="cadastral_present">
        <div class="menu-active" id="Cadastral"></div>
        <mat-icon matTooltip="Cadastral">edit_road</mat-icon>
      </div>
      <div
        class="menu"
        (click)="activateMenu('Grading'); openSidbar('summarySidebar', 'Grading')"
        *ngIf="category === 'grading'">
        <div class="menu-active" id="Grading"></div>
        <mat-icon matTooltip="Grading">credit_card</mat-icon>
      </div>
      <div
        class="menu"
        (click)="activateMenu('Vegetation'); openSidbar('summarySidebar', 'Vegetation')"
        *ngIf="category === 'vegetation'">
        <div class="menu-active" id="Vegetation"></div>
        <mat-icon>credit_card</mat-icon>
      </div>
      <div class="menu" (click)="activateMenu('compare'); openComparision2()" *ngIf="compare_icon">
        <div class="menu-active" id="compare"></div>
        <mat-icon matTooltip="Compare">compare</mat-icon>
      </div>
      <div class="menu" [routerLink]="['/app/analytics']">
        <div class="menu-active" id="activity"></div>
        <mat-icon matTooltip="Analytics">analytics</mat-icon>
      </div>
      <div class="menu" (click)="activateMenu('aoi'); openSidbar('summarySidebar', 'aoi')">
        <div class="menu-active" id="aoi"></div>
        <mat-icon matTooltip="Area of Intrest">border_color</mat-icon>
      </div>
      <div class="menu" (click)="openSharedialog()">
        <div class="menu-active" id="share"></div>
        <mat-icon matTooltip="Share">share</mat-icon>
      </div>
      <div class="menu" [routerLink]="['/app/defect-recification']">
        <div class="menu-active" id="share"></div>
        <mat-icon matTooltip="Defect Rectification">list_alt_check</mat-icon>
      </div>
      <div class="menu" [routerLink]="['/app/powerloss-dashboard']">
        <div class="menu-active" id="share"></div>
        <mat-icon matTooltip="Powerloss Dashboard">trending_up</mat-icon>
      </div>
      <div class="menu" (click)="openReportdialog()">
        <div class="menu-active" id="download"></div>
        <mat-icon matTooltip="Download">download</mat-icon>
      </div>
    </div>
  </div>
</div>
<div class="sidebar-card" id="summarySidebar">
  <div class="header">
    <span *ngIf="currentMenu === 'summary'">Summary</span>
    <span *ngIf="currentMenu === 'invertor'">Inverter</span>
    <span *ngIf="currentMenu === 'Topography'">Topography Features</span>
    <span *ngIf="currentMenu === 'Grading'">Grading Features</span>
    <span *ngIf="currentMenu === 'Vegetation'">Vegetation Features</span>
    <span *ngIf="currentMenu === 'Cadastral'">Cadastral</span>
    <span *ngIf="currentMenu === 'aoi'">Area of Intrest</span>
    <span class="card-icon ml-4">
      <mat-icon
        (click)="closeSidebar('summarySidebar')"
        *ngIf="currentMenu"
        matTooltip="Close Sidebar"
        matTooltipPosition="right">
        close
      </mat-icon>
    </span>
  </div>
  <div *ngIf="currentMenu === 'summary'">
    <div class="container1">
      <div class="slider-wrapper">
        <button id="prev-slide" class="slide-button" (click)="initSlider(false)">
          <mat-icon class="btn-style">chevron_left</mat-icon>
        </button>
        <div class="icon-section">
          <ng-container *ngFor="let menu of menuIcons; let i = index">
            <ng-container *ngFor="let summary of summary_data">
              <ng-container *ngIf="menu.name === summary.name">
                <div
                  class="icon-container"
                  (click)="activate_tab(menu.name, i, currentMenu, summary.name, summary.kml)">
                  <div class="icon-btn-cont">
                    <button
                      class="icon-button mat-elevation-z3"
                      [style.background]="i === currentIndex ? '#008b8b' : menu.color">
                      <img [src]="menu.img" />
                    </button>
                    <div
                      class="icon-name"
                      [style.width]="menu.name === 'Delamination' ? '60px' : 'fit-content'"
                      [style.word-break]="menu.name === 'Delamination' ? 'break-all' : 'normal'"
                      [style.color]="i === currentIndex2 ? '#008b8b' : '#383838'">
                      {{ menu.name }}
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </div>
        <button id="next-slide" class="slide-button" (click)="initSlider(true)">
          <mat-icon class="btn-style">chevron_right</mat-icon>
        </button>
      </div>
    </div>
    <ng-container *ngFor="let summary of summary_data">
      <div class="details-section" *ngIf="summary.name === namedata">
        <div class="des-card" style="height: auto">
          <div class="details">
            <div class="count" data-toggle="tooltip" title="count">
              {{ summary.name }} - {{ summary.count }}
            </div>
          </div>
          <div class="details">
            <div
              class="mt-3"
              *ngIf="summary.name !== 'Hotspot' && summary.name !== 'Panel Failure'">
              <p>
                <span class="font-weight-bold">Severity -</span>
                {{ severityMapping[formatSummaryName(summary.name)] }}
              </p>
            </div>
            <div class="d-flex align-items-center mt-3 gap-3">
              <b>Description</b>
              <mat-icon (click)="isShown1 = !isShown1">
                {{ isShown1 ? 'visibility' : 'visibility_off' }}
              </mat-icon>
            </div>
            <p *ngIf="isShown1" class="mt-2 mb-0">
              {{ descriptionMap[summary.name] || defaultDescription }}
            </p>
          </div>
        </div>
      </div>
    </ng-container>
    <div class="table-card" *ngIf="defects_summary.length > 0">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
            <th>Count</th>
            <th>Severity</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let map of defects_summary; let i = index"
            (click)="sub_defect_kml(map.name, map.color, 'summary')"
            [style.border]="selectedTable == map.name ? '2px solid ' + map.color : 'lightgray'"
            style="cursor: pointer">
            <td><mat-icon [style.color]="map.color">timelapse</mat-icon></td>
            <td>{{ map.name }}</td>
            <td>{{ map.count }}</td>
            <td>{{ severityMapping[formatSummaryName(map.name)] || '--' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="graph-class" id="chart1">
      <plotly-plot
        *ngIf="graph_pie_defects"
        [data]="graph_pie_defects.data"
        [layout]="graph_pie_defects.layout"
        [config]="graph_pie_defects.config"></plotly-plot>
    </div>
  </div>
  <div *ngIf="currentMenu === 'invertor'">
    <mat-card class="mb-4 text-center font-weight-bold">
      <mat-label class="m-2">Current Inverter</mat-label>
      <mat-select
        class="p-2"
        [(ngModel)]="inverter_value"
        (ngModelChange)="load_invDiv()"
        name="inverter_value">
        <mat-option [value]="inverter.name" *ngFor="let inverter of inverter_names">
          {{ inverter.name }}
        </mat-option>
      </mat-select>
    </mat-card>
    <div class="container1">
      <div class="slider-wrapper">
        <button id="prev-slide" class="slide-button" (click)="initSlider(false)">
          <mat-icon class="btn-style">chevron_left</mat-icon>
        </button>
        <div class="icon-section">
          <ng-container *ngFor="let menu of menuIcons; let i = index">
            <ng-container *ngFor="let inverter of invData">
              <ng-container *ngIf="menu.name === inverter.name">
                <div
                  class="icon-container"
                  (click)="activate_tab(menu.name, i, currentMenu, inverter.name, inverter.name)">
                  <div class="icon-btn-cont">
                    <button
                      class="icon-button mat-elevation-z3"
                      [style.background]="i === currentIndex ? '#008b8b' : menu.color">
                      <img [src]="menu.img" />
                    </button>
                    <div
                      class="icon-name"
                      [style.width]="menu.name === 'Delamination' ? '60px' : 'fit-content'"
                      [style.word-break]="menu.name === 'Delamination' ? 'break-all' : 'normal'"
                      [style.color]="i === currentIndex2 ? '#008b8b' : '#383838'">
                      {{ menu.name }}
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </div>
        <button id="next-slide" class="slide-button" (click)="initSlider(true)">
          <mat-icon class="btn-style">chevron_right</mat-icon>
        </button>
      </div>
    </div>
    <ng-container *ngFor="let summary of invData">
      <div class="details-section" *ngIf="summary.name === namedata">
        <div class="des-card" style="height: auto">
          <div class="details">
            <div class="count" data-toggle="tooltip" title="count">
              {{ summary.name }} - {{ summary.count }}
            </div>
          </div>
          <div class="details">
            <div
              class="mt-3"
              *ngIf="summary.name !== 'Hotspot' && summary.name !== 'Panel Failure'">
              <p>
                <span class="font-weight-bold">Severity -</span>
                {{ severityMapping[formatSummaryName(summary.name)] }}
              </p>
            </div>
            <div class="d-flex align-items-center mt-3 gap-3">
              <b>Description</b>
              <mat-icon (click)="isShown1 = !isShown1">
                {{ isShown1 ? 'visibility' : 'visibility_off' }}
              </mat-icon>
            </div>
            <p *ngIf="isShown1" class="mt-2 mb-0">
              {{ descriptionMap[summary.name] || defaultDescription }}
            </p>
          </div>
        </div>
      </div>
    </ng-container>
    <div class="table-card" *ngIf="inv_tb.length > 0">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
            <th>Count</th>
            <th>Severity</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let item of inv_tb; index as k"
            (click)="sub_defect_kml(item.name, item.color, 'invertor')"
            style="cursor: pointer"
            [style.border]="selectedTable == item.name ? '2px solid ' + item.color : 'lightgray'">
            <td><mat-icon [style.color]="item.color">timelapse</mat-icon></td>
            <td>{{ item.name }}</td>
            <td>{{ item.count }}</td>
            <td>{{ severityMapping[formatSummaryName(item.name)] || '--' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="currentMenu === 'Topography'">
    <div class="table-card ml-2 mr-2" *ngIf="topography_values">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let map of topography_values; let i = index"
            (click)="sub_defect_kml(map.name, map.color, 'Topography')"
            style="cursor: pointer"
            [style.background-color]="selectedRow === map.name ? 'lightcyan' : ''"
            [style.border-bottom]="selectedRow === map.name ? '2px solid darkcyan' : ''"
            (mouseenter)="hoveredRow = map.name"
            (mouseleave)="hoveredRow = null"
            [class.hovered]="hoveredRow == map.name">
            <td><mat-icon [style.color]="map.color">timelapse</mat-icon></td>
            <td>{{ map.name }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="currentMenu === 'Grading'">
    <div class="table-card ml-2 mr-2" *ngIf="grading_values">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let map of grading_values; let i = index"
            (click)="sub_defect_kml(map.name, map.color, 'Grading')"
            style="cursor: pointer"
            [style.background-color]="selectedRow === map.name ? 'lightcyan' : ''"
            [style.border-bottom]="selectedRow === map.name ? '2px solid darkcyan' : ''"
            (mouseenter)="hoveredRow = map.name"
            (mouseleave)="hoveredRow = null"
            [class.hovered]="hoveredRow == map.name">
            <td><mat-icon [style.color]="map.color">timelapse</mat-icon></td>
            <td>{{ map.name }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="currentMenu === 'Vegetation'">
    <div class="table-card ml-2 mr-2" *ngIf="vegetation_values">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let map of vegetation_values; let i = index"
            (click)="sub_defect_kml(map.name, map.color, 'Vegetation')"
            style="cursor: pointer"
            [style.background-color]="selectedRow === map.name ? 'lightcyan' : ''"
            [style.border-bottom]="selectedRow === map.name ? '2px solid darkcyan' : ''"
            (mouseenter)="hoveredRow = map.name"
            (mouseleave)="hoveredRow = null"
            [class.hovered]="hoveredRow == map.name">
            <td><mat-icon [style.color]="map.color">timelapse</mat-icon></td>
            <td>{{ map.name }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="currentMenu === 'Cadastral'">
    <div class="table-card ml-2 mr-2" *ngIf="cadastralData">
      <table class="table table-bordered align-middle w-100">
        <thead>
          <tr>
            <th>#</th>
            <th>Name</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let map of cadastralData; let i = index"
            (click)="sub_defect_kml(map.name, map.color, 'cadastral')"
            style="cursor: pointer"
            [style.background-color]="selectedRow === map.name ? 'lightcyan' : ''"
            [style.border-bottom]="selectedRow === map.name ? '2px solid darkcyan' : ''"
            (mouseenter)="hoveredRow = map.name"
            (mouseleave)="hoveredRow = null"
            [class.hovered]="hoveredRow == map.name">
            <td><mat-icon [style.color]="map.color">timelapse</mat-icon></td>
            <td>{{ map.name }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="currentMenu === 'aoi'">
    <div class="text-center my-3">
      <button mat-mini-fab matTooltip="Add AOI" matTooltipPosition="right" (click)="addDrawtool()">
        <mat-icon>add_circle</mat-icon>
      </button>
    </div>
    <div *ngIf="aoiData && aoiData.length; then thenBlock; else elseBlock"></div>
    <ng-template #thenBlock>
      <div
        *ngFor="let data of aoiData; let i = index"
        class="card mb-3"
        [class.selected]="activeCardIndex === i">
        <div class="card-body">
          <p class="mb-2">
            <span class="font-weight-bold">{{ data.label }}</span>
          </p>
          <ng-container *ngIf="activeCardIndex === i">
            <div style="font-size: 0.8rem" class="mb-2">
              <div>
                <span class="font-weight-bold">Description:</span>
                {{ data.description }}
              </div>
              <div>
                <span class="font-weight-bold">Area:</span>
                {{ data.area }}
              </div>
              <div>
                <span class="font-weight-bold">Created by:</span>
                {{ data.created_by }}
              </div>
              <div>
                <span class="font-weight-bold">Created on:</span>
                {{ data.created_at | date: 'dd-MM-yyyy HH:mm:ss a' }}
              </div>
            </div>
          </ng-container>
          <div class="w-100 d-flex justify-content-around">
            <mat-icon
              class="card-icon"
              matTooltip="Show"
              matTooltipPosition="below"
              (click)="showDrawtool(data, i)">
              remove_red_eye
            </mat-icon>
            <mat-icon
              *ngIf="activeCardIndex === i"
              class="card-icon"
              matTooltip="Edit"
              matTooltipPosition="below"
              (click)="editDrawtool(data, i)">
              edit
            </mat-icon>
            <mat-icon
              class="card-icon"
              matTooltip="Download"
              matTooltipPosition="below"
              (click)="downloadAOI(data, i)">
              download
            </mat-icon>
            <mat-icon
              class="card-icon"
              matTooltip="Delete"
              matTooltipPosition="below"
              (click)="deleteDrawtool(data)">
              delete
            </mat-icon>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template #elseBlock>
      <div
        class="card"
        style="font-size: 18px; text-align: center; padding: 1rem; font-weight: 600">
        Data not found.
        <br />
        <span style="font-size: 14px">To add one, click the "+" button.</span>
      </div>
    </ng-template>
  </div>
</div>
