import { Plotly } from 'angular-plotly.js/lib/plotly.interface';

export interface DefectAnalysis {
  count: number;
  sum: number;
  mean: number;
  std: number;
}

export interface TemperatureAnalysis {
  average_temps: { [key: string]: number };
  max_delta_t: { [key: string]: number };
}

export interface TableAnalysis {
  [id: string]: {
    defect: number;
    power_loss: number;
    avg_temp: number;
  };
}

export interface PowerLossData {
  overall_summary: {
    total_defects: number;
    defect_counts: { [key: string]: number };
    total_power_loss: number;
    average_power_loss: number;
    defect_by_block: { [block: string]: number };
  };
  power_loss_analysis: { [key: string]: DefectAnalysis };
  temperature_analysis: TemperatureAnalysis;
  table_analysis: TableAnalysis;
}

export interface AdvancedChartConfig {
  data: Plotly.Data[];
  layout: Partial<Plotly.Layout>;
  config: Partial<Plotly.Config>;
}
