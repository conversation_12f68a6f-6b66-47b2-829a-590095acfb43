import { SharedModule } from '@/directives/shared.module';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PlotlyModule } from 'angular-plotly.js';
import * as PlotlyJS from 'plotly.js-dist-min';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { CompChartDashboardComponent } from './comp-chart-dashboard/comp-chart-dashboard.component';
import { MapSectionRoutingModule } from './map-section-routing.module';
import { MapSectionComponent } from './map-section.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { AoiDialogComponent } from './sub-components/aoi-dialog/aoi-dialog.component';
import { ComparisionComponent } from './sub-components/comparision/comparision.component';
import { SubdefectsComponent } from './subdefects/subdefects.component';
import { WeatherComponent } from './weather/weather.component';

PlotlyModule.plotlyjs = PlotlyJS;

@NgModule({
  declarations: [
    SidebarComponent,
    MapSectionComponent,
    AoiDialogComponent,
    ComparisionComponent,
    SubdefectsComponent,
    WeatherComponent,
    CompChartDashboardComponent,
  ],
  imports: [
    CommonModule,
    MapSectionRoutingModule,
    MaterialLibrary,
    PlotlyModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    DragDropModule,
  ],
})
export class MapSectionModule {}
