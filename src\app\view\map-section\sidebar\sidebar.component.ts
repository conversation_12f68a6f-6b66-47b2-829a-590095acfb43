import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { Drawtool, DrawtoolC } from '@/interfaces/drawtool';
import { ShareComponent } from '@/view/layout/share/share.component';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ReportsComponent } from '../../layout/reports/reports.component';
import { HttpService } from '../services-map/http.service';
import { ComparisionComponent } from '../sub-components/comparision/comparision.component';

@Component({
  selector: 'sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnChanges {
  aoiData: Drawtool[] = null;
  project_id: number;
  drawtool: Drawtool = new DrawtoolC();
  inverter_value: any;
  currentMenu: string;
  previousID: string;
  p = 1;
  isShown1 = false;
  datesumlength: any;
  datevalue: any;
  dashboard_visible: boolean = false;
  Project_layer_inverter_data: any;
  Project_layer: any;
  Project_layer_summary: any;
  defects_summary = [];
  summary_data = [];
  summ: any;
  invData = [];
  inv_all: any;
  inv_tb = [];
  Summary_tab: any;
  sub_defect = [];
  sub_label = [];
  kml_file_location: any;
  inverterMap = new Map();
  menuIcons = [
    {
      name: 'PID',
      img: './assets/images/pid.svg',
      color: '#DC2828',
    },
    {
      name: 'Hotspot',
      img: './assets/images/hotspot.svg',
      color: '#089E60',
    },
    {
      name: 'Open Circuit',
      img: './assets/images/open circuit.svg',
      color: '#FFC107',
    },
    {
      name: 'Panel Failure',
      img: './assets/images/panel failure.svg',
      color: '#D56DFC',
      rippleColor: '#007bff45',
    },
    {
      name: 'Short Circuit',
      img: './assets/images/short circuit.svg',
      color: '#1396CC',
    },
    {
      name: 'Diode Issue',
      img: './assets/images/open circuit.svg',
      color: '#FFC107',
    },
    {
      name: 'Missing Module',
      img: './assets/images/Missing_Module.svg',
      color: '#800080',
    },
    {
      name: 'Broken Glass',
      img: './assets/images/broken_glass.svg',
      color: '#FF4500',
    },
    {
      name: 'Delamination',
      img: './assets/images/Delimination.svg',
      color: '#00008B',
    },
  ];
  descriptionMap: { [key: string]: string } = {
    Hotspot: 'Power dissipation occurring in a small area results in cell overheating',
    'Short Circuit':
      'One or more substring open circuit failure with hotspot. At one or more substrings, easily mistaken for cell breakage or cell defects, Potential Induced Degradation (PID), or mismatch',
    'Open Circuit': 'Loss of connection within module junction box or cell connecter',
    'Diode Issue': 'Loss of connection within module junction box or cell connecter',
    PID: 'The full panel surface is homogeneously heated up compared to other panels. It may happen due to PID effects.',
  };
  defaultDescription: string =
    'Frames of the modules are homogeneously heated. The negative grounding should be checked at the inverter level. The module frames would have high leakage current.';
  severityMapping = {
    Hotspot: 'High',
    Bypass_Diode_Issues: 'Severe',
    Junction_Box_Issue: 'Severe',
    Dirt: 'Low',
    Multicell_Hotspot: 'High',
    Single_Cell_Hotspot: 'Severe',
    Short_Circuit: 'Medium',
    Open_Circuit: 'High',
    Diode_Issue: 'Severe',
    Panel_Failure: 'High',
    Open_String_Tables: 'Severe',
    String_Failure: 'Severe',
    PID: 'High',
    Missing_Module: 'High',
    Broken_Glass: 'High',
    Delamination: 'Medium',
    // For old projects
    Module: 'High',
    Table: 'Severe',
    String: 'Severe',
  };
  category: string = null;
  currentIndex = 0;
  isOpenCompare: boolean;
  dialogRef: any;
  namedata: string;
  slider_state = false;
  colors_tab = [];
  Completed_date_array = [];
  inverter_names = [];
  inverter_names2 = [];
  topograpyData: any = '';
  topography_title: any;
  topography_values: any;
  grading_values: any;
  topography_data: any;
  vegetation_values: any;
  isVisible = false;
  sidebar_logo = '';
  closesidebar: any;
  splitted_sidebar_value: any;
  grading_title: any;
  grading_data: any;
  vegetation_data: any;
  usersWithAccess: [] = [];
  usersList: [] = [];
  tab_name: string;
  Cadastral_data: any;
  compare_icon = false;
  graph_pie_defects: any = null;
  selectedRow: string | null = null;
  hoveredRow: string | null = null;
  selectedTable: string = '';
  currentIndex2: number = 0;
  cadastralData: any;
  cadastral_present: boolean = false;
  @Output() compare_slider_event = new EventEmitter<boolean>();
  current_summary_state: any = 'PID';
  @Output() current_summary_state_event: EventEmitter<any> = new EventEmitter<any>();
  inverter_page: any;
  @Output() inverter_page_event: EventEmitter<any> = new EventEmitter<any>();
  cadastrial_map_page: any;
  @Output() cadastrial_map_page_event: EventEmitter<any> = new EventEmitter<any>();
  removing_kml: any = 'remove';
  @Output() removing_kml_event: EventEmitter<any> = new EventEmitter<any>();
  subdefects_page: any = 'visible';
  @Output() subdefects_page_event: EventEmitter<any> = new EventEmitter<any>();
  @Output() close_stack: EventEmitter<any> = new EventEmitter<any>();
  @Input() booleanValueFromParent: boolean;
  @Output() aoi_action_event = new EventEmitter<any>();
  activeCardIndex: number | null = null;

  constructor(
    private router: Router,
    private _http: HttpService,
    public dialog: MatDialog,
    private userService: UserService,
    private toastr: ToastrService,
    private sharedDataService: SharedDataService,
    private apiConfigService: ApiConfigService
  ) {}

  ngOnInit(): void {
    this.userService.sidebarLogo$.subscribe((logo: string) => {
      this.sidebar_logo = logo;
    });
    const role = this.userService.getRole();
    const group_id = this.userService.getGroupId();

    if (role === 'admin' && group_id) {
      this.sharedDataService
        .fetchData(this.apiConfigService.getManageUsers(group_id), 'manage_users')
        .subscribe(data => {
          if (data) {
            this.usersList = data;
          }
        });
    }

    this._http.getclosesidebar().subscribe(
      info => {
        this.closesidebar = info;
        this.splitted_sidebar_value = this.closesidebar.close_side_bar.split('/', 2);

        if (this.splitted_sidebar_value[1] == 'True') {
          this.closeSidebar(this.splitted_sidebar_value[0]);
        }
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
    this.apiConfigService.getProjectData().subscribe(data => {
      this.inv_all = data['data'];
      data['data']['dates'] = Object.keys(data['data']['date_status']);
      const data_key = Object.keys(data['data']['date_status']);
      const data_value = Object.values(data['data']['date_status']);
      for (let k = 0; k < data_key.length; k++) {
        if (data_value[k] == 'completed') {
          this.Completed_date_array.push(data_key[k]);
        }
      }
      this.datesumlength = this.Completed_date_array.length - 1;
      this.datevalue = localStorage.getItem('date');
      const obj_date_value = data['data']['processed_data'][this.datevalue];
      this.project_id = data['data'].id;
      this.kml_file_location = obj_date_value['kml_file_location'];
      this.usersWithAccess = data['data']['users'];
      localStorage.setItem('report_path', JSON.stringify(obj_date_value['report_path']));
      this.Project_layer_summary = Object.keys(obj_date_value['summary_layers']);
      this.Project_layer_inverter_data = Object.keys(obj_date_value['inverter_layers']);
      this.category = data['data']['category'].toLocaleLowerCase();
      if (this.category == 'thermography' || this.category == 'topography') {
        this.compare_icon = true;
      }
      this.topography_data = Object.keys(obj_date_value['topography_layers']);
      if (
        obj_date_value['topography_layers']['Cadastral Map'] &&
        Object.keys(obj_date_value['topography_layers']['Cadastral Map']).length > 0
      ) {
        this.Cadastral_data = Object.keys(obj_date_value['topography_layers']['Cadastral Map']);
        this.cadastral_present = true;
      }
      this.grading_data = Object.keys(obj_date_value['grading_layers']);
      if (obj_date_value['vegetation_layers']) {
        this.vegetation_data = Object.keys(obj_date_value['vegetation_layers']);
      }
      for (let j = 0; j < this.Project_layer_inverter_data.length; j++) {
        const x = j + 1;
        this.inverter_names.push({
          name: this.Project_layer_inverter_data[j],
          pvalue: x,
        });
      }
      this.inverterNameMap(this.inverter_names);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['booleanValueFromParent'] &&
      changes['booleanValueFromParent'].currentValue === true
    ) {
      this.datevalue = localStorage.getItem('date');
      const obj_date_value = this.inv_all['data']['processed_data'][this.datevalue];
      this.Project_layer_inverter_data = Object.keys(obj_date_value['inverter_layers']);
      this.Project_layer = Object.values(
        this.inv_all['processed_data'][this.datevalue]['inverter_layers']
      );
      this.inverterNameMap(this.inverter_names);
    }
  }
  inverterNameMap(inverter_names) {
    for (let i = 0; i < inverter_names.length; i++) {
      this.LoadGBKml(parseInt(this.inverter_names[i]['pvalue']));
    }
  }
  formatSummaryName(name: string): string {
    return name.replace(/\s+/g, '_');
  }
  activateMenu(id: string) {
    if (
      this.previousID != undefined &&
      (this.Summary_tab == true || this.Summary_tab == undefined)
    ) {
      const prevId = document.getElementById(this.previousID);
      prevId.style.width = '0px';
    } else {
      const e = document.getElementById(id);
      e.style.width = '5px';
      this.previousID = id;
    }
    if (id == 'compare') {
      this.close_stack.emit();
    }
  }
  initSlider(isNext) {
    const iconsList = document.querySelector('.slider-wrapper .icon-section');
    const direction = isNext ? 1 : -1;
    const scrollAmount = iconsList.clientWidth * direction;
    iconsList.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }
  // Function for Opening sidebar
  openSidbar(id: string, menuId: string) {
    const sideBar = document.getElementById(id);
    sideBar.style.display = 'block';
    sideBar.style.width = '325px';
    sideBar.style['z-index'] = '1000';
    // Function to update sidebar styles based on window width
    const updateSidebarStyles = () => {
      if (window.innerWidth < 700) {
        sideBar.style.width = '250px';
        sideBar.style.zIndex = '1000';
      } else {
        sideBar.style.width = '325px';
        sideBar.style.zIndex = '1000';
      }
    };
    updateSidebarStyles();
    window.addEventListener('resize', updateSidebarStyles);
    this.namedata = '';
    switch (menuId) {
      case 'summary':
        this.summary_data = [];
        this.currentMenu = 'summary';
        this.summary_data_render();
        break;
      case 'invertor':
        this.invData = [];
        this.currentMenu = 'invertor';
        this.inverter_data_render();
        break;
      case 'Topography':
        this.topograpyData = [];
        this.currentMenu = 'Topography';
        this.Topography_data_render();
        break;
      case 'Grading':
        this.topograpyData = [];
        this.currentMenu = 'Grading';
        this.Grading_data_render();
        break;
      case 'Cadastral':
        this.currentMenu = menuId;
        this.cadastral_data_render();
        break;
      case 'Vegetation':
        this.topograpyData = [];
        this.currentMenu = 'Vegetation';
        this.Vegetation_data_render();
        break;
      case 'aoi':
        this.currentMenu = 'aoi';
        this.getAoiData();
        break;
      default:
        break;
    }
  }
  openComparision2() {
    this.router.navigate(['map', 'compare-map']);
    this.dashboard_visible = true;
    // this.closeSidebar('summarySidebar');
  }
  // Function for closing sidebar
  closeSidebar(id: string) {
    this.selectedRow = null;
    this.selectedTable = null;
    this.activeCardIndex = null;
    const sideBar = document.getElementById(id);
    sideBar.style.display = 'none';
    sideBar.style.width = '0px';
    this.removing_kml_event.emit('chevron_left');
    if (this.Summary_tab == true) {
      this.activateMenu(this.previousID);
      this.Summary_tab = false;
    }
    this.aoi_action_event.emit({ action: 'remove', data: {} });
  }

  activate_tab(name: string, i: number, currentMenu: string, defect_name, kml) {
    localStorage.setItem('current_tab', defect_name);
    this.namedata = defect_name;
    this.currentIndex = i;
    this.currentIndex2 = i;
    this.selectedTable = null;
    const kml_color = [];
    if (currentMenu == 'summary') {
      this.loadSumm_data(defect_name);
      const summary_layers = this.summ['processed_data'][this.datevalue]['summary_layers'][name];
      const sub_group_detail = summary_layers['sub_group'];
      if (Object.keys(sub_group_detail).length == 0) {
        kml_color.push(summary_layers['color']);
      } else {
        const sub_group_lables = summary_layers['sub_group'];
        for (const n in sub_group_lables) {
          kml_color.push(sub_group_lables[n]['color']);
        }
      }

      this.current_summary_state = {
        tab: summary_layers['kml'],
        menu: currentMenu,
        color: kml_color,
        pageno: this.p,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
    }
    if (currentMenu == 'invertor') {
      this.inv_table(this.inverter_value, kml);
      const inverter_layers =
        this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value][
          name
        ];
      if (Object.keys(inverter_layers['sub_group']).length == 0) {
        kml_color.push(inverter_layers['color']);
      } else {
        const sub_group_lables_inv = inverter_layers['sub_group'];
        for (const t in sub_group_lables_inv) {
          kml_color.push(sub_group_lables_inv[t]['color']);
        }
      }
      this.current_summary_state = {
        tab: inverter_layers['kml'],
        menu: currentMenu,
        color: kml_color,
        pageno: this.p,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
    }
  }
  // Function for loading kml from sub group table in summary,inverter,cadastrial side bar.
  sub_defect_kml(n, color, type) {
    localStorage.setItem('sub_defects', n);
    if (type == 'summary') {
      this.selectedTable = n;
      const sub_group_kml =
        this.summ['processed_data'][this.datevalue]['summary_layers'][this.namedata]['sub_group'][
          n
        ]['kml'];
      this.current_summary_state = {
        tab: sub_group_kml,
        menu: 'summary_sub_details',
        color: sub_group_kml['color'],
        pageno: this.p,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
    }
    if (type == 'invertor') {
      this.selectedTable = n;
      const sub_group_kml_inverter =
        this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value][
          this.namedata
        ]['sub_group'][n]['kml'];
      this.current_summary_state = {
        tab: sub_group_kml_inverter,
        menu: 'inverter_sub_details',
        color: sub_group_kml_inverter['color'],
        pageno: this.p,
      };
      this.current_summary_state_event.emit(this.current_summary_state);
    }
    if (type == 'Topography') {
      this.selectedRow = n;
      this.cadastrial_map_page = {
        menu: 'cadastral_map',
        tab: n,
        color: color,
        pageno: this.p,
      };
      this.cadastrial_map_page_event.emit(this.cadastrial_map_page);
    }
    if (type == 'Grading') {
      this.selectedRow = n;
      this.cadastrial_map_page = {
        tab: n,
        menu: 'Grading',
        color: color,
        pageno: this.p,
      };
      this.cadastrial_map_page_event.emit(this.cadastrial_map_page);
    }
    if (type == 'Vegetation') {
      this.selectedRow = n;
      this.cadastrial_map_page = { tab: n, menu: 'Vegetation', color: color, pageno: this.p };
      this.cadastrial_map_page_event.emit(this.cadastrial_map_page);
    }
    if (type == 'cadastral') {
      this.selectedRow = n;
      this.cadastrial_map_page = {
        menu: 'cadastral',
        tab: n,
        color: color,
        pageno: this.p,
      };
      this.cadastrial_map_page_event.emit(this.cadastrial_map_page);
    }
  }
  summary_data_render() {
    this.removing_kml_event.emit(this.removing_kml);
    localStorage.setItem('current_tab', 'PID');
    if (this.Summary_tab == true) {
      if (this.previousID == 'invertor') {
        this.Summary_tab = false;
        this.activateMenu('summary');
        return this.summary_data_render();
      } else if (this.previousID == 'Topography') {
        this.Summary_tab = false;
        this.activateMenu('summary');
        return this.summary_data_render();
      }
      this.closeSidebar('summarySidebar');
      return;
    }
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.summ = data['data'];
        this.datevalue = localStorage.getItem('date');
        const summary_layer = this.summ['processed_data'][this.datevalue]['summary_layers'];
        this.Project_layer_summary = Object.keys(summary_layer);
        for (const k in summary_layer) {
          this.summary_data.push({
            name: k,
            kml: summary_layer[k]['kml'],
            color: summary_layer[k]['color'],
            count: summary_layer[k]['Count'],
          });
        }
        this.activate_tab(
          this.summary_data[0].name,
          0,
          'summary',
          this.summary_data[0].name,
          this.summary_data[0].kml
        );
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  loadSumm_data(elm) {
    this.removing_kml_event.emit(this.removing_kml);
    this.subdefects_page_event.emit('visibility_off');
    const layer_data =
      this.summ['processed_data'][this.datevalue]['summary_layers'][elm]['sub_group'];
    if (elm === 'Hotspot' || elm === 'Panel Failure') {
      this.sub_defect.length = 0;
      this.sub_label.length = 0;
    }
    this.defects_summary = [];
    this.sub_defect = [];
    this.sub_label = [];
    this.colors_tab = [];

    for (const key in layer_data) {
      this.defects_summary.push({
        name: key,
        count: layer_data[key]['Count'],
        kml: layer_data[key]['kml'],
        color: layer_data[key]['color'],
      });
      this.sub_label.push(key);
      this.sub_defect.push(layer_data[key]['Count']);
      this.colors_tab.push(layer_data[key]['color']);
    }
    if (this.sub_defect.length === 0) {
      document.getElementById('chart1').style.display = 'none';
      this.sub_defect = [0, 0, 0, 0, 0];
      this.sub_label = ['No data', 'No data', 'No data', 'No data', 'No data'];
    } else {
      document.getElementById('chart1').style.display = 'flex';
    }
    this.graph_pie_defects = {
      data: [
        {
          values: this.sub_defect,
          labels: this.sub_label,
          type: 'pie',
          showlegend: false,
          hoverinfo: 'label+percent',
          textinfo: 'label+percent',
          textposition: 'inside',
          hole: 0.6,
          marker: {
            colors: this.colors_tab,
          },
        },
      ],
      layout: { margin: { r: 20, t: 20, l: 20, b: 20 }, height: 200, width: 200 },
      config: {
        responsive: true,
        annotations: [
          {
            showarrow: false,
            x: 0.17,
            y: 0.5,
          },
        ],
        displayModeBar: true,
        displaylogo: false,
      },
    };
    if (this.isOpenCompare) {
      this.openComparision('compare');
    }
    this.Summary_tab = true;
  }
  LoadGBKml(value: any) {
    let url;
    if (value <= 9 && value >= 1) {
      url = this.kml_file_location + 'INVERTER0' + value + '/gb.kml';
    } else {
      url = this.kml_file_location + 'INVERTER' + value + '/gb.kml';
    }
    fetch(url)
      .then(res => res.text())
      .then(kmltext => {
        const parser = new DOMParser();
        const kml = parser.parseFromString(kmltext, 'text/xml');
        const description = kml.querySelector('Placemark description').textContent;
        const inverterName = description.split('Inverter No: ')[1].trim();
        this.inverterMap.set(inverterName, value);
      });
  }
  inverter_data_render() {
    localStorage.setItem('current_tab', 'PID');
    this.removing_kml_event.emit(this.removing_kml);
    if (this.Summary_tab == true) {
      if (this.previousID == 'summary' || this.previousID == 'Topography') {
        this.Summary_tab = false;
        this.activateMenu('invertor');
        return this.inverter_data_render();
      }
      this.Summary_tab = false;
      this.previousID = 'invertor';
      this.closeSidebar('summarySidebar');
      return;
    }
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.inv_all = data['data'];
        this.datevalue = localStorage.getItem('date');
        const obj_date_value = data['data']['processed_data'][this.datevalue];
        this.Project_layer_inverter_data = Object.keys(obj_date_value['inverter_layers']);
        this.Project_layer = Object.values(
          this.inv_all['processed_data'][this.datevalue]['inverter_layers']
        );
        this.inverter_names = [];
        for (let j = 0; j < this.Project_layer_inverter_data.length; j++) {
          const x = j + 1;
          this.inverter_names.push({
            name: this.Project_layer_inverter_data[j],
            pvalue: x,
          });
        }
        this.inverter_value = this.inverter_names[0].name;
        this.load_invDiv();
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  load_invDiv() {
    this.invData = [];
    for (let j = 0; j < this.inverter_names.length; j++) {
      if (this.inverter_value == this.inverter_names[j]['name']) {
        if (this.inverterMap.get(this.inverter_value) != undefined)
          this.p = this.inverterMap.get(this.inverter_value);
        else this.p = parseInt(this.inverter_names[j]['pvalue']);
      }
    }

    this.removing_kml_event.emit(this.removing_kml);
    this.inverter_page_event.emit(this.p);
    this.subdefects_page_event.emit('visibility_off');
    const obj =
      this.inv_all['processed_data'][this.datevalue]['inverter_layers'][this.inverter_value];
    for (const k in obj) {
      this.invData.push({
        inv_name: this.inverter_value,
        name: k,
        count: obj[k]['count'],
        kml: obj[k]['kml'],
        color: obj[k]['color'],
      });
    }
    this.inv_table(this.inverter_value, this.namedata);
  }
  inv_table(ele, elm) {
    this.inv_tb = [];
    const obj =
      this.inv_all['processed_data'][this.datevalue]['inverter_layers'][ele][elm]['sub_group'];
    for (const key in obj) {
      this.inv_tb.push({
        name: key,
        count: obj[key]['count'],
        kml: obj[key]['kml'],
        color: obj[key]['color'],
      });
    }
    if (this.isOpenCompare) {
      this.openComparision('compare');
    }
    this.Summary_tab = true;
  }
  Topography_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (this.Summary_tab == true) {
      if (this.previousID == 'summary') {
        this.Summary_tab = false;
        this.activateMenu('Topography');
        return this.Topography_data_render();
      } else if (this.previousID == 'invertor') {
        this.Summary_tab = false;
        this.activateMenu('Topography');
        return this.Topography_data_render();
      }
      this.Summary_tab = false;
      this.previousID = 'invertor';
      this.closeSidebar('summarySidebar');
      return;
    }
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.topography_values = [];
        this.summ = data['data'];
        this.datevalue = localStorage.getItem('date');
        this.Project_layer = this.summ['processed_data'][this.datevalue]['topography_layers'];
        this.topography_title = Object.keys(this.Project_layer);
        const topographyLayers = this.summ['processed_data'][this.datevalue]['topography_layers'];

        let sub_feature;

        if (
          'TopographicFeatures' in topographyLayers &&
          'sub_feature' in topographyLayers['TopographicFeatures']
        ) {
          sub_feature = topographyLayers['TopographicFeatures']['sub_feature'];
        } else if (
          'Topography' in topographyLayers &&
          'sub_feature' in topographyLayers['Topography']
        ) {
          sub_feature = topographyLayers['Topography']['sub_feature'];
        } else {
          console.error('Sub-feature not found in JSON data');
        }

        for (const featureName in sub_feature) {
          const feature = sub_feature[featureName];
          this.topography_values.push({
            name: featureName,
            kml: feature.kml,
            color: feature.color,
          });
        }
        const featureNames = Object.keys(sub_feature);
        const firstFeatureName = featureNames[0];
        this.current_summary_state = {
          tab: firstFeatureName,
          menu: 'cadastral_map',
          color: sub_feature[firstFeatureName].color,
          pageno: 1,
        };
        this.current_summary_state_event.emit(this.current_summary_state);
        if (this.isOpenCompare) {
          this.openComparision('compare');
        }
        this.Summary_tab = true;
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  Grading_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (localStorage.getItem('shareComponent') == 'open') {
      this.Summary_tab = false;
      this.dialog.closeAll();
      this.activateMenu('Grading');
    }
    if (this.Summary_tab == true) {
      if (this.previousID == 'summary') {
        this.Summary_tab = false;
        this.activateMenu('Grading');
        return this.Grading_data_render();
      } else if (this.previousID == 'invertor') {
        this.Summary_tab = false;
        this.activateMenu('Grading');
        return this.Grading_data_render();
      }
      this.Summary_tab = false;
      this.previousID = 'invertor';
      this.closeSidebar('summarySidebar');
      return;
    }
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.grading_values = [];
        this.summ = data['data'];
        this.datevalue = localStorage.getItem('date');
        this.Project_layer = this.summ['processed_data'][this.datevalue]['grading_layers'];
        this.grading_title = Object.keys(
          this.summ['processed_data'][this.datevalue]['grading_layers']
        );
        const sub_feature =
          this.summ['processed_data'][this.datevalue]['grading_layers']['GradingFeatures'][
            'sub_feature'
          ];

        for (const featureName in sub_feature) {
          const feature = sub_feature[featureName];
          this.grading_values.push({
            name: featureName,
            kml: feature.kml,
            color: feature.color,
          });
        }

        const featureNames = Object.keys(sub_feature);
        const firstFeatureName = featureNames[0];

        this.current_summary_state = {
          tab: firstFeatureName,
          menu: 'Grading',
          color: sub_feature[firstFeatureName].color,
          pageno: 1,
        };

        this.current_summary_state_event.emit(this.current_summary_state);
        if (this.isOpenCompare) {
          this.openComparision('compare');
        }
        this.Summary_tab = true;
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  cadastral_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (this.Summary_tab == true) {
      if (this.previousID == 'summary') {
        this.Summary_tab = false;
        this.activateMenu('Cadastral');
        return this.cadastral_data_render();
      } else if (this.previousID == 'invertor') {
        this.Summary_tab = false;
        this.activateMenu('Topography');
        return this.cadastral_data_render();
      }
      this.Summary_tab = false;
      this.previousID = 'invertor';
      this.closeSidebar('summarySidebar');
      return;
    }
    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.cadastralData = [];
        this.summ = data['data'];
        this.datevalue = localStorage.getItem('date');
        try {
          const sub_feature =
            this.summ['processed_data'][this.datevalue]['topography_layers']['Cadastral Map'][
              'sub_feature'
            ];
          // Rest of your code that uses sub_feature
          for (const featureName in sub_feature) {
            const feature = sub_feature[featureName];
            this.cadastralData.push({
              name: featureName,
              kml: feature.kml,
              color: feature.color,
            });
          }

          const featureNames = Object.keys(sub_feature);
          const firstFeatureName = featureNames[0];

          this.current_summary_state = {
            tab: firstFeatureName,
            menu: 'Cadastral',
            color: sub_feature[firstFeatureName].color,
            pageno: 1,
          };

          this.current_summary_state_event.emit(this.current_summary_state);
          if (this.isOpenCompare) {
            this.openComparision('compare');
          }
          this.Summary_tab = true;
        } catch (error) {
          console.error('Error accessing sub_feature:', error.message);
        }
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  Vegetation_data_render() {
    this.subdefects_page_event.emit('visibility_off');
    if (localStorage.getItem('shareComponent') == 'open') {
      this.Summary_tab = false;
      this.dialog.closeAll();
      this.activateMenu('Vegetation');
    }
    if (this.Summary_tab == true) {
      if (this.previousID == 'summary') {
        this.Summary_tab = false;
        this.activateMenu('Vegetation');
        return this.Vegetation_data_render();
      } else if (this.previousID == 'invertor') {
        this.Summary_tab = false;
        this.activateMenu('Vegetation');
        return this.Vegetation_data_render();
      }
      this.Summary_tab = false;
      this.previousID = 'invertor';
      this.closeSidebar('summarySidebar');
      return;
    }

    this.apiConfigService.getProjectData().subscribe(
      data => {
        this.vegetation_values = [];
        this.summ = data['data'];

        this.datevalue = localStorage.getItem('date');
        this.Project_layer = this.summ['processed_data'][this.datevalue]['vegetation_layers'];
        const sub_feature =
          this.summ['processed_data'][this.datevalue]['vegetation_layers']['Vegetation Features'][
            'sub_feature'
          ];

        for (const featureName in sub_feature) {
          const feature = sub_feature[featureName];
          this.vegetation_values.push({
            name: featureName,
            kml: feature.kml,
            color: feature.color,
          });
        }

        const featureNames = Object.keys(sub_feature);
        const firstFeatureName = featureNames[0];

        this.current_summary_state = {
          tab: firstFeatureName,
          menu: 'Vegetation',
          color: sub_feature[firstFeatureName].color,
          pageno: 1,
        };

        this.current_summary_state_event.emit(this.current_summary_state);
        if (this.isOpenCompare) {
          this.openComparision('compare');
        }
        this.Summary_tab = true;
      },
      err => {
        this.toastr.error('', 'Error Occured');
        console.error(err);
      }
    );
  }
  openComparision(id: string) {
    const e = document.getElementById(id);
    if (this.isOpenCompare) {
      this.dialogRef.close();
      this.isOpenCompare = false;
      e.style.width = '0px';
    } else {
      this.isOpenCompare = true;
      this.dialogRef = this.dialog.open(ComparisionComponent, {
        height: 'calc(100%)',
        width: 'calc(100%)',
        maxWidth: '100%',
        maxHeight: '100%',
      });
      e.style.width = '5px';
      this.closeSidebar('summarySidebar');
    }
    this.dialogRef.afterClosed().subscribe(() => {
      this.dialog.closeAll();
      e.style.width = '0px';
    });
  }
  openSharedialog() {
    const dialogRef = this.dialog.open(ShareComponent, {
      data: {
        projectName: localStorage.getItem('proj_name'),
        projectId: localStorage.getItem('project_id'),
        usersWithAccess: this.usersWithAccess,
        allUsers: this.usersList,
      },
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ngOnInit();
      }
    });
  }
  openReportdialog() {
    const project = localStorage.getItem('proj_name');
    const date = localStorage.getItem('date');
    const reports = localStorage.getItem('report_path');
    if (project && date && reports) {
      this.dialog.open(ReportsComponent, {
        data: {
          projectName: project,
          currentDate: date,
          fileObj: JSON.parse(reports),
        },
      });
    }
  }
  getAoiData() {
    this.apiConfigService.getDrawtools(this.project_id, this.datevalue).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.aoiData = res.data;
        }
      },
      err => {
        this.toastr.error(err.error.message);
      }
    );
  }
  createDrawtool(drawtool) {
    this.apiConfigService.createDrawtool(drawtool).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.toastr.success('', 'AOI created successfully');
          this.getAoiData();
          this.aoi_action_event.emit({ action: 'show', data: drawtool });
        }
      },
      err => {
        this.toastr.error(err.error.message);
      }
    );
  }
  updateDrawtool(drawtool) {
    this.apiConfigService.updateDrawtool(drawtool).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.toastr.success('', 'AOI updated successfully');
          this.aoi_action_event.emit({ action: 'show', data: drawtool });
        }
      },
      err => {
        this.toastr.error(err.error.message);
      }
    );
  }
  deleteDrawtool(drawtool) {
    this.activeCardIndex = null;
    this.apiConfigService.deleteDrawtool(drawtool.id).subscribe(
      (res: any) => {
        if (res.status == 'success') {
          this.aoi_action_event.emit({ action: 'remove', data: drawtool });
          this.toastr.success('', 'AOI updated successfully');
          this.getAoiData();
        }
      },
      err => {
        this.toastr.error(err.error.message);
      }
    );
  }
  selectCard(index: number): void {
    this.activeCardIndex = index;
  }
  showDrawtool(drawtool, i) {
    this.selectCard(i);
    this.aoi_action_event.emit({ action: 'show', data: drawtool });
  }
  editDrawtool(drawtool, i) {
    this.selectCard(i);
    this.aoi_action_event.emit({ action: 'edit', data: drawtool });
  }
  addDrawtool() {
    this.activeCardIndex = null;
    this.aoi_action_event.emit({ action: 'add', data: {} });
  }
  downloadAOI(drawtool, i) {
    this.selectCard(i);
    const label = drawtool.label.trim();
    const polygon = Array.isArray(drawtool.polygon) ? drawtool.polygon : [drawtool.polygon];
    const coordinates = polygon.map(point => `${point.lng},${point.lat}`).join('\n');

    const kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <Placemark>
      <name>${label}</name>
      <description>${drawtool.description}</description>
      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>
              ${coordinates}
            </coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
    </Placemark>
  </Document>
</kml>`;

    const blob = new Blob([kml], { type: 'application/vnd.google-earth.kml+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${label}.kml`;
    a.click();
    if (a.parentNode) {
      a.parentNode.removeChild(a);
    }
    URL.revokeObjectURL(url);
  }
}
