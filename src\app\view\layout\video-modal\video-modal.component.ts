import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-video-modal',
  templateUrl: './video-modal.component.html',
  styleUrl: './video-modal.component.css',
})
export class VideoModalComponent implements OnInit {
  @ViewChild('videoPlayer', { static: false }) videoPlayer!: ElementRef<HTMLVideoElement>;
  isPlaying = false;
  currentTime = 0;
  videoDuration = 0;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<VideoModalComponent>
  ) {}

  ngOnInit(): void {
    this.togglePlayPause();
  }
  togglePlayPause() {
    if (this.videoPlayer) {
      const video: HTMLVideoElement = this.videoPlayer.nativeElement;
      if (this.isPlaying) {
        video.pause();
      } else {
        video.play();
      }
      this.isPlaying = !this.isPlaying;
    }
  }
  updateTime() {
    if (this.videoPlayer) this.currentTime = this.videoPlayer.nativeElement.currentTime;
  }
  seekVideo() {
    if (this.videoPlayer) this.videoPlayer.nativeElement.currentTime = this.currentTime;
  }
  setVideoDuration() {
    if (this.videoPlayer) this.videoDuration = this.videoPlayer.nativeElement.duration;
  }
  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
  }
  onClose() {
    this.dialogRef.close();
  }
}
