"use strict";<PERSON><PERSON>Layer.Canvas=<PERSON><PERSON>TileLayer.extend({_delays:{},_delaysForZoom:null,createCanvas:function(e,t,i){var s,o=e.getContext("2d"),a=this.options.doubleSize,l=this.getTileSize(),r=l.x,n=l.y;e.width=a?2*r:r,e.height=a?2*n:n;var h=new Image;h.onerror=this._tileOnError.bind(this,i,h),h.onload=function(){try{o.drawImage(h,0,0),e.complete=!0}catch(e){s=e}finally{i(s,e)}};var c=this._getZoomForUrl();h.src=isNaN(c)?"":this.getTileUrl(t),h.crossOrigin="anonymous";var _=this._tileCoordsToKey(t);this._tilesImages[_]=h},createTile:function(e,t){var i=this,s=this.options.timeout,o=e.z,a=document.createElement("canvas");return s?(o!==this._delaysForZoom&&(this._clearDelaysForZoom(),this._delaysForZoom=o),this._delays[o]||(this._delays[o]=[]),this._delays[o].push(setTimeout((function(){i.createCanvas(a,e,t)}),s))):this.createCanvas(a,e,t),a},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._tilesImages={},this._resetView(),this._update()},_clearDelaysForZoom:function(){var e=this._delaysForZoom,t=this._delays[e];t&&(t.forEach((function(e,i){clearTimeout(e),delete t[i]})),delete this._delays[e])},_abortLoading:function(){var e,t,i;for(e in this._tiles)this._tiles[e].coords.z!==this._tileZoom&&(i=this._tilesImages[e]||{},t=this._tiles[e].el,i.onload=L.Util.falseFn,i.onerror=L.Util.falseFn,t.complete||(i.src=L.Util.emptyImageUrl,L.DomUtil.remove(i),delete this._tilesImages[e],delete this._tiles[e]))}}),L.tileLayer.canvas=function(e,t){return new L.TileLayer.Canvas(e,t)};