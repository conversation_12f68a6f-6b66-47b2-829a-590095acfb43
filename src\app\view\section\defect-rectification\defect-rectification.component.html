<div class="container-fluid">
  <h1 class="text-center font-weight-bold">
    {{ projectInfo.project_name }} ({{ projectInfo.date }})
  </h1>
  <mat-tab-group class="m-0 p-0">
    <mat-tab label="Defect" [disabled]="isUploading">
      <div class="filter-options row col-12">
        <div class="actions col-4 p-0">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Select Columns</mat-label>
            <mat-select
              [value]="displayedColumns"
              (selectionChange)="onSelectionChange($event)"
              multiple>
              <mat-option *ngFor="let column of allColumns" [value]="column">
                {{ column }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-icon
            class="icon-fix-description"
            matTooltip="Reset Filters"
            (click)="resetFilters()">
            refresh
          </mat-icon>
        </div>
        <div class="actions col-4 p-0">
          <mat-icon
            (click)="exportToExcel()"
            [class.disabled]="!dataSource || dataSource.filteredData.length === 0"
            class="icon-fix-download"
            matTooltip="Download File">
            download
          </mat-icon>
          <mat-icon
            class="icon-fix-description"
            matTooltip="Check Layout"
            (click)="downloadLayoutFile()">
            description
          </mat-icon>
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Search</mat-label>
            <input matInput [formControl]="searchControl" placeholder="Search.." />
            <button
              *ngIf="searchControl.value"
              matSuffix
              mat-icon-button
              aria-label="Clear"
              (click)="clearSearch()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>
      </div>
      <div class="filter-options">
        <mat-form-field class="dropdown-fix">
          <mat-label>Select Defect</mat-label>
          <mat-select [(ngModel)]="selectedDefect" (selectionChange)="applyFilter()">
            <mat-select-trigger>
              {{ selectedDefect }}
              <button
                *ngIf="selectedDefect"
                mat-icon-button
                class="clear-button"
                (click)="$event.stopPropagation(); clearFilter('defect')">
                <mat-icon>close</mat-icon>
              </button>
            </mat-select-trigger>
            <mat-option>
              <lib-mat-select-search
                [list]="defectTypes"
                [searchProperties]="['type']"></lib-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let defect of defectTypes" [value]="defect.type">
              {{ defect.type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="dropdown-fix">
          <mat-label>Select Block</mat-label>
          <mat-select [(ngModel)]="selectedBlock" (selectionChange)="applyFilter()">
            <mat-select-trigger>
              {{ selectedBlock }}
              <button
                *ngIf="selectedBlock"
                mat-icon-button
                class="clear-button"
                (click)="$event.stopPropagation(); clearFilter('block')">
                <mat-icon>close</mat-icon>
              </button>
            </mat-select-trigger>
            <mat-option>
              <lib-mat-select-search
                [list]="blockNumbers"
                [searchProperties]="['block']"
                (filtered)="onFilteredBlocks($event)"></lib-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let block of filteredBlocks" [value]="block.block">
              {{ block.block }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="dropdown-fix">
          <mat-label>Select Table</mat-label>
          <mat-select [(ngModel)]="selectedTable" (selectionChange)="applyFilter()">
            <mat-select-trigger>
              {{ selectedTable }}
              <button
                *ngIf="selectedTable"
                mat-icon-button
                class="clear-button"
                (click)="$event.stopPropagation(); clearFilter('table')">
                <mat-icon>close</mat-icon>
              </button>
            </mat-select-trigger>
            <mat-option>
              <lib-mat-select-search
                [list]="tableTypes"
                [searchProperties]="['table']"
                (filtered)="onFilteredTables($event)"></lib-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let table of filteredTables" [value]="table.table">
              {{ table.table }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="dropdown-fix">
          <mat-label>Select Severity</mat-label>
          <mat-select [(ngModel)]="selectedSeverity" (selectionChange)="applyFilter()">
            <mat-select-trigger>
              {{ selectedSeverity }}
              <button
                *ngIf="selectedSeverity"
                mat-icon-button
                class="clear-button"
                (click)="$event.stopPropagation(); clearFilter('severity')">
                <mat-icon>close</mat-icon>
              </button>
            </mat-select-trigger>
            <mat-option>
              <lib-mat-select-search
                [list]="severityTypes"
                [searchProperties]="['severity']"
                (filtered)="onFilteredSeverity($event)"></lib-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let severity of filteredSeverity" [value]="severity.severity">
              {{ severity.severity }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="dropdown-fix">
          <mat-label>Select Version</mat-label>
          <mat-select [(ngModel)]="selectedVersion" (selectionChange)="applyFilter()">
            <mat-select-trigger>
              {{ selectedVersion }}
              <button
                *ngIf="selectedVersion"
                mat-icon-button
                class="clear-button"
                (click)="$event.stopPropagation(); clearFilter('version')">
                <mat-icon>close</mat-icon>
              </button>
            </mat-select-trigger>
            <mat-option>
              <lib-mat-select-search
                [list]="versionTypes"
                [searchProperties]="['version']"
                (filtered)="onFilteredVersions($event)"></lib-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let version of filteredVersions" [value]="version.version">
              {{ version.version }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="table-view p-2">
        <table mat-table [dataSource]="dataSource" matSort>
          <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ column }}</th>
            <td mat-cell *matCellDef="let element">
              <ng-container [ngSwitch]="column">
                <span
                  *ngSwitchCase="'Remarks'"
                  [matTooltip]="element[column]"
                  [matTooltipDisabled]="!shouldTruncate(element[column])"
                  matTooltipPosition="above"
                  class="truncate-text">
                  {{ element[column] || '--' }}
                </span>
                <span *ngSwitchDefault>
                  {{ cleanText(element[column]) || '--' }}
                </span>
              </ng-container>
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <p class="my-5 font-weight-bold text-center" style="font-size: 34px" *ngIf="isDataLoading">
          Data Loading!!
        </p>
        <p
          class="my-5 font-weight-bold text-center"
          style="font-size: 34px"
          *ngIf="!dataSource?.data?.length && !isDataLoading">
          Data Not Available
        </p>
        <mat-paginator
          showFirstLastButtons
          [length]="totalItems"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          [disabled]="!dataSource?.data?.length || isDataLoading"></mat-paginator>
      </div>
    </mat-tab>
    <mat-tab label="Upload File" [disabled]="!dataSource?.data?.length">
      <div class="upload-container">
        <div class="wrapper">
          <form (click)="triggerFileInput()">
            <input
              class="file-input"
              type="file"
              name="file"
              hidden
              (change)="onFileChange($event)" />
            <mat-icon class="icon-fix-cloud">cloud_upload</mat-icon>
            <div class="upload-instructions">
              <p class="title">Tap File to Upload</p>
              <p>Or</p>
              <p class="browse-button">Browse a File</p>
            </div>
            <p class="file-info">
              <span>Supported File Format:</span>
              <span class="file-types">.xlsx</span>
            </p>
          </form>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
