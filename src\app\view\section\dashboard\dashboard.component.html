<div *ngIf="get_dashboard_data && main_data; then thenBlock; else noAccessTemplate"></div>

<ng-template #thenBlock>
  <ng-container *appHasRole="['admin', 'manager']; else noAccessTemplate">
    <project-details
      [get_dashboard_data]="get_dashboard_data"
      [main_data]="main_data"></project-details>
    <dashboard-map [get_dashboard_data]="get_dashboard_data"></dashboard-map>
  </ng-container>
</ng-template>

<ng-template #noAccessTemplate>
  <mat-card class="signup-card container-responsive" *ngIf="noDataFound">
    <mat-card-content>
      <div class="m-auto mb-3">
        <img src="{{ no_project }}" alt="Not Found" class="responsive-img" />
      </div>
      <div class="text-center mt-3">
        <button
          mat-button
          class="btn stepper-action-btn touch-target"
          *appHasRole="'admin'"
          [routerLink]="['/app/project/create']">
          Create Project
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</ng-template>
