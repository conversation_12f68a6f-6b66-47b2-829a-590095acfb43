.carnot-heading {
  font-size: var(--font-3xl);
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  margin: var(--spacing-md) var(--spacing-xl);
  font-family: "Poppins", sans-serif !important;
}
.section {
  font-family: "Poppins", sans-serif !important;
}
.carnot-heading:hover {
  text-decoration: none;
}
.video-player {
  width: 75%;
}
ul li a {
  cursor: pointer;
}
.header:hover {
  background: #fff;
  -webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);
}
.btns {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-md);
  line-height: normal;
  border: 1px solid transparent;
  border-radius: 2px;
  width: 8rem;
}
.btns-primary {
  color: white;
  background-color: var(--primary);
}
.btns-primary:hover {
  background-color: var(--primary);
}
.btns-primary.focus,
.btns-primary:focus {
  color: #fff;
  background-color: var(--primary);
  border-color: var(--primary);
}
.btns-primary.disabled,
.btns-primary:disabled {
  color: #fff;
  background-color: var(--primary);
  border-color: var(--primary);
}
.btns-primary:not(:disabled):not(.disabled).active,
.btns-primary:not(:disabled):not(.disabled):active,
.show > .btns-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}
.btns-rounded {
  border-radius: 80px;
}
.btns-stroke {
  background: 0 0;
}
a:hover {
  color: white;
}
h4 {
  font-weight: 600;
}
.width-relative-to-parent-input {
  width: 60%;
  display: block;
  margin: 0 auto;
}
.width-relative-to-parent {
  width: 60%;
  display: block;
  margin: 0 auto;
  margin-top: 0.5rem;
}
@media (min-width: 981px) {
  .width-relative-to-parent-input {
    width: 50%;
    margin-left: 10rem;
  }
  .width-relative-to-parent {
    width: 25%;
    margin-right: 10rem;
  }
}
.display-column {
  display: flex;
  flex-direction: column;
}
@media (max-width: 981px) {
  .display-column {
    flex-direction: row;
  }
}
.flex {
  display: flex;
}
.justify-between {
  justify-content: space-between;
}
.items-center {
  align-items: center;
}
.header {
  height: unset;
}
.header-main2 {
  display: flex;
  list-style: none;
  justify-content: space-evenly;
  width: 35rem;
  align-items: center;
  margin: 0;
}
@media (max-width: 767px) {
  .header-main2 {
    display: none;
  }
}
.header-btn {
  display: flex;
  justify-content: space-evenly;
  width: 15rem;
}
.mobile-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  float: right;
  margin-top: -60px;
}
.menu-toggle {
  display: none;
  flex-direction: column;
  flex: 1;
  cursor: pointer;
}
.bar {
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 3px 0;
}
.close-button {
  cursor: pointer;
  font-size: 35px;
  color: #fff;
  position: absolute;
  top: 30px;
  right: 20px;
}
.menu-modal {
  position: fixed;
  top: 2px;
  right: 0;
  width: 12rem;
}
.menu-items {
  list-style: none;
  margin-top: 60px;
  z-index: var(--z-modal);
  color: #fff;
  font-size: larger;
  padding: 6px;
  margin-left: 6px;
}
@media (max-width: 767px) {
  .menu-toggle {
    display: flex;
  }
  .video-player {
    width: 95%;
  }
  .btns {
    width: 100%;
    margin: var(--spacing-sm) 0;
  }
  .header-btn {
    width: 100%;
    justify-content: center;
  }
}
.modal-content {
  background: var(--primary);
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.3s ease;
}
.modal-content:hover {
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}
.icon-container {
  display: flex;
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 50px;
  padding: 10px;
}
.custom-icon-size {
  font-size: 40px;
  height: 40px;
  width: 40px;
  color: var(--primary);
}
