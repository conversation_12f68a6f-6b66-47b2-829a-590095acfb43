<div *ngIf="all_projects_carnot; then thenBlock; else noAccessTemplate"></div>
<ng-template #noAccessTemplate>
  <mat-card class="signup-card" *ngIf="noDataFound">
    <mat-card-content>
      <div class="m-auto mb-3">
        <img src="{{ no_project }}" alt="Not Found" class="responsive-img" />
      </div>
      <div class="text-center mt-3">
        <button
          mat-button
          class="btn stepper-action-btn"
          *appHasRole="'admin'"
          [routerLink]="['/app/project/create']">
          Create Project
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</ng-template>
<ng-template #thenBlock>
  <ng-container *appHasRole="['admin', 'manager', 'pilot']; else noAccessTemplate">
    <ng-container *ngIf="current_View == 'list'">
      <div class="search-box-top">
        <mat-form-field class="w-100">
          <mat-label class="field-content">Search..</mat-label>
          <mat-icon matPrefix class="field-icon">search</mat-icon>
          <input matInput placeholder="Search.." name="search" (input)="applyFilter($event)" />
        </mat-form-field>
      </div>
    </ng-container>
    <ng-container *ngIf="current_View == 'grid'">
      <div class="search-box-top">
        <mat-form-field class="w-100">
          <mat-label class="field-content">Search..</mat-label>
          <mat-icon matPrefix class="field-icon">search</mat-icon>
          <input matInput placeholder="Search.." name="search" (input)="onSearchChange($event)" />
        </mat-form-field>
        <div class="filter2-box-icon">
          <mat-form-field>
            <mat-select
              [(value)]="defaultOption"
              (selectionChange)="handleDropdownChange($event.value)">
              <mat-option *ngFor="let option of dropdownOptions" [value]="option.value">
                {{ option.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="modal-class" *ngIf="isModalOpen2" (click)="closeModal($event)" id="id01">
        <input
          class="search-box-top2"
          placeholder="Enter project name..."
          (input)="onSearchChange($event)" />
        <mat-icon class="search-box-icon2">search</mat-icon>
      </div>
      <div class="modal-class" *ngIf="isModalOpen">
        <mat-form-field class="filter2-box-icon-mobile">
          <mat-select
            [(value)]="defaultOption"
            (selectionChange)="handleDropdownChange($event.value)">
            <mat-option
              *ngFor="let option of dropdownOptions"
              [value]="option.value"
              (click)="handleDropdownChange(option.value)">
              {{ option.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </ng-container>
    <div class="button-view">
      <button mat-mini-fab matTooltip="Grid View" (click)="ViewClick('grid')">
        <mat-icon>apps</mat-icon>
      </button>
      <button mat-mini-fab matTooltip="Map View" (click)="ViewClick('map'); mapRender()">
        <mat-icon>map</mat-icon>
      </button>
      <button
        mat-mini-fab
        matTooltip="List View"
        (click)="ViewClick('list'); genrate_table()"
        id="isVisibleMobile">
        <mat-icon>list</mat-icon>
      </button>
      <button
        mat-mini-fab
        matTooltip="Filter Project"
        class="grid-view4"
        (click)="openDropdownModal()">
        <mat-icon>filter_list</mat-icon>
      </button>
      <button mat-mini-fab matTooltip="List View" class="grid-view5" (click)="openDropdownModal2()">
        <mat-icon>search</mat-icon>
      </button>
    </div>
    <div style="margin-top: 4rem">
      <ng-container *ngIf="current_View == 'grid'">
        <div *ngIf="project_list.length; else elseProjectBlock">
          <div class="row p-2">
            <div class="col-3 p-1 mb-2" *ngFor="let i of project_list; let k = index">
              <div class="project-container mat-elevation-z8">
                <div class="image-card">
                  <img src="{{ i.image }}" class="project-img" />
                  <div class="btn-icons-container">
                    <button
                      *ngIf="i.video"
                      class="mr-2"
                      mat-mini-fab
                      matTooltip="Current Field Status"
                      (click)="openVideodialog(i.video)">
                      <mat-icon>video_call</mat-icon>
                    </button>
                    <button
                      class="mr-2"
                      *appHasRole="'admin'"
                      mat-mini-fab
                      matTooltip="Share"
                      (click)="openSharedialog(i.name, i.id, i.users)">
                      <mat-icon>share</mat-icon>
                    </button>
                    <button
                      class="mr-2"
                      *appHasRole="['admin', 'manager']"
                      mat-mini-fab
                      matTooltip="Download"
                      (click)="openReportdialog(i.name, i.current_date, i.processed_data)">
                      <mat-icon>download</mat-icon>
                    </button>
                    <button mat-mini-fab matTooltip="Upload" (click)="selectedUploadPage(i.id)">
                      <mat-icon>cloud_upload</mat-icon>
                    </button>
                  </div>
                </div>
                <div class="details-card" id="{{ i.name }}">
                  <div
                    class="project-title"
                    [class.clickable]="i.current_date"
                    (click)="selectedMapdate(i.current_date, i.name, i.id, i.category)">
                    {{ i.name }}
                  </div>
                  <div class="project-capacity">
                    <p>{{ i.plant_capacity }}</p>
                    <p style="text-transform: capitalize">{{ i.category }}</p>
                  </div>
                  <div class="project-category">{{ i.city }},{{ i.state }},{{ i.country }}</div>
                  <div class="project-date" *ngIf="i.current_date">
                    <div class="d-flex align-items-center">
                      <span id="date_{{ k }}">{{ i.current_date }}</span>
                      <mat-icon mat-button [matMenuTriggerFor]="menu">arrow_drop_down</mat-icon>
                      <mat-menu #menu="matMenu">
                        <button
                          mat-menu-item
                          (click)="selectChange(date, k, i.status[j])"
                          id="date_{{ k }}"
                          *ngFor="let date of i.date; let j = index">
                          {{ date }}
                        </button>
                      </mat-menu>
                    </div>
                    <ng-container [ngSwitch]="i.current_date_status">
                      <div class="status-indication" *ngSwitchCase="'created'">
                        <div class="indication-bar" [ngStyle]="{ 'background-color': 'red' }"></div>
                        <span class="indication-text ml-2">Yet to start</span>
                      </div>
                      <div class="status-indication" *ngSwitchCase="'completed'">
                        <div
                          class="indication-bar"
                          [ngStyle]="{ 'background-color': 'green' }"></div>
                        <span class="indication-text ml-2">Completed</span>
                      </div>
                      <div class="status-indication" *ngSwitchCase="'ftp'">
                        <div
                          class="indication-bar"
                          [ngStyle]="{ 'background-color': 'yellow' }"></div>
                        <span class="indication-text ml-2">In Progress</span>
                      </div>
                      <div class="status-indication" *ngSwitchCase="'processing'">
                        <div
                          class="indication-bar"
                          [ngStyle]="{ 'background-color': 'yellow' }"></div>
                        <span class="indication-text ml-2">In Progress</span>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ng-template #elseProjectBlock>
          <div class="text-center font-weight-bolder fs-2">No Project Found..</div>
        </ng-template>
      </ng-container>
      <ng-container *ngIf="current_View == 'map'"><div id="mapdashboard"></div></ng-container>
      <ng-container *ngIf="current_View == 'list'">
        <div class="mat-elevation-z8">
          <table mat-table [dataSource]="dataSource" matSort>
            <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ column | titlecase }}</th>
              <td mat-cell *matCellDef="let element">{{ element[column] }}</td>
            </ng-container>
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr
              mat-row
              (click)="selectedMapdate(row.date, row.name, row.id, row.category)"
              *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <mat-paginator [pageSizeOptions]="[10, 20, 30]" showFirstLastButtons></mat-paginator>
        </div>
      </ng-container>
    </div>
  </ng-container>
</ng-template>
