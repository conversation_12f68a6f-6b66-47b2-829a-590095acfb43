import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { SharedModule } from '@/directives/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { AllProjectsComponent } from './all-projects/all-projects.component';
import { CreateProjectComponent } from './create-project/create-project.component';
import { DocumentsComponent } from './create-project/documents/documents.component';
import { EnvironmentComponent } from './create-project/environment/environment.component';
import { FileComponent } from './create-project/file/file.component';
import { PayloadComponent } from './create-project/payload/payload.component';
import { ProjectDetailsComponent } from './create-project/project-details/project-details.component';
import { SubscriptionPlanComponent } from './create-project/subscription-plan/subscription-plan.component';
import { DialogExampleComponent } from './dialog-example/dialog-example.component';
import { ProjectRoutingModule } from './project-routing.module';

@NgModule({
  declarations: [
    AllProjectsComponent,
    CreateProjectComponent,
    DocumentsComponent,
    EnvironmentComponent,
    PayloadComponent,
    FileComponent,
    SubscriptionPlanComponent,
    ProjectDetailsComponent,
    DialogExampleComponent,
  ],
  imports: [
    CommonModule,
    ProjectRoutingModule,
    MaterialLibrary,
    ReactiveFormsModule,
    FormsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    SharedModule,
  ],
})
export class ProjectModule {}
