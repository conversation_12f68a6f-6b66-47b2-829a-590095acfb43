.project-container {
  padding: 1rem;
  display: flex;
  width: 100%;
  position: relative;
  flex-direction: row;
}
.mat-stepper-vertical {
  background-color: var(--background) !important;
}
@media (max-width: 767px) {
  .project-container {
    flex-direction: column;
    height: fit-content;
    padding: var(--spacing-sm);
  }
}
.leaflet-div-icon2 {
  background: red;
  border: 0.3125rem solid rgba(255, 255, 255, 0.5);
  color: blue;
  font-weight: 500;
  text-align: center;
  border-radius: 50%;
  line-height: 1.875rem;
}
.active-project {
  width: 100%;
  border-radius: 10px;
  background-color: #f2f2f2;
  height: 105px;
  cursor: pointer;
  border: none;
  padding: 10px;
  display: inline-block;
  color: #1b1b1b;
  box-shadow: 0 3px 6px var(--shadow);
}
.active-project .project-image {
  height: 5.5rem;
  height: 5.5rem;
  border-radius: 0.625rem;
  float: left;
}
@media only screen and (max-width: 700px) {
  .active-project .project-image {
    height: 7rem;
    margin-top: 0.1rem;
    width: 30%;
    height: 7rem;
    margin-top: 0.1rem;
    width: 30%;
  }
}
.active-project .project-active-details {
  float: left;
  padding-left: 1.875rem;
  width: 75%;
}
.project-active-details p {
  margin-top: 0;
  margin-bottom: 0.3125rem;
}
.project-card {
  width: 50%;
}
@media (max-width: 700px) {
  .project-card {
    width: 100%;
  }
}
.project-list {
  width: 70%;
  position: relative;
  flex: 1;
}
@media (max-width: 700px) {
  .project-list {
    width: 80%;
  }
}
.horizontal-card {
  background-color: var(--primary);
  height: 100%;
  width: 100%;
  border-radius: 1.25rem;
  border: none;
  padding: 5.625rem 1.25rem;
  position: relative;
}
.horizontal-card-bg {
  background-color: var(--primary);
  height: 100%;
  width: 25%;
  border-radius: 1.25rem;
  border: none;
  padding: 15.625rem 1.25rem;
  position: relative;
}
.horizontal-card-bg-null {
  background-color: var(--white);
  height: 100%;
  width: 60%;
  border-radius: 1.25rem;
  box-shadow: none;
  border: transparent;
  padding: 5.625rem 1.25rem;
  position: relative;
}
.horizontal-card .title {
  font-size: 10rem;
  font-weight: 900;
  color: rgba(187, 187, 187, 0.719);
  letter-spacing: 0.125rem;
}
@media (max-width: 700px) {
  .horizontal-card .title {
    margin-bottom: 5rem;
    margin-top: -2rem;
    font-size: 7rem;
    margin-left: -2rem;
  }
}
.horizontal-card .project-details {
  position: absolute;
  bottom: 1.25rem;
  padding-right: 3.125rem;
  width: 100%;
}
.progress-percentage {
  position: absolute;
  right: 3.125rem;
  top: 2.1875rem;
}
.custom-progress {
  height: 0.5rem;
  border-radius: 1.25rem;
}
.project-details h5 {
  text-transform: uppercase;
  letter-spacing: 0.0625rem;
  font-size: 1.5625rem;
  font-weight: 600;
  color: #eee;
}
.project-details p {
  color: #eee;
  font-size: 0.875rem;
}
.horizontal-card .project-image .img {
  width: 18.75rem;
  height: auto;
  position: absolute;
  right: -9.375rem;
  bottom: 0;
  top: 0;
  margin: auto;
  z-index: 100;
}
.stepper .title {
  font-size: 1.5625rem;
  letter-spacing: 0.0625rem;
}
.title .icon {
  position: relative;
  top: 0.25rem;
}
@media (max-width: 700px) {
  .stepper .steps {
    margin-left: 2rem;
    width: 100%;
    margin-top: 0;
  }
}
.steps .custom-btn {
  margin: 0.625rem 1.25rem;
}
@media (max-width: 700px) {
  .steps .custom-btn {
    width: max-content;
  }
}
.steps .custom-btn .icon {
  top: -0.125rem;
  font-size: 1.5625rem;
  position: relative;
}
.prev,
.next {
  position: absolute;
  z-index: 1000;
  top: 0;
  bottom: 0;
  margin: auto;
}
.prev {
  left: 0;
}
.next {
  right: 0;
}
.mat-button {
  border: none;
  color: var(--primary);
  padding: 0.125rem 1.625rem;
  font-size: 0.875rem;
  text-align: center;
  margin: 0.25rem 0.125rem;
  opacity: 1;
  transition: 0.3s;
}
.mat-button:hover {
  opacity: 1;
  background-color: var(--primary);
  border: none;
  color: var(--white);
  padding: 0.125rem 1.8125rem;
  font-size: 0.875rem;
  text-align: center;
  margin: 0.25rem 0.125rem;
  opacity: 0.6;
  transition: 0.3s;
}
.title {
  font-weight: 600;
  font-size: 1.8rem;
  padding: 0.8rem 1.5rem;
}
@media (max-width: 700px) {
  .title-imp {
    display: inline-block;
    width: 20rem;
    margin-left: 2px;
  }
}
.card {
  background-color: var(--white);
  border-radius: 10px;
  border: none;
  position: relative;
  margin-bottom: 20px;
  margin-top: 20px;
  box-shadow:
    0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1),
    0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1),
    0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12),
    0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
  flex: 0 0 33.333333%;
}
.header-text {
  font-weight: 600;
  font-size: 1.6rem;
  text-align: center;
  text-transform: uppercase;
}
.card2 {
  background-color: #f2f2f2;
  border-radius: 0.625rem;
  border: none;
  position: relative;
  box-shadow:
    0 0.46875rem 2.1875rem rgba(90, 97, 105, 0.1),
    0 0.9375rem 1.40625rem rgba(90, 97, 105, 0.1),
    0 0.25rem 0.53125rem rgba(90, 97, 105, 0.12),
    0 0.125rem 0.1875rem rgba(90, 97, 105, 0.1);
  border-left: solid 5px var(--primary);
  padding: 0.5rem;
}
@media (min-width: 768px) {
  .card2 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: #f2f2f2;
  }
}
.stats-card {
  padding: 0.5rem;
  background-color: #f2f2f2;
  position: relative;
}
.box-margin {
  padding: 0;
  margin: 0;
}
.box-head {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 1rem;
  width: 100%;
}
.box-text {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0.5rem 1rem;
  width: 100%;
}
.box-sm {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 1rem;
  width: 100%;
  width: 100%;
}
.display-flex {
  display: flex;
  justify-content: space-evenly;
  gap: 1rem;
}
@media (max-width: 767px) {
  .display-flex {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }
  .active-project {
    height: auto;
    min-height: 105px;
    margin-bottom: var(--spacing-sm);
  }
  .box-head {
    font-size: var(--font-lg);
    margin: var(--spacing-sm);
  }
  .box-text {
    font-size: var(--font-xl);
    margin: var(--spacing-xs) var(--spacing-sm);
  }
  .box-sm {
    font-size: var(--font-md);
    margin: 0 var(--spacing-sm);
  }
  .stats-container {
    margin-bottom: var(--spacing-md);
  }
  .project-container {
    margin: var(--spacing-sm) 0;
  }
}
.btn-place {
  position: absolute;
  right: 1rem;
  top: 2.5rem;
}
.stats-container {
  width: 100%;
}
