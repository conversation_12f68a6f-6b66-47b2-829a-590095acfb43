import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-aoi-dialog',
  templateUrl: './aoi-dialog.component.html',
  styleUrls: ['./aoi-dialog.component.css'],
})
export class AoiDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<AoiDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  onSave(): void {
    this.dialogRef.close(this.data);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
