import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { AuthenticationRoutingModule } from './authentication-routing.module';
import { LoginComponent } from './login/login.component';
import { RecoveryComponent } from './recovery/recovery.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { SignupComponent } from './signup/signup.component';

@NgModule({
  declarations: [LoginComponent, SignupComponent, ResetPasswordComponent, RecoveryComponent],
  imports: [CommonModule, AuthenticationRoutingModule, MaterialLibrary, ReactiveFormsModule],
})
export class AuthenticationModule {}
