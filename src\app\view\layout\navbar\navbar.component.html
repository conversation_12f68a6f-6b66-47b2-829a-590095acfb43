<mat-toolbar class="nav-fixed">
  <!-- Mobile Menu Toggle -->
  <button
    class="mobile-menu-toggle mobile-only"
    [class.active]="isMobileMenuOpen"
    (click)="toggleMobileMenu()"
    aria-label="Toggle mobile menu">
    <div class="hamburger">
      <span></span>
      <span></span>
      <span></span>
    </div>
  </button>

  <!-- Logo -->
  <div class="logo-container">
    <img class="logo" src="assets/logo.png" alt="Carnot" *ngIf="false" />
    <span class="brand-text">Carnot</span>
  </div>

  <!-- Desktop Menu -->
  <div class="menu-section desktop-only">
    <button mat-mini-fab [matMenuTriggerFor]="menu">
      <mat-icon>perm_identity</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item [routerLink]="['/app', 'my-profile']">
        <mat-icon>account_circle</mat-icon>
        <span>My Profile</span>
      </button>
      <button mat-menu-item [routerLink]="['/app', 'manage-users']" *appHasRole="'admin'">
        <mat-icon>supervised_user_circle</mat-icon>
        <span>Manage users</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </div>

  <!-- Mobile Menu -->
  <div class="menu-section mobile-only">
    <button mat-icon-button [matMenuTriggerFor]="mobileMenu">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #mobileMenu="matMenu">
      <button mat-menu-item [routerLink]="['/app', 'my-profile']">
        <mat-icon>account_circle</mat-icon>
        <span>My Profile</span>
      </button>
      <button mat-menu-item [routerLink]="['/app', 'manage-users']" *appHasRole="'admin'">
        <mat-icon>supervised_user_circle</mat-icon>
        <span>Manage users</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </div>
</mat-toolbar>
