import { AuthService } from '@/controller/auth.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { UserService } from '@/controller/user.service';
import { HttpService } from '@/view/map-section/services-map/http.service';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit {
  @Input() isMobileOpen = false;
  @Output() closeMobile = new EventEmitter<void>();

  isAnalytics = false;
  sidebar_logo = '';
  help_center = environment.help_center;
  selectedIcon: string | null = null;

  constructor(
    private sharedDataService: SharedDataService,
    private userService: UserService,
    private httpService: HttpService,
    private authService: AuthService,
    private toastr: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.setActiveItemFromRoute();
  }

  private initializeComponent(): void {
    this.userService.sidebarLogo$.subscribe((logo: string) => {
      this.sidebar_logo = logo;
    });
    this.httpService.get_analytics_icon().subscribe(data => {
      this.isAnalytics = data;
    });
  }
  private setActiveItemFromRoute(): void {
    const currentUrl = this.router.url;
    if (currentUrl.includes('/home')) {
      this.selectedIcon = 'home';
    } else if (currentUrl.includes('/project/create')) {
      this.selectedIcon = 'create';
    } else if (currentUrl.includes('/project')) {
      this.selectedIcon = 'projects';
    } else if (currentUrl.includes('/map')) {
      this.selectedIcon = 'map';
    } else if (currentUrl.includes('/manage-users')) {
      this.selectedIcon = 'manage-users';
    } else if (currentUrl.includes('/my-profile')) {
      this.selectedIcon = 'my-profile';
    }
  }
  selectItem(item: string): void {
    this.selectedIcon = item;
    if (this.isMobileOpen) {
      this.closeSidebar();
    }
  }
  closeSidebar(): void {
    this.closeMobile.emit();
  }
  logout(): void {
    this.authService.logout();
    this.toastr.success('You have been successfully logged out', 'Logout');
    this.sharedDataService.clearData();
  }
  openHelpCenter(): void {
    window.open(this.help_center, '_blank');
  }
}
