.img-container {
  padding: 20px;
}

.img-container .env-image {
  width: 100%;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  margin: auto;
}

@media only screen and (max-width: 600px) {
  .env-image {
    display: none;
  }
}

@media only screen and (max-width: 600px) {
  .stepper-action-btn {
    width: 100px;
    text-align: center !important;
    padding: 5px !important;
  }
}

.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

/* .btn {
    border: 2px solid gray;
    color: var(--white);
    background-color: var(--white);
    padding: 8px 20px 8px 20px;
    border-radius: 8px;
    font-size: 20px;
    font-weight: 500;
} */

.upload-btn-wrapper input[type="file"] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

input {
}

h4 {
}

.mat-button {
  background-color: var(--primary);
  border: none;
  color: blue;
  padding: 2px 26px;

  font-size: 14px;
  text-align: center;
  margin: 4px 2px;
  opacity: 1;
  transition: 0.3s;
}

.mat-button:hover {
  background-color: var(--primary);
  border: none;
  color: blue;
  padding: 2px 29px;

  font-size: 14px;
  text-align: center;
  margin: 4px 2px;
  opacity: 1;
  transition: 0.3s;
}
