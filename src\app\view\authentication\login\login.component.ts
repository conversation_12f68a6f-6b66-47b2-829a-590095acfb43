import { AuthService } from '@/controller/auth.service';
import { DataService } from '@/controller/data.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  Isworking = false;
  logo: string = environment.login_page_logo;
  client_logo: string = environment.client_logo;
  hide = true;
  showEmailLogin: boolean = false;

  clientRows: string[][] = [
    ['enel', 'lnt', 'acwa-power', 'nevados'],
    ['statkraft', 'gulfrel', 'cleanmax', 'azure-power'],
    ['mahindra-teqo', 'alfanar', 'plus-power', 'juniper-green'],
  ];

  constructor(
    private router: Router,
    private authService: AuthService,
    private sharedDataService: SharedDataService,
    private dataService: DataService,
    private toastr: ToastrService
  ) {
    this.loginForm = new FormGroup({
      username: new FormControl('', Validators.required),
      password: new FormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.sharedDataService.clearData();
  }
  loginWithSocial(provider: string) {
    this.authService.loginWithSocial(provider);
  }
  loginWithEmail() {
    this.Isworking = true;
    const loginObject = {
      username: this.dataService.encryptString(this.loginForm.get('username').value.trim()),
      password: this.dataService.encryptString(this.loginForm.get('password').value.trim()),
    };
    this.authService
      .loginWithEmail(loginObject)
      .then(res => {
        if (res && res['status'] === 'success') {
          this.toastr.success(res['message'], 'Welcome Back!!');
          this.router.navigate(['/app', 'home']);
          this.Isworking = false;
        } else {
          this.Isworking = false;
          this.toastr.error(res['message'], 'Login Failed!');
        }
      })
      .catch(err => {
        this.Isworking = false;
        this.toastr.error(err.message || 'An error occurred during login', 'Login Failed!');
      });
  }
  hasError = (controlName: string, errorName: string) => {
    return this.loginForm.controls[controlName].hasError(errorName);
  };
}
