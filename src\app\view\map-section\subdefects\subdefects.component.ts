import { ApiConfigService } from '@/controller/api-config.service';
import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TooltipPosition } from '@angular/material/tooltip';
import { HttpService } from '../services-map/http.service';

@Component({
  selector: 'subdefects',
  templateUrl: './subdefects.component.html',
  styleUrls: ['./subdefects.component.css'],
})
export class SubdefectsComponent implements OnInit {
  @ViewChild('imageDialog') imageDialog: TemplateRef<any>;
  positionOptions: TooltipPosition = 'above';
  position = new FormControl(this.positionOptions);
  subdefects_page: any;
  @Output() subdefects_page_event: EventEmitter<any> = new EventEmitter<any>();
  rectify_defects: any;
  track: any = null;
  kml_file_location: any;
  table_no: any;
  polies = [];
  poly: any;
  descObj: any;
  summ: any;
  project_id_summary: any;
  dateonload: string;
  project_values: any;
  defect_values: any;
  count_dec: any;
  count_dec_val: any;
  get_summary_defect_count: any;
  get_summary_subdefect_defect_count: any;
  get_inventor_subdefect_defect_count: any;
  defects_data = {};
  defects = [];
  get_inventor_defect_count: any;
  inventor_keys: any;
  mode: any;
  sidebarVisible: boolean = true;
  count = 0;
  selectedTable: string = null;
  isVisible = true;
  constructor(
    private _http: HttpService,
    private apiConfigService: ApiConfigService,
    public dialog: MatDialog
  ) {}

  toggleSidebarVisibility() {
    this.isVisible = false;
    setTimeout(() => {
      this.isVisible = true;
    }, 100);
  }

  ngOnInit(): void {
    this._http.getsubdefects().subscribe(() => {
      this.defects = [];
      this.inventor_keys = [];
      this.defect_values = [];
      this.dateonload = localStorage.getItem('date');
      this.apiConfigService.getProjectData().subscribe(data => {
        this.summ = data['data'];
        this.project_id_summary = Object.keys(data['data']);
        this.kml_file_location = this.summ['processed_data'][this.dateonload]['kml_file_location'];
        this.mode = localStorage.getItem('mode');
        const x = localStorage.getItem('current_tab');
        const p = parseInt(localStorage.getItem('page'), 10);
        this.inventor_keys = Object.keys(
          this.summ['processed_data'][this.dateonload]['inverter_layers']
        );
        if (this.mode == 'summary') {
          this.get_summary_defect_count =
            this.summ['processed_data'][this.dateonload]['summary_layers'][x]['Count'];
          if (this.get_summary_defect_count != 0) {
            this.opendefectsbar();
            this.defect_value_summary();
          } else {
            this.closedefectsbar();
          }
        } else if (this.mode == 'summary_sub_details') {
          const n = localStorage.getItem('sub_defects');
          this.get_summary_subdefect_defect_count =
            this.summ['processed_data'][this.dateonload]['summary_layers'][x]['sub_group'][n][
              'Count'
            ];
          if (this.get_summary_subdefect_defect_count != 0) {
            this.opendefectsbar();
            this.defect_value_summary();
          } else {
            this.closedefectsbar();
          }
        } else if (this.mode == 'invertor') {
          const p_val = Number(p) - 1;
          this.get_inventor_defect_count =
            this.summ['processed_data'][this.dateonload]['inverter_layers'][
              this.inventor_keys[p_val]
            ][x]['count'];
          if (this.get_inventor_defect_count != 0) {
            this.opendefectsbar();
            this.defect_value_inverter();
          } else {
            this.closedefectsbar();
          }
        } else if (this.mode == 'inverter_sub_details') {
          const p_val = Number(p) - 1;
          const n = localStorage.getItem('sub_defects');
          this.get_inventor_subdefect_defect_count =
            this.summ['processed_data'][this.dateonload]['inverter_layers'][
              this.inventor_keys[p_val]
            ][x]['sub_group'][n]['count'];
          if (this.get_inventor_subdefect_defect_count != 0) {
            this.opendefectsbar();
            this.defect_value_inverter();
          } else {
            this.closedefectsbar();
          }
        }
      });
    });
  }
  closeSidebar() {
    this.isVisible = !this.isVisible;
  }
  opendefectsbar() {
    const sideBar = document.getElementById('defectssidebar');
    if (window.innerWidth < 800) {
      sideBar.style['z-index'] = 1100;
    } else {
      sideBar.style['z-index'] = 1000;
    }
  }
  closedefectsbar() {
    const sideBar = document.getElementById('defectssidebar');
    sideBar.style['z-index'] = 0;
  }
  defect_value_summary() {
    const kml_name_load = localStorage.getItem('kmlfilename').split(',');
    this.count_dec = 0;
    this.count_dec_val = 0;
    this.load_array(this.kml_file_location + 'GLOBAL/', kml_name_load);
  }
  defect_value_inverter() {
    const kml_name_load_inv = localStorage.getItem('kmlfilename').split(',');
    const p = parseInt(localStorage.getItem('page'));
    if (p <= 9 && p >= 1) {
      this.load_array(this.kml_file_location + 'INVERTER0' + p + '/', kml_name_load_inv);
    } else {
      this.load_array(this.kml_file_location + 'INVERTER' + p + '/', kml_name_load_inv);
    }
  }
  async load_array(url, defects_arr) {
    for (const defect of defects_arr) {
      try {
        const response = await fetch(url + defect + '.kml');
        const kmltext = await response.text();
        const parser = new DOMParser();
        const kml = parser.parseFromString(kmltext, 'text/xml');
        const place = Array.from(kml.getElementsByTagName('Placemark'));
        for (const placemark of place) {
          const description = placemark.childNodes[1].textContent;
          const coor = placemark.getElementsByTagName('coordinates')[0].textContent;
          const latlngArray = coor.replace(/\n/g, ' ').split(/[ ,]+/).filter(Boolean);
          const pa = parser.parseFromString(description, 'text/html');
          const u = Array.from(pa.getElementsByTagName('td'));
          let b;
          let n = 0;
          for (const td of u) {
            if (n % 2 === 0) {
              b = td.textContent;
            } else {
              if (b === 'Defect:') {
                this.table_no = td.textContent;
              }
            }
            n++;
          }
          this.polygonMarkerCreating(latlngArray, description);
        }
      } catch (error) {
        console.error(error);
      }
    }
  }
  polygonMarkerCreating(lat, dec) {
    this.project_values = [];
    const polygonPoints = [];
    let j = 1;
    let k = 0;
    let l = 2;
    const iterate = lat.length / 3;
    for (let i = 0; i < iterate; i++) {
      polygonPoints.push([lat[i + j], lat[i + k], lat[i + l]]);
      j = j + 2;
      k = k + 2;
      l = l + 2;
    }
    this.polies.push(this.poly);
    const parser = new DOMParser();
    const markup = parser.parseFromString(dec, 'text/html');
    const y = markup.getElementsByTagName('td');
    let b = '';
    let f = null;
    this.descObj = [];
    if (y.length > 0) {
      for (let t = 0; t < y.length; t++) {
        if (t % 2 == 0) {
          b = y[t].innerText;

          f = b.split(':');
          b = f[0].replace(' ', '_');
          this.descObj.push(b);
        } else {
          b = y[t].innerText;
          if (b === null || b.trim() === '') {
            b = y[t].innerHTML;
          }
          if (t == y.length - 1 || t == y.length - 3 || t == y.length - 5) {
            this.descObj.push(b);
            continue;
          }
          f = b.split(':');
          b = f[0].replace(' ', '_');
          this.descObj.push(b);
        }
      }
      this.defects_array_making(this.descObj);
    }
  }
  defects_array_making(dec_obj) {
    this.count++;
    this.project_values.push(dec_obj);
    if (this.count_dec === 0) {
      this.defect_values = this.project_values;
    } else {
      this.defect_values = this.defect_values.concat(this.project_values);
    }
    this.project_values = [];
    this.count_dec = 1;
    switch (this.mode) {
      case 'summary':
        this.getData(this.defect_values);
        break;
      case 'summary_sub_details':
        this.getData(this.defect_values);
        break;
      case 'invertor':
        this.getData(this.defect_values);
        break;
      case 'inverter_sub_details':
        this.getData(this.defect_values);
        break;
      default:
        break;
    }
  }
  getData(defect_values) {
    this.defects = defect_values.map(defectValue => {
      const temp = {};
      for (let k = 0; k < defectValue.length; k += 2) {
        const key = defectValue[k];
        const value = defectValue[k + 1];
        temp[key] = value;
      }
      return temp;
    });
  }
  selectedMaplocation(lat, long, table) {
    this.selectedTable = table;
    this.subdefects_page_event.emit([lat, long]);
  }
  openDialog(template: TemplateRef<any>, defect: any): void {
    this.dialog.open(template, {
      data: defect,
      width: '80%',
      height: '80%',
    });
  }
  async downloadImage(imageSource: string) {
    const name = imageSource.substring(imageSource.lastIndexOf('/') + 1);
    await this._http.fileDownload(name, imageSource);
  }
}
