import { ApiConfigService } from '@/controller/api-config.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HttpService {
  total_rectify_data: any[] = [];
  files: any[] = [];
  mission_val: any[] = [];
  set_mision_flight_data: any[] = [];

  constructor(
    private toastr: ToastrService,
    private apiConfigService: ApiConfigService,
    private http: HttpClient
  ) {}

  private newDate = new BehaviorSubject<any>({
    date: '',
  });
  private close_side_bar = new BehaviorSubject<any>({
    close_side_bar: 'summarySidebar',
  });
  private newDatefromanalytics = new BehaviorSubject<any>({
    date: '',
  });
  private newDatefromAllProjects = new BehaviorSubject<any>({
    date: '',
  });
  private newscreenshotmap = new BehaviorSubject<any>({
    date: 'Capture',
  });
  private newAoI = new BehaviorSubject<any>({
    AOI: 'Capture',
  });
  private projname = new BehaviorSubject<any>({
    projnameval: 'Adani',
  });
  private visibility = new BehaviorSubject<any>({
    visibility_val: 'visible',
  });
  private NewAdduser = new BehaviorSubject<any>({
    NewAdduserval: [],
  });
  private newgradinggraph = new BehaviorSubject<any>({
    newgradinggraph_val: 'visible',
  });
  private defect_table_no = new BehaviorSubject<any>({
    Table_no: '',
  });
  private is_analytics_icon = new BehaviorSubject<any>(false);

  setAreaofinterest(AoI: any) {
    this.newAoI.next(AoI);
  }
  setNewUserInfo(date: any) {
    this.newDate.next(date);
  }
  setdefect_table_no(table_no: any) {
    this.defect_table_no.next(table_no);
  }
  setgradingtable(tableno: any) {
    this.newgradinggraph.next(tableno);
  }
  setclosesidebar(close_side_bar: any) {
    this.close_side_bar.next(close_side_bar);
  }
  setNewdateanalyticsInfo(date: any) {
    this.newDatefromanalytics.next(date);
  }
  setproject_name(projname: any) {
    this.projname.next(projname);
  }
  setNewadduser(fname) {
    this.NewAdduser.next(fname);
  }
  setsubdefects(visibility) {
    this.visibility.next(visibility);
  }
  setChangedCompletedDate(date: any) {
    this.newDatefromAllProjects.next(date);
  }
  setrectify_data(rectify_data) {
    this.total_rectify_data = rectify_data;
  }
  setfiles(files) {
    this.files = files;
  }
  setmissiondata(data) {
    this.mission_val = data;
  }
  set_mision_flight_detail(data) {
    this.set_mision_flight_data = data;
  }
  set_analytics_icon(data) {
    this.is_analytics_icon.next(data);
  }
  getproject_name() {
    return this.projname.asObservable();
  }
  getgradingtable() {
    return this.newgradinggraph.asObservable();
  }
  getAreaofinterest() {
    return this.newAoI.asObservable();
  }
  getNewUserInfo() {
    return this.newDate.asObservable();
  }
  getclosesidebar() {
    return this.close_side_bar.asObservable();
  }
  getNewdateanalyticsInfo() {
    return this.newDatefromanalytics.asObservable();
  }
  getNewadduser() {
    return this.NewAdduser.asObservable();
  }
  getChangedCompletedDate() {
    return this.newDatefromAllProjects.asObservable();
  }
  getscreenshotmap() {
    return this.newscreenshotmap.asObservable();
  }
  getsubdefects() {
    return this.visibility.asObservable();
  }
  getdefect_table_no() {
    return this.defect_table_no.asObservable();
  }
  getrectify_data() {
    return this.total_rectify_data;
  }
  getfiles() {
    return this.files;
  }
  getmissiondata() {
    return this.mission_val;
  }
  get_mision_flight_detail() {
    return this.set_mision_flight_data;
  }
  get_analytics_icon() {
    return this.is_analytics_icon;
  }
  async fileDownload(fileName, fileUrl: string) {
    if (!fileUrl) {
      this.toastr.warning('No file available for download.');
      return;
    }
    const signedUrl = await this.getSignedUrl(fileUrl);
    if (!signedUrl) {
      this.toastr.error('Failed to generate download link.');
      return;
    }
    this.toastr.info('Please wait..', 'Downloading Started!!!');

    const headers = new HttpHeaders().set('X-Skip-Authorization', 'true');
    this.http.get(signedUrl, { headers, responseType: 'blob' }).subscribe({
      next: blob => {
        const ext = signedUrl.split('.').pop()?.toLowerCase();
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `${fileName}.${ext}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      },
      error: () => {
        this.toastr.error('Download failed. Please try again.');
      },
    });
  }
  async getSignedUrl(fileUrl: string): Promise<string> {
    try {
      const urlParts = fileUrl.split('/');
      const bucketName = urlParts[2].split('.')[0];
      const fileKey = urlParts.slice(3).join('/');
      const res = await firstValueFrom(this.apiConfigService.getSignedUrl(bucketName, fileKey));
      if (res && res[0].url) {
        return res[0].url;
      } else {
        this.toastr.warning('No signed URL was generated');
        return null;
      }
    } catch (err) {
      console.error('Error getting signed URL:', err);
      this.toastr.error('Failed to generate download link');
      return null;
    }
  }
}
