<div class="sharecomponent">
  <p class="title mb-4">Users Access List - {{ data.projectName }}</p>
  <div class="table-responsive text-center">
    <table class="table table-bordered table-striped table-hover align-middle">
      <thead class="sticky-top">
        <tr>
          <th>Full name</th>
          <th>Email</th>
          <th>Role</th>
          <th>Access</th>
        </tr>
      </thead>
      <tbody class="table-group-divider">
        <tr *ngFor="let user of usersData; let i = index">
          <td>{{ user.name }}</td>
          <td>{{ user.email }}</td>
          <td class="text-uppercase">{{ user.role }}</td>
          <td>
            <div class="form-switch" style="font-size: 1.5rem">
              <input
                [class.yes]="user.access"
                class="form-check-input"
                type="checkbox"
                role="switch"
                [(ngModel)]="user.access"
                (ngModelChange)="updateRole(user)" />
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <mat-dialog-actions style="justify-content: space-evenly">
    <button mat-button class="w-25 text-uppercase" (click)="onClose()" [disabled]="Isworking">
      <span *ngIf="!Isworking">Close</span>
      <span *ngIf="Isworking">
        <div class="spinner-border" role="status" *ngIf="Isworking"></div>
      </span>
    </button>
  </mat-dialog-actions>
</div>
