import { ApiConfigService } from '@/controller/api-config.service';
import { SharedDataService } from '@/controller/shared-data.service';
import { AdvancedChartConfig, PowerLossData } from '@/interfaces/powerloss';
import { HttpService } from '@/view/map-section/services-map/http.service';
import { KeyValue } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-powerloss-dashboard',
  templateUrl: './powerloss-dashboard.component.html',
  styleUrl: './powerloss-dashboard.component.css',
})
export class PowerlossDashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  defectDistributionChart: AdvancedChartConfig;
  blockDefectsChart: AdvancedChartConfig;
  powerLossScatter: AdvancedChartConfig;
  temperatureComparisonChart: AdvancedChartConfig;
  defectSummaryChart: AdvancedChartConfig;
  temperatureHeatmap: AdvancedChartConfig;
  defectStandardDeviationChart: AdvancedChartConfig;
  boxPlotChart: AdvancedChartConfig;
  threeDScatterChart: AdvancedChartConfig;
  parallelChart: AdvancedChartConfig;

  data: PowerLossData | null = null;
  excel_report: string = null;
  analysis_pdf_report: string = null;
  reference_pdf_report: string = null;
  criticalDefects: any = [];
  projectInfo: any = {};
  dataSource = new MatTableDataSource([]);
  isDataLoading = true;
  displayedColumns: string[] = ['table_name', 'defect_count', 'power_loss', 'avg_temp'];
  formattedDefectNames = new Map<string, string>();

  showDefectChart = true;
  showTempChart = true;
  showBubbleChart = true;
  showBoxChart = true;

  selectedTempChartType: 'group' | 'stack' | 'line' = 'line';
  selectedDefectChartType: 'dual' | 'mean' | 'total' = 'mean';
  normalizationMode: 'mean' | 'total' = 'mean';
  scaleMode: 'avg' | 'delta' = 'delta';
  selectedChartType: 'box' | 'violin' = 'violin';

  constructor(
    private http: HttpClient,
    private _http: HttpService,
    private toastr: ToastrService,
    private apiConfigService: ApiConfigService,
    private sharedDataService: SharedDataService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this._http.set_analytics_icon(true);
    this.projectInfo['project_id'] = localStorage.getItem('project_id');
    this.projectInfo['project_name'] = localStorage.getItem('project_name');
    this.projectInfo['date'] = localStorage.getItem('date');

    this.loadData();
  }

  ngOnDestroy(): void {
    this._http.set_analytics_icon(false);
    this.data = null;
    this.criticalDefects = [];
    this.formattedDefectNames.clear();
    this.dataSource.data = [];
    this.defectDistributionChart = null;
    this.blockDefectsChart = null;
    this.powerLossScatter = null;
    this.temperatureComparisonChart = null;
    this.defectSummaryChart = null;
    this.temperatureHeatmap = null;
    this.defectStandardDeviationChart = null;
    this.boxPlotChart = null;
    this.threeDScatterChart = null;
    this.parallelChart = null;
  }

  private async loadData(): Promise<void> {
    this.isDataLoading = true;
    try {
      const data = await firstValueFrom(
        this.sharedDataService.fetchData(
          this.apiConfigService.getProjectByID(this.projectInfo['project_id']),
          `project_${this.projectInfo['project_id']}`
        )
      );

      const urlsData = data?.processed_data?.[this.projectInfo['date']]?.power_loss;
      if (!urlsData) throw new Error('No processed data available.');

      this.excel_report = urlsData.excel_report;
      this.analysis_pdf_report = urlsData.analysis_pdf_report;
      this.reference_pdf_report = urlsData.reference_pdf_report;

      const signedUrl = await this._http.getSignedUrl(urlsData.json_report);
      if (!signedUrl) throw new Error('No Power Loss data available.');

      const headers = new HttpHeaders().set('X-Skip-Authorization', 'true');
      const rawJson = await firstValueFrom(
        this.http.get(signedUrl, { headers, responseType: 'text' })
      );
      const safeJson = rawJson.replace(/\bNaN\b/g, 'null');
      const powerLossData: PowerLossData = JSON.parse(safeJson);

      this.data = powerLossData;
      if (powerLossData?.power_loss_analysis) {
        this.criticalDefects = Object.entries(powerLossData.power_loss_analysis).map(
          ([name, d]: [string, any]) => ({
            name,
            value: d.mean,
            total: d.sum,
            count: d.count,
            std: d.std,
            deltaT: powerLossData.temperature_analysis?.max_delta_t?.[name] ?? null,
            avgT: powerLossData.temperature_analysis?.average_temps?.[name] ?? null,
          })
        );
      }

      this.createCharts();
      this.initializeTable();
    } catch (error: any) {
      this.toastr.error(error?.message || 'Failed to load power loss data');
    } finally {
      this.isDataLoading = false;
      this.cdr.detectChanges();
    }
  }

  formatDefectName(name: string): string {
    if (!name) return '';
    if (this.formattedDefectNames.has(name)) {
      return this.formattedDefectNames.get(name);
    }
    const formatted = name
      .replace(/_/g, ' ')
      .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
    this.formattedDefectNames.set(name, formatted);
    return formatted;
  }

  formatColumnName(column: string): string {
    const columnMappings = {
      avg_temp: 'Avg. Temp (°C)',
      power_loss: 'Power Loss (W)',
      defect_count: 'Defect Count',
      table_name: 'Table Name',
    };
    return columnMappings[column] || column.replace(/_/g, ' ');
  }

  async downloadReport(fileUrl: string) {
    await this._http.fileDownload(this.projectInfo['project_name'], fileUrl);
  }

  private createCharts() {
    if (!this.data) return;

    try {
      this.defectDistributionChart = this.createDefectDistribution();
      this.blockDefectsChart = this.createBlockDefects();
      this.powerLossScatter = this.createPowerLossScatter();
      this.temperatureComparisonChart = this.createTemperatureChart();
      this.defectSummaryChart = this.createDefectSummaryChart();
      this.temperatureHeatmap = this.createTemperatureHeatmap();
      this.defectStandardDeviationChart = this.createDefectStandardDeviationChart();
      this.boxPlotChart = this.createBoxPlot();
      this.threeDScatterChart = this.create3DScatter();
      this.parallelChart = this.createParallelCoordinates();
    } catch (error) {
      console.error('Error creating charts:', error);
    }
  }

  private initializeTable() {
    if (!this.data?.table_analysis) return;
    try {
      const tableData = Object.entries(this.data.table_analysis).map(([key, value]) => ({
        table_name: key,
        defect_count: value.defect,
        power_loss: value.power_loss.toFixed(2),
        avg_temp: value.avg_temp.toFixed(2),
      }));
      this.dataSource.data = tableData;
    } catch (error) {
      console.error('Error initializing table:', error);
    }
  }

  ngAfterViewInit() {
    if (this.dataSource && this.paginator && this.sort) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  private readonly CHART_BASE_CONFIG: AdvancedChartConfig = {
    data: [],
    layout: {
      font: { family: 'Poppins', color: '#282828' },
      title: {
        font: {
          color: 'darkcyan',
          family: 'Poppins',
          size: 20,
        },
      },
      hovermode: 'closest',
      xaxis: { showgrid: true },
      yaxis: { showgrid: true },
      autosize: true,
      automargin: true,
      margin: { l: 50, r: 25, t: 50, b: 100 },
      legend: {
        orientation: 'h',
        x: 1,
        y: 1,
        xanchor: 'right',
      },
    },
    config: {
      responsive: true,
      displayModeBar: true,
      displaylogo: false,
      modeBarButtonsToRemove: [
        'toggleSpikelines',
        'select2d',
        'lasso2d',
        'zoomIn2d',
        'zoomOut2d',
        'autoScale2d',
      ],
      modeBarButtonsToAdd: ['hoverclosest', 'hovercompare'],
    },
  };

  private createChartBase(): AdvancedChartConfig {
    return {
      data: [],
      layout: JSON.parse(JSON.stringify(this.CHART_BASE_CONFIG.layout)),
      config: JSON.parse(JSON.stringify(this.CHART_BASE_CONFIG.config)),
    };
  }

  private createDefectDistribution(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data!.overall_summary.defect_counts);

    config.data = [
      {
        values: defects.map(d => d[1]),
        labels: defects.map(d => this.formatDefectName(d[0])),
        type: 'pie',
        hole: 0.5,
        rotation: 135,
        marker: {
          line: { width: 2 },
        },
        textinfo: 'percent+label',
        hoverinfo: 'label+value+percent',
        texttemplate: '<b>%{label}</b><br>%{value} defects (%{percent})',
      },
    ];
    config.layout = {
      ...config.layout,
      title: { ...config.layout['title'], text: '<b>Defect Type Distribution Analysis</b>' },
      margin: { l: 0, r: 0, t: 50, b: 25 },
      legend: {
        ...config.layout['legend'],
        orientation: 'v',
      },
    };

    return config;
  }

  onDefectBubbleTypeChange() {
    if (!this.data) return;

    try {
      this.showBubbleChart = false;
      this.powerLossScatter = this.createPowerLossScatter();
      setTimeout(() => {
        this.showBubbleChart = true;
        this.cdr.detectChanges();
      }, 50);
    } catch (error) {
      console.error('Error updating power loss scatter chart:', error);
      this.showBubbleChart = true;
    }
  }

  private createPowerLossScatter(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data!.power_loss_analysis);
    const xValues = defects.map(([name]) =>
      this.scaleMode === 'delta'
        ? this.data!.temperature_analysis.max_delta_t[name]
        : this.data!.temperature_analysis.average_temps[name]
    );
    const yValues = defects.map(d => (this.normalizationMode === 'mean' ? d[1].mean : d[1].sum));
    const medianY = [...yValues].sort((a, b) => a - b)[Math.floor(yValues.length / 2)];

    config.data = [
      {
        x: xValues,
        y: yValues,
        mode: 'markers+text',
        type: 'scatter',
        marker: {
          size: defects.map(d => Math.sqrt(d[1].count) * 3),
          color: defects.map(d => d[1].std),
          opacity: 0.5,
          line: { width: 2 },
          showscale: true,
          colorbar: { title: 'Std Dev' },
        },
        text: defects.map(([name]) => this.formatDefectName(name)),
        textposition: yValues.map(y => (y > medianY ? 'top center' : 'bottom center')),
        hovertemplate:
          '<b>%{text}</b><br>' +
          (this.scaleMode === 'delta' ? 'Max ΔTemp: %{x}<br>' : 'Avg Temp: %{x}W<br>') +
          (this.normalizationMode === 'mean' ? 'Mean Loss: %{y}W<br>' : 'Total Loss: %{y}W<br>') +
          'Std Dev: %{marker.color}',
      },
    ];
    config.layout = {
      ...config.layout,
      title: {
        ...config.layout['title'],
        text: '<b>Thermal Impact Analysis: Temperature vs Power Loss</b>',
      },
      xaxis: {
        title:
          this.scaleMode === 'delta' ? 'Max ΔTemp Temperature (°C)' : 'Average Temperature (°C)',
      },
      yaxis: {
        title: this.normalizationMode === 'mean' ? 'Mean Power Loss (W)' : 'Total Power Loss (W)',
      },
    };

    return config;
  }

  onTempChartTypeChange() {
    if (!this.data) return;

    try {
      this.showTempChart = false;
      this.temperatureComparisonChart = this.createTemperatureChart();
      setTimeout(() => {
        this.showTempChart = true;
        this.cdr.detectChanges();
      }, 50);
    } catch (error) {
      console.error('Error updating temperature comparison chart:', error);
      this.showTempChart = true;
    }
  }

  private createTemperatureChart(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.keys(this.data!.power_loss_analysis);
    const xValues = defects.map(d => this.formatDefectName(d));

    if (this.selectedTempChartType === 'line') {
      config.data = [
        {
          x: xValues,
          y: defects.map(d => this.data!.temperature_analysis.average_temps[d]),
          name: 'Average Temp',
          type: 'scatter',
          mode: 'lines+markers',
          line: { shape: 'spline', width: 2 },
          hovertemplate: 'Avg: %{y}°C',
        },
        {
          x: xValues,
          y: defects.map(d => this.data!.temperature_analysis.max_delta_t[d]),
          name: 'Max ΔTemp',
          type: 'scatter',
          mode: 'lines+markers',
          line: { shape: 'spline', width: 2 },
          hovertemplate: 'Max ΔTemp: %{y}°C',
        },
      ];
      config.layout = {
        ...config.layout,
        title: {
          ...config.layout['title'],
          text: '<b>Temperature Extremes: Average vs Maximum Variation</b>',
        },
        xaxis: { title: 'Defect Type' },
        yaxis: { title: 'Temperature (°C)' },
        shapes: defects.map((d, index) => ({
          type: 'line',
          x0: index,
          x1: index,
          y0: this.data!.temperature_analysis.average_temps[d],
          y1: this.data!.temperature_analysis.max_delta_t[d],
          line: { width: 2 },
        })),
      };
    } else {
      config.data = [
        {
          x: xValues,
          y: defects.map(d => this.data!.temperature_analysis.average_temps[d]),
          name: 'Average Temp',
          type: 'bar',
          marker: { line: { width: 2 } },
          textposition: 'auto',
          hoverinfo: 'name+y',
        },
        {
          x: xValues,
          y: defects.map(d => this.data!.temperature_analysis.max_delta_t[d]),
          name: 'Max ΔTemp',
          type: 'bar',
          marker: { line: { width: 2 } },
          textposition: 'auto',
          hoverinfo: 'name+y',
        },
      ];
      config.layout = {
        ...config.layout,
        title: {
          ...config.layout['title'],
          text: '<b>Temperature Extremes: Average vs Maximum Variation</b>',
        },
        xaxis: { title: 'Defect Type' },
        yaxis: { title: 'Temperature (°C)' },
        barmode: this.selectedTempChartType,
      };
    }
    return config;
  }

  private createBlockDefects(): AdvancedChartConfig {
    const config = this.createChartBase();
    const blocks = Object.entries(this.data!.overall_summary.defect_by_block);

    config.data = [
      {
        x: blocks.map(b => b[1]),
        y: blocks.map(b => this.formatDefectName(b[0])),
        type: 'bar',
        orientation: 'h',
        marker: {
          color: blocks.map((_, i) => `rgba(78, 115, 223, ${0.3 + (0.7 * i) / blocks.length})`),
          line: { width: 2 },
        },
        text: blocks.map(b => b[1].toString()),
        textposition: 'auto',
        hovertemplate: '<b>%{y}</b><br>Defects: %{x}',
      },
    ];

    config.layout = {
      ...config.layout,
      title: { ...config.layout['title'], text: '<b>Block-wise Defect Concentration Analysis</b>' },
      xaxis: { title: 'Number of Defects' },
      yaxis: { title: 'Block Name' },
    };

    return config;
  }

  onDefectChartTypeChange() {
    if (!this.data) return;

    try {
      this.showDefectChart = false;
      this.defectSummaryChart = this.createDefectSummaryChart();
      setTimeout(() => {
        this.showDefectChart = true;
        this.cdr.detectChanges();
      }, 50);
    } catch (error) {
      console.error('Error updating defect summary chart:', error);
      this.showDefectChart = true;
    }
  }

  private createDefectSummaryChart(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data!.power_loss_analysis);
    const xValues = defects.map(d => this.formatDefectName(d[0]));

    if (this.selectedDefectChartType === 'dual') {
      config.data = [
        {
          x: xValues,
          y: defects.map(d => d[1].mean),
          name: 'Mean Loss',
          type: 'bar',
          marker: { line: { width: 2 } },
          hovertemplate: '<b>%{x}</b><br>Mean: %{y}W',
        },
        {
          x: xValues,
          y: defects.map(d => d[1].sum),
          name: 'Total Loss',
          type: 'scatter',
          mode: 'lines+markers',
          yaxis: 'y2',
          line: { shape: 'spline', width: 2 },
          hovertemplate: '<b>%{x}</b><br>Total: %{y}W',
        },
      ];
      config.layout = {
        ...config.layout,
        title: {
          ...config.layout['title'],
          text: '<b>Dual Perspective: Mean vs Total Power Loss Analysis</b>',
        },
        xaxis: { title: 'Defect Type' },
        yaxis: { title: 'Mean Power Loss (W)' },
        yaxis2: {
          title: 'Total Power Loss (W)',
          overlaying: 'y',
          side: 'right',
        },
        barmode: 'group',
        margin: { ...config.layout['margin'], l: 50, r: 50 },
      };
    } else if (this.selectedDefectChartType === 'mean') {
      config.data = [
        {
          x: xValues,
          y: defects.map(d => d[1].mean),
          name: 'Mean Loss',
          type: 'bar',
          marker: { line: { width: 2 } },
          hovertemplate: '<b>%{x}</b><br>Mean: %{y}W',
        },
      ];
      config.layout = {
        ...config.layout,
        title: {
          ...config.layout['title'],
          text: '<b>Mean Power Loss Analysis</b>',
        },
        xaxis: { title: 'Defect Type' },
        yaxis: { title: 'Mean Power Loss (W)' },
        margin: { ...config.layout['margin'], l: 50, r: 50 },
      };
    } else if (this.selectedDefectChartType === 'total') {
      config.data = [
        {
          x: xValues,
          y: defects.map(d => d[1].sum),
          name: 'Total Loss',
          type: 'scatter',
          mode: 'lines+markers',
          line: { shape: 'spline', width: 2 },
          hovertemplate: '<b>%{x}</b><br>Total: %{y}W',
        },
      ];
      config.layout = {
        ...config.layout,
        title: {
          ...config.layout['title'],
          text: '<b>Total Power Loss Analysis</b>',
        },
        xaxis: { title: 'Defect Type' },
        yaxis: { title: 'Total Power Loss (W)' },
        margin: { ...config.layout['margin'], l: 50, r: 50 },
      };
    }
    return config;
  }

  private createTemperatureHeatmap(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.keys(this.data!.temperature_analysis.average_temps);

    config.data = [
      {
        z: [
          defects.map(d => this.data!.temperature_analysis.average_temps[d]),
          defects.map(d => this.data!.temperature_analysis.max_delta_t[d]),
        ],
        x: defects.map(d => this.formatDefectName(d)),
        y: ['Avg. Temp', 'Max ΔTemp'],
        type: 'heatmap',
        showscale: true,
        hoverongaps: false,
        hovertemplate: '<b>%{y}</b><br>%{x}<br>%{z}°C',
      },
    ];
    config.layout = {
      ...config.layout,
      title: {
        ...config.layout['title'],
        text: '<b>Thermal Matrix: Temperature Distribution Analysis</b>',
      },
      xaxis: { title: 'Defect Type' },
      yaxis: { title: 'Temperature (°C)' },
      margin: { ...config.layout['margin'], l: 75 },
    };

    return config;
  }

  private createDefectStandardDeviationChart(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data!.power_loss_analysis);

    config.data = [
      {
        x: defects.map(d => this.formatDefectName(d[0])),
        y: defects.map(d => d[1].std),
        mode: 'markers',
        type: 'scatter',
        marker: {
          size: defects.map(d => Math.pow(d[1].std, 2) * 2),
          color: defects.map(d => d[1].std),
          showscale: true,
        },
        hovertemplate: '<b>%{x}</b><br>Std Dev: %{y}',
      },
    ];
    config.layout = {
      ...config.layout,
      title: {
        ...config.layout['title'],
        text: '<b>Power Loss Variability: Standard Deviation Analysis</b>',
      },
      xaxis: { title: 'Defect Type' },
      yaxis: { title: 'Standard Deviation' },
    };

    return config;
  }

  private create3DScatter(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data.power_loss_analysis);
    const meanValues = defects.map(d => d[1].mean);
    config.data = [
      {
        x: meanValues,
        y: defects.map(d => this.data!.temperature_analysis.average_temps[d[0]]),
        z: defects.map(d => d[1].count),
        mode: 'markers',
        type: 'scatter3d',
        marker: {
          color: meanValues.map(
            value =>
              (value - Math.min(...meanValues)) /
              (Math.max(...meanValues) - Math.min(...meanValues))
          ),
          line: { width: 2 },
        },
        text: defects.map(d => this.formatDefectName(d[0])),
        hovertemplate:
          '<b>%{text}</b><br>' +
          'Mean Loss: %{x:.2f}W<br>' +
          'Temp: %{y:.1f}°C<br>' +
          'Count: %{z}',
      },
    ];
    config.layout = {
      ...config.layout,
      title: {
        ...config.layout['title'],
        text: '<b>3D Defect Analysis</b>',
      },
      margin: { l: 0, r: 0, b: 25, t: 50 },
      scene: {
        xaxis: { title: 'Mean Loss (W)' },
        yaxis: { title: 'Temperature (°C)' },
        zaxis: { title: 'Defect Count' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 0.8 },
        },
      },
    };
    return config;
  }

  onBoxChartTypeChange() {
    if (!this.data) return;

    try {
      this.showBoxChart = false;
      this.boxPlotChart = this.createBoxPlot();
      setTimeout(() => {
        this.showBoxChart = true;
        this.cdr.detectChanges();
      }, 50);
    } catch (error) {
      console.error('Error updating box plot chart:', error);
      this.showBoxChart = true;
    }
  }

  private createBoxPlot(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data.power_loss_analysis);

    if (this.selectedChartType === 'violin') {
      config.data = defects.map(d => ({
        y: [
          d[1].mean - d[1].std * 2,
          d[1].mean - d[1].std,
          d[1].mean,
          d[1].mean + d[1].std,
          d[1].mean + d[1].std * 2,
        ],
        name: this.formatDefectName(d[0]),
        type: 'violin',
        box: { visible: true },
        meanline: { visible: true },
        points: 'outliers',
        hoverinfo: 'y+name',
        hoveron: 'violins',
      }));
    } else {
      config.data = defects.map(d => ({
        y: [
          d[1].mean - d[1].std * 2,
          d[1].mean - d[1].std,
          d[1].mean,
          d[1].mean + d[1].std,
          d[1].mean + d[1].std * 2,
        ],
        name: this.formatDefectName(d[0]),
        type: 'box',
        boxpoints: 'outliers',
        jitter: 0.5,
        marker: { size: 8 },
        line: { width: 2 },
      }));
    }

    config.layout = {
      ...config.layout,
      title: {
        ...config.layout['title'],
        text: '<b>Power Loss Distribution by Defect Type</b>',
      },
      yaxis: { title: 'Power Loss (W)' },
      legend: {
        ...config.layout['legend'],
        orientation: 'v',
      },
    };

    return config;
  }

  private createParallelCoordinates(): AdvancedChartConfig {
    const config = this.createChartBase();
    const defects = Object.entries(this.data!.power_loss_analysis);
    const dimensions = [
      {
        label: 'Defect Count',
        values: defects.map(d => d[1].count),
      },
      {
        label: 'Mean Loss (W)',
        values: defects.map(d => d[1].mean),
      },
      {
        label: 'Max ΔTemp (°C)',
        values: defects.map(d => this.data!.temperature_analysis.max_delta_t[d[0]]),
      },
      {
        label: 'Total Loss (W)',
        values: defects.map(d => d[1].sum),
      },
    ];

    config.data = [
      {
        type: 'parcoords',
        line: {
          color: defects.map(d => d[1].std),
          showscale: true,
          colorbar: {
            title: 'Std Dev',
            thickness: 20,
          },
        },
        dimensions: dimensions,
        hoverinfo: 'all',
        hoveron: 'dimensions',
      },
    ];
    config.layout = {
      ...config.layout,
      title: { ...config.layout['title'], text: '<b>Multivariate Defect Analysis</b>' },
      margin: { ...config.layout['margin'], t: 100, b: 25 },
    };
    return config;
  }

  compareData(a: KeyValue<string, any>, b: KeyValue<string, any>): number {
    try {
      return b.value.sum - a.value.sum;
    } catch {
      return b.value - a.value;
    }
  }
}
