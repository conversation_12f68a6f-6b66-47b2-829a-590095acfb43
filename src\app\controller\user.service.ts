import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DataService } from './data.service';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly ROLE_KEY = 'role';
  private readonly GROUPID_KEY = 'groupId';
  private readonly LOGO_KEY = 'logo';

  private roleSubject = new BehaviorSubject<string>(null);
  role$: Observable<string> = this.roleSubject.asObservable();

  private groupIdSubject = new BehaviorSubject<string>(null);
  groupId$: Observable<string> = this.groupIdSubject.asObservable();

  private sidebarLogoSubject = new BehaviorSubject<string>(null);
  sidebarLogo$: Observable<string> = this.sidebarLogoSubject.asObservable();

  constructor(private dataService: DataService) {
    const storedRole = this.dataService.retrieveKey(localStorage.getItem(this.ROLE_KEY));
    const storedGroupId = this.dataService.retrieveKey(localStorage.getItem(this.GROUPID_KEY));
    const storedLogo = this.dataService.retrieveKey(localStorage.getItem(this.LOGO_KEY));

    if (storedRole && storedGroupId && storedLogo) {
      this.roleSubject.next(storedRole);
      this.groupIdSubject.next(storedGroupId);
      this.sidebarLogoSubject.next(storedLogo);
    }
  }

  setUserData(groupId: string, role: string, sidebar_logo: string) {
    this.dataService.storeKey(this.GROUPID_KEY, groupId);
    this.groupIdSubject.next(groupId);

    this.dataService.storeKey(this.ROLE_KEY, role);
    this.roleSubject.next(role);

    this.dataService.storeKey(this.LOGO_KEY, sidebar_logo);
    this.sidebarLogoSubject.next(sidebar_logo);
  }

  getGroupId(): string {
    return this.groupIdSubject.value;
  }

  getRole(): string {
    return this.roleSubject.value;
  }

  getSidebarLogo(): string {
    return this.sidebarLogoSubject.value;
  }

  hasRole(role: string): boolean {
    return this.roleSubject.value === role;
  }

  resetUserData(): void {
    this.roleSubject.next(null);
    this.groupIdSubject.next(null);
    this.sidebarLogoSubject.next(null);
  }
}
