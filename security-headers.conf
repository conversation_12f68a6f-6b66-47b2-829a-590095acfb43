# This header tells the browser to only access the website over HTTPS, not HTTP.
# The max-age parameter specifies how long the browser should remember this setting.
add_header Strict-Transport-Security "max-age=31449600; includeSubDomains" always;

# This header is used to prevent cross-site scripting (XSS) and other code injection attacks by
# specifying which sources of content are allowed to be loaded by the browser when the website is accessed.
# add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://www.google.com https://www.gstatic.com https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline'; img-src 'self' https://www.google-analytics.com https://www.googletagmanager.com; font-src 'self'; frame-src 'self' https://www.google.com https://www.google.com; object-src 'none'; base-uri 'self';";
add_header Content-Security-Policy "font-src 'self' fonts.gstatic.com https://cdnjs.cloudflare.com;";
# add_header Content-Security-Policy "existing-policy; font-src 'self' fonts.gstatic.com https://cdnjs.cloudflare.com;";

add_header X-XSS-Protection "1; mode=block";

# X-Frame-Options: This header tells
# the browser not to allow the website to be displayed in an <iframe> or <frame> on another website.
add_header X-Frame-Options "DENY" always;

# X-Content-Type-Options: This header tells the browser to disable content-sniffing
# and only use the MIME type given in the Content-Type header.
add_header X-Content-Type-Options "nosniff" always;

# Referrer-Policy: This header tells the browser how to send the Referer header when following links.
# add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Referrer-Policy "strict-origin" always;

# Feature-Policy: This header tells the browser which features and APIs the website is allowed to use,
# such as the microphone, geolocation, and camera.
add_header Feature-Policy "microphone 'none'; geolocation 'none'; camera 'none'" always;

# Permissions-Policy: This header tells the browser which permissions the website is allowed
# to request for certain features and APIs
add_header Permissions-Policy "microphone=(); geolocation=(); camera=()" always;
