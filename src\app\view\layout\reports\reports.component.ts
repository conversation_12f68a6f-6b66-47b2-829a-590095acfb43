import { HttpService } from '@/view/map-section/services-map/http.service';
import { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css'],
})
export class ReportsComponent {
  block_type: string = null;
  block_type_arr: string[];
  inverter_type_arr: string[];
  file_type_arr: string[];
  inverter_type: string = null;
  file_type: string = null;
  file_url: string;
  progress = 0;
  Isworking = false;
  public fileObj: any = {};

  constructor(
    private http: HttpClient,
    private _http: HttpService,
    private toastr: ToastrService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<ReportsComponent>
  ) {
    this.fileObj = this.data.fileObj;
    this.block_type_arr = this.objectKeys(this.fileObj);
  }
  async downloadFile() {
    if (this.block_type && this.block_type == 'SUMMARY' && this.file_type) {
      this.file_url = this.fileObj[this.block_type][this.file_type];
    }
    if (this.block_type && this.block_type == 'INVERTER' && this.inverter_type && this.file_type) {
      this.file_url = this.fileObj[this.block_type][this.inverter_type][this.file_type];
    }
    if (this.file_url) {
      try {
        const urlParts = this.file_url.split('/');
        const signedUrl = await this._http.getSignedUrl(this.file_url);
        if (!signedUrl) {
          this.toastr.error('Failed to generate download link.');
          return;
        }
        this.Isworking = true;
        this.progress = 0;
        this.toastr.warning('', 'Downloading Started');
        const headers = new HttpHeaders()
          .set('Range', 'bytes=0-')
          .set('X-Skip-Authorization', 'true');
        this.http
          .get(signedUrl, {
            headers,
            observe: 'events',
            responseType: 'blob',
            reportProgress: true,
          })
          .subscribe(
            (event: HttpEvent<Blob>) => {
              switch (event.type) {
                case HttpEventType.DownloadProgress: {
                  const loaded = event.loaded || 0;
                  const total = event.total || 0;
                  this.progress = Math.round((loaded / total) * 100);
                  break;
                }
                case HttpEventType.Response: {
                  const blob = new Blob([event.body], { type: 'application/octet-stream' });
                  const link = document.createElement('a');
                  link.href = URL.createObjectURL(blob);
                  link.download = urlParts[urlParts.length - 1];
                  link.click();
                  URL.revokeObjectURL(link.href);
                  this.Isworking = false;
                  break;
                }
                default:
                  break;
              }
            },
            error => {
              console.error(error);
              this.toastr.error('', 'Downloading Failed');
            }
          );
      } catch (error) {
        this.toastr.error(error.statusText, 'Downloading Failed');
      }
    }
  }
  checkData() {
    if (this.file_type) {
      this.file_type = null;
    }
    if (this.block_type && this.block_type == 'SUMMARY') {
      this.file_type_arr = this.objectKeys(this.fileObj[this.block_type]);
    }
    if (this.block_type && this.block_type == 'INVERTER') {
      this.inverter_type_arr = this.objectKeys(this.fileObj[this.block_type]);
      if (this.inverter_type) {
        this.file_type_arr = this.objectKeys(this.fileObj[this.block_type][this.inverter_type]);
      }
    }
  }
  objectKeys(obj: any): string[] {
    if (obj == null) return [];
    return Object.keys(obj);
  }
  onClose() {
    this.dialogRef.close();
  }
}
