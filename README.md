# **Carnot UI**

## **Introduction**

To be add

---

## **Features**

- To be add

---

## **Installation**

### **Prerequisites**

Before you begin, ensure you have the following installed on your system:

- **Node.js** (v18.x or later)
- **Angular CLI** (v15.x or later)

### **Steps to Set Up**

1. Clone the repository:

   ```bash
   git clone https://github.com/DATASEE-AI/carnot-ui.git
   cd carnot-ui
   ```

2. Install project dependencies:

   ```bash
   npm install
   ```

3. Run the application locally:
   ```bash
   ng serve
   ```
   The application will be available at `http://localhost:4200`.

---

## **Usage**

### **Development**

- To start the development server:

  ```bash
  ng serve
  ```

- To build the application for production:
  ```bash
  ng build --prod
  ```

### **Scripts**

- **Formatting**:
  ```bash
  npm run prettier
  ```

---

## **Folder Structure**

Here’s an overview of the repository’s directory structure:

```plaintext
src/
│
├── app/
│   ├── controller/       # Logic to manage and handle APIs and data rednering
│   ├── directives/       # Custom directives for reusable DOM behavior
│   ├── guards/           # Route guards for navigation control
│   ├── interceptors/     # HTTP interceptors for request/response handling
│   ├── interfaces/       # TypeScript interfaces for type definitions
│   ├── library/          # Mat library config
│   ├── view/             # Component templates and associated SCSS files
│   ├── app-routing.module.ts  # Application route definitions
│   ├── app.component.*        # Root application component files
│   └── app.module.ts          # Root application module
│
├── assets/                 # Static assets such as images and fonts
├── environments/           # Environment-specific configurations
├── favicon.ico             # Application icon
├── index.html              # Main HTML entry point
├── main.ts                 # Application bootstrap logic
├── polyfills.ts            # Polyfills for browser compatibility
├── proxy.conf.json         # Proxy configuration for API requests
└── styles.scss             # Global styles and variables
├── .dockerignore             # Docker ignore configuration
├── .editorconfig             # Editor configuration for consistent code formatting
├── .eslintrc.json            # ESLint configuration for linting rules
├── .gitignore                # Git ignore configuration
├── .prettierignore           # Prettier ignore configuration
├── .prettierrc               # Prettier configuration for code formatting
├── .todo                     # Pending task tracker
├── angular.json              # Angular project settings and configurations
├── build_push_docker.sh      # Script for building and pushing Docker images
├── Dockerfile                # Docker configuration for production builds
├── Dockerfile.dev            # Docker configuration for development builds
├── extra-webpack.config.js   # Additional Webpack configuration
├── karma.conf.js             # Configuration file for Karma test runner
├── nginx.conf                # NGINX server configuration
├── package.json              # Project metadata and dependency management
├── README.md                 # Project documentation
├── security-headers.conf     # Security headers for server configuration
└── tsconfig.json             # TypeScript compiler configuration
```

---

## **Contributing**

### **Guidelines**

We encourage all team members to contribute by adhering to the following:

1. **Coding Standards**: Follow the Angular style guide and use the provided ESLint configuration for consistency.
2. **Branching Strategy**: Use feature branches (e.g., `feature/component-name`) for new features or fixes.
3. **Commit Messages**: Use clear, concise, and descriptive commit messages.

### **Code Review Process**

- Submit a pull request (PR) with a detailed description of the changes.
- Ensure all tests pass and the PR meets coding and design standards.
- The PR will be reviewed by at least one team member before merging.

---

## **License**

This repository is proprietary to **Datasee.AI** and is intended for internal use only. Redistribution or use outside the organization requires explicit permission.

---

## **Contact Information**

For questions, support, or feedback, please contact the **UI Development Team**:

- **Email**: <EMAIL>
- **Slack**: #product-development
