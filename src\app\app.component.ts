import { Component, OnInit } from '@angular/core';
import { LoadingService } from './controller/loading.service';
import { UserService } from './controller/user.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent implements OnInit {
  logo: string = '';

  constructor(
    public loadingService: LoadingService,
    private userService: UserService
  ) {}

  ngOnInit() {
    this.userService.sidebarLogo$.subscribe(logo => {
      this.logo = logo;
    });
  }
}
