import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'dashboard-map',
  templateUrl: './dashboard-map.component.html',
  styleUrls: ['./dashboard-map.component.css'],
})
export class DashboardMapComponent implements OnInit {
  @Input() get_dashboard_data: any;
  categorywise_project: any;
  category_wise_color = {
    topography: 'red',
    grading: 'yellow',
    thermography: 'green',
    vegetation: 'blue',
    'due diligence': 'ltblue',
  };
  tab_name = 'thermography';
  graph: any = {};
  graph1: any = {};
  graph2: any = {};

  ngOnInit(): void {
    this.categorywise_project = Object.keys(this.category_wise_color);
    if (this.get_dashboard_data) {
      this.plotly_graph_bar();
      this.plotly_stacked();
    }
  }
  plotly_graph_bar() {
    const values: any = Object.values(this.get_dashboard_data['dashboard_total']);
    const keys: any = Object.keys(this.get_dashboard_data['dashboard_total']);
    this.graph = {
      data: [
        {
          y: keys,
          x: values,
          type: 'bar',
          marker: {
            color: [
              '#339933',
              '#1be461',
              '#ef6c00',
              '#ffd54f',
              '#ffa64f',
              '#17a2b8',
              '#6f42c1',
              '#a78bda',
              '#117888',
              '#17a2b8',
              '#28a745',
            ],
          },
          orientation: 'h',
        },
      ],
      layout: {
        title: 'Overview of Anomalies Detected',
        margin: { r: 40, t: 90, l: 140 }, // Adjust margins as needed
      },
      config: {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
    this.graph1 = {
      data: [
        {
          values: values,
          labels: keys,
          type: 'pie',
          showlegend: false,
          hoverinfo: 'label+percent',
          textinfo: 'label+percent',
          textposition: 'inside',
          marker: {
            colors: [
              '#339933',
              '#1be461',
              '#ef6c00',
              '#ffd54f',
              '#ffa64f',
              '#17a2b8',
              '#6f42c1',
              '#a78bda',
              '#117888',
              '#17a2b8',
              '#28a745',
            ],
          },
        },
      ],
      layout: { title: 'Defect Distribution Overview', margin: { r: 40, t: 90, l: 40, b: 80 } },
      config: {
        responsive: true,
        annotations: [
          {
            font: {
              size: 14,
            },
            showarrow: false,
            text: 'GHG',
            x: 0.17,
            y: 0.5,
          },
        ],
        displayModeBar: true,
        displaylogo: false,
        modebarButtonsToAdd: ['zoomIn2d', 'zoomOut2d'],
      },
    };
  }
  plotly_stacked() {
    const jsonData = this.get_dashboard_data['project_wise_total'];
    const project_keys = Object.keys(jsonData);
    const dataByCategory = {};
    project_keys.forEach(projectName => {
      const defectsCount = jsonData[projectName];
      for (const category in defectsCount) {
        if (!dataByCategory[category]) {
          dataByCategory[category] = {
            name: category,
            data: Array(project_keys.length).fill(0),
          };
        }
        dataByCategory[category].data[project_keys.indexOf(projectName)] = defectsCount[category];
      }
    });
    const dataArray: any[] = Object.values(dataByCategory);
    // Assuming dataArray is an array of objects with 'name' and 'data' properties
    this.graph2 = {
      data: dataArray.map((categoryData, index) => ({
        x: project_keys,
        y: categoryData.data,
        type: 'bar',
        name: categoryData.name,
        marker: {
          color: [
            '#339933',
            '#1be461',
            '#ef6c00',
            '#ffd54f',
            '#ffa64f',
            '#17a2b8',
            '#6f42c1',
            '#a78bda',
            '#117888',
            '#17a2b8',
            '#28a745',
          ][index], // Use modulo to loop through colors if more bars than colors
        },
      })),
      layout: {
        autosize: true,
        title: 'Project-wise Anomaly Count',
        showlegend: false,
        margin: { r: 20, t: 90, l: 40, b: 130 },
      },
      config: {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'toggleSpikelines',
          'select2d',
          'lasso2d',
          'zoomIn2d',
          'zoomOut2d',
          'autoScale2d',
        ],
      },
    };
  }
}
