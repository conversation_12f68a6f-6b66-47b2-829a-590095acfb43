import { AuthService } from '@/controller/auth.service';
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class LogoutGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService
  ) {}
  async canActivate(): Promise<boolean> {
    const isLoggedIn = await this.authService.isLoggedIn();
    if (isLoggedIn) {
      this.router.navigate(['/app', 'home']); // Redirect logged-in users away from public pages
      return false;
    } else {
      return true; // Allow non-logged-in users to access public pages
    }
  }
}
