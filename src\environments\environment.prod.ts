export const environment = {
  production: true,
  carnot_url: 'https://api.datasee.ai:9443/carnot/api/v1/',
  ftp_url: 'https://metaupload.datasee.ai:8443/v1/',
  ftp_ui: 'https://data.datasee.ai/',
  help_center: 'https://help.datasee.ai/',
  keycloak: {
    url: 'https://auth.datasee.ai/',
    realm: 'datasee-ai',
    clientId: 'carnot',
  },
  master_key: 'e1b1f6b8c303263938cef92efed32814fd2bfb174dd6038e2197444ddf27566f',
  login_page_logo:
    'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/carnot/logo/main_logo.png',
  not_found:
    'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/carnot/logo/not-found.png',
  no_project:
    'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/carnot/logo/no-project.png',
  weather: 'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/carnot/logo/weather.png',
  client_logo: 'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/client_logos/',
  demo_video:
    'https://datasee-ai-public-assets.s3.ap-south-1.amazonaws.com/carnot/video/Explainer_Video.mp4',
  terrain_tile_layer: 'http://{s}.tile.osm.org/{z}/{x}/{y}.png',
  sattelite_tile_layer:
    'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
};
