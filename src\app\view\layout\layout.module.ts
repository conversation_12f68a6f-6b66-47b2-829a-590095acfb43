import { SharedModule } from '@/directives/shared.module';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialLibrary } from 'src/app/library/material.lib';
import { NavbarComponent } from './navbar/navbar.component';
import { ReportsComponent } from './reports/reports.component';
import { ShareComponent } from './share/share.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { VideoModalComponent } from './video-modal/video-modal.component';

@NgModule({
  declarations: [
    SidebarComponent,
    NavbarComponent,
    ShareComponent,
    ReportsComponent,
    VideoModalComponent,
  ],
  imports: [
    CommonModule,
    MaterialLibrary,
    RouterModule,
    FlexLayoutModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
  ],
  exports: [
    SidebarComponent,
    NavbarComponent,
    ShareComponent,
    ReportsComponent,
    VideoModalComponent,
  ],
})
export class LayoutModule {}
