.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.card-body {
  padding: 0.5rem 1.5rem;
}
.card-title {
  margin: 0px;
  text-align: center;
  color: var(--primary);
  font-weight: bold;
}
.col-md-12,
.col-md-6,
.col-md-4,
.col-md-3 {
  padding: 0rem 0.5rem;
}
.card-heading {
  text-align: center;
  font-size: 32px;
  font-weight: 800;
  padding: 20px;
  line-height: 4rem;
}
