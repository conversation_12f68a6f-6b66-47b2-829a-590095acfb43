.mfp-arrow, .owl-carousel, .owl-carousel .owl-item, html {-webkit-tap-highlight-color: transparent;}dl, ol, p, pre, ul {margin-top: 0;}address, dl, ol, pre, ul {margin-bottom: 1rem;}img, svg {vertical-align: middle;}body, caption {text-align: left;}dd, label {margin-bottom: 0.5rem;}pre, textarea {overflow: auto;}article, aside, figcaption, figure, footer, header, hgroup, legend, main, nav, section {display: block;}address, legend {line-height: inherit;}a:not([href]), a:not([href]):hover, legend {color: inherit;}.badge, progress, sub, sup {vertical-align: baseline;}label, output {display: inline-block;}button, hr, input {overflow: visible;}:root {--blue: #007bff;--indigo: #6610f2;--purple: #6f42c1;--pink: #e83e8c;--red: #dc3545;--orange: #fd7e14;--yellow: #ffc107;--green: #28a745;--teal: #20c997;--cyan: #17a2b8;--white: #fff;--gray: #6c757d;--gray-dark: #343a40;--primary: #007bff;--secondary: #6c757d;--success: #28a745;--info: #17a2b8;--warning: #ffc107;--danger: #dc3545;--light: #f8f9fa;--dark: #343a40;--breakpoint-xs: 0;--breakpoint-sm: 576px;--breakpoint-md: 768px;--breakpoint-lg: 992px;--breakpoint-xl: 1200px;--font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';--font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;}*, ::after, ::before {box-sizing: border-box;}html {-webkit-text-size-adjust: 100%;}body {margin: 0;font-weight: 400;background-color: #fff;}[tabindex='-1']:focus:not(:focus-visible) {outline: 0 !important;}abbr[data-original-title], abbr[title] {-webkit-text-decoration: underline dotted;text-decoration: underline dotted;cursor: help;border-bottom: 0;-webkit-text-decoration-skip-ink: none;text-decoration-skip-ink: none;}.btn, [type='button']:not(:disabled), [type='reset']:not(:disabled), [type='submit']:not(:disabled), button:not(:disabled), summary {cursor: pointer;}address {font-style: normal;}ol ol, ol ul, ul ol, ul ul {margin-bottom: 0;}dt {font-weight: 700;}dd {margin-left: 0;}blockquote, figure {margin: 0 0 1rem;}b, strong {font-weight: bolder;}small {font-size: 80%;}sub, sup {position: relative;font-size: 75%;line-height: 0;}sub {bottom: -0.25em;}sup {top: -0.5em;}a {text-decoration: none;background-color: transparent;}a:hover {text-decoration: underline;}.breadcrumb-item + .breadcrumb-item:hover::before, .btn-link, .card-link:hover, .dropdown-item.active, .dropdown-item:active, .dropdown-item:focus, .dropdown-item:hover, .list-group-item-action:focus, .list-group-item-action:hover, .nav-link:focus, .nav-link:hover, .navbar-toggler:focus, .navbar-toggler:hover, .page-link:hover, a.badge:focus, a.badge:hover, a:not([href]), a:not([href]):hover {text-decoration: none;}code, kbd, pre, samp {font-size: 1em;}img {border-style: none;}svg {overflow: hidden;}table {border-collapse: collapse;}caption {padding-top: 0.75rem;padding-bottom: 0.75rem;color: #6c757d;caption-side: bottom;}th {text-align: inherit;}button {border-radius: 0;}button, input, optgroup, select, textarea {margin: 0;font-size: inherit;line-height: inherit;}button, select {text-transform: none;}select {word-wrap: normal;}.card, code {word-wrap: break-word;}[type='button']::-moz-focus-inner, [type='reset']::-moz-focus-inner, [type='submit']::-moz-focus-inner, button::-moz-focus-inner {padding: 0;border-style: none;}input[type='checkbox'], input[type='radio'] {box-sizing: border-box;padding: 0;}textarea {resize: vertical;}fieldset {min-width: 0;padding: 0;margin: 0;border: 0;}legend {width: 100%;max-width: 100%;padding: 0;margin-bottom: 0.5rem;font-size: 1.5rem;white-space: normal;}.badge, .dropdown-header, .dropdown-item, .dropdown-toggle, .input-group-text, .navbar-brand, .progress-bar {white-space: nowrap;}[type='number']::-webkit-inner-spin-button, [type='number']::-webkit-outer-spin-button {height: auto;}[type='search'] {outline-offset: -2px;}::-webkit-file-upload-button {font: inherit;}.display-1, .display-2, .display-3, .display-4 {line-height: 1.2;}summary {display: list-item;}template {display: none;}[hidden] {display: none !important;}.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {margin-bottom: 0.5rem;font-weight: 500;line-height: 1.2;}.blockquote, hr {margin-bottom: 1rem;}.display-1, .display-2, .display-3, .display-4, .lead {font-weight: 300;}.h1, h1 {font-size: 2.5rem;}.h2, h2 {font-size: 2rem;}.h3, h3 {font-size: 1.75rem;}.close, .h4, h4 {font-size: 1.5rem;}.h5, h5 {font-size: 1.25rem;}.h6, h6 {font-size: 1rem;}.lead {font-size: 1.25rem;}.display-1 {font-size: 6rem;}.display-2 {font-size: 5.5rem;}.display-3 {font-size: 4.5rem;}.display-4 {font-size: 3.5rem;}hr {box-sizing: content-box;height: 0;margin-top: 1rem;border: 0;border-top: 1px solid rgba(0, 0, 0, 0.1);}.img-fluid, .img-thumbnail {max-width: 100%;height: auto;}.small, small {font-size: 80%;font-weight: 400;}.mark, mark {padding: 0.2em;background-color: #fcf8e3;}.list-inline, .list-unstyled {padding-left: 0;list-style: none;}.list-inline-item {display: inline-block;}.list-inline-item:not(:last-child) {margin-right: 0.5rem;}.initialism {font-size: 90%;text-transform: uppercase;}.blockquote {font-size: 1.25rem;}.blockquote-footer {display: block;font-size: 80%;color: #6c757d;}.blockquote-footer::before {content: '\2014\00A0';}.img-thumbnail {padding: 0.25rem;background-color: #fff;border: 1px solid #dee2e6;border-radius: 0.25rem;}.figure {display: inline-block;}.figure-img {margin-bottom: 0.5rem;line-height: 1;}.figure-caption {font-size: 90%;color: #6c757d;}code, kbd {font-size: 87.5%;}a > code, pre code {color: inherit;}code {color: #e83e8c;}kbd {padding: 0.2rem 0.4rem;color: #fff;background-color: #212529;border-radius: 0.2rem;}kbd kbd {padding: 0;font-size: 100%;font-weight: 700;}.container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl {padding-right: 15px;padding-left: 15px;margin-right: auto;margin-left: auto;width: 100%;}.btn, .btn-link, .custom-select, .dropdown-item, .form-control, .input-group-text {font-weight: 400;}pre {display: block;font-size: 87.5%;color: #212529;}pre code {font-size: inherit;word-break: normal;}.pre-scrollable {max-height: 340px;overflow-y: scroll;}@media (min-width: 768px) {.container {max-width: 720px;}}@media (min-width: 992px) {.container {max-width: 960px;}}@media (min-width: 1200px) {.container {max-width: 1140px;}}@media (min-width: 768px) {.container, .container-md, .container-sm {max-width: 720px;}}@media (min-width: 992px) {.container, .container-lg, .container-md, .container-sm {max-width: 960px;}}@media (min-width: 1200px) {.container, .container-lg, .container-md, .container-sm, .container-xl {max-width: 1140px;}}.row {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;margin-right: -15px;margin-left: -15px;}.no-gutters {margin-right: 0;margin-left: 0;}.no-gutters > .col, .no-gutters > [class*='col-'] {padding-right: 0;padding-left: 0;}.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto {position: relative;width: 100%;padding-right: 15px;padding-left: 15px;}.col {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;max-width: 100%;}.row-cols-1 > * {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.row-cols-2 > * {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.row-cols-3 > * {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.row-cols-4 > * {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.row-cols-5 > * {-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.row-cols-6 > * {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-auto {-ms-flex: 0 0 auto;flex: 0 0 auto;width: auto;max-width: 100%;}.col-1 {-ms-flex: 0 0 8.333333%;flex: 0 0 8.333333%;max-width: 8.333333%;}.col-2 {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-3 {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}@media (max-width: 550px) {.col-3 {max-width: 100%;flex: none;}}@media (min-width: 550px) and (max-width: 820px) {.col-3 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}}@media (min-width: 820px) and (max-width: 1100px) {.col-3 {-ms-flex: 0 0 33.33%;flex: 0 0 33.33%;max-width: 33.33%;}}.col-4 {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}@media (max-width: 700px) {.col-4 {max-width: 100%;flex: none;}}.col-5 {-ms-flex: 0 0 41.666667%;flex: 0 0 41.666667%;max-width: 41.666667%;}.col-6 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.col-7 {-ms-flex: 0 0 58.333333%;flex: 0 0 58.333333%;max-width: 58.333333%;}.col-8 {-ms-flex: 0 0 66.666667%;flex: 0 0 66.666667%;max-width: 66.666667%;}@media (max-width: 700px) {.col-8 {max-width: 100%;flex: none;}}.col-9 {-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}.col-10 {-ms-flex: 0 0 83.333333%;flex: 0 0 83.333333%;max-width: 83.333333%;}.col-11 {-ms-flex: 0 0 91.666667%;flex: 0 0 91.666667%;max-width: 91.666667%;}.col-12 {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.order-first {-ms-flex-order: -1;order: -1;}.order-last {-ms-flex-order: 13;order: 13;}.order-0 {-ms-flex-order: 0;order: 0;}.order-1 {-ms-flex-order: 1;order: 1;}.order-2 {-ms-flex-order: 2;order: 2;}.order-3 {-ms-flex-order: 3;order: 3;}.order-4 {-ms-flex-order: 4;order: 4;}.order-5 {-ms-flex-order: 5;order: 5;}.order-6 {-ms-flex-order: 6;order: 6;}.order-7 {-ms-flex-order: 7;order: 7;}.order-8 {-ms-flex-order: 8;order: 8;}.order-9 {-ms-flex-order: 9;order: 9;}.order-10 {-ms-flex-order: 10;order: 10;}.order-11 {-ms-flex-order: 11;order: 11;}.order-12 {-ms-flex-order: 12;order: 12;}.offset-1 {margin-left: 8.333333%;}.offset-2 {margin-left: 16.666667%;}.offset-3 {margin-left: 25%;}.offset-4 {margin-left: 33.333333%;}.offset-5 {margin-left: 41.666667%;}.offset-6 {margin-left: 50%;}.offset-7 {margin-left: 58.333333%;}.offset-8 {margin-left: 66.666667%;}.offset-9 {margin-left: 75%;}.offset-10 {margin-left: 83.333333%;}.offset-11 {margin-left: 91.666667%;}@media (min-width: 576px) {.col-sm {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;max-width: 100%;}.row-cols-sm-1 > * {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.row-cols-sm-2 > * {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.row-cols-sm-3 > * {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.row-cols-sm-4 > * {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.row-cols-sm-5 > * {-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.row-cols-sm-6 > * {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-sm-auto {-ms-flex: 0 0 auto;flex: 0 0 auto;width: auto;max-width: 100%;}.col-sm-1 {-ms-flex: 0 0 8.333333%;flex: 0 0 8.333333%;max-width: 8.333333%;}.col-sm-2 {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-sm-3 {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.col-sm-4 {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.col-sm-5 {-ms-flex: 0 0 41.666667%;flex: 0 0 41.666667%;max-width: 41.666667%;}.col-sm-6 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.col-sm-7 {-ms-flex: 0 0 58.333333%;flex: 0 0 58.333333%;max-width: 58.333333%;}.col-sm-8 {-ms-flex: 0 0 66.666667%;flex: 0 0 66.666667%;max-width: 66.666667%;}.col-sm-9 {-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}.col-sm-10 {-ms-flex: 0 0 83.333333%;flex: 0 0 83.333333%;max-width: 83.333333%;}.col-sm-11 {-ms-flex: 0 0 91.666667%;flex: 0 0 91.666667%;max-width: 91.666667%;}.col-sm-12 {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.order-sm-first {-ms-flex-order: -1;order: -1;}.order-sm-last {-ms-flex-order: 13;order: 13;}.order-sm-0 {-ms-flex-order: 0;order: 0;}.order-sm-1 {-ms-flex-order: 1;order: 1;}.order-sm-2 {-ms-flex-order: 2;order: 2;}.order-sm-3 {-ms-flex-order: 3;order: 3;}.order-sm-4 {-ms-flex-order: 4;order: 4;}.order-sm-5 {-ms-flex-order: 5;order: 5;}.order-sm-6 {-ms-flex-order: 6;order: 6;}.order-sm-7 {-ms-flex-order: 7;order: 7;}.order-sm-8 {-ms-flex-order: 8;order: 8;}.order-sm-9 {-ms-flex-order: 9;order: 9;}.order-sm-10 {-ms-flex-order: 10;order: 10;}.order-sm-11 {-ms-flex-order: 11;order: 11;}.order-sm-12 {-ms-flex-order: 12;order: 12;}.offset-sm-0 {margin-left: 0;}.offset-sm-1 {margin-left: 8.333333%;}.offset-sm-2 {margin-left: 16.666667%;}.offset-sm-3 {margin-left: 25%;}.offset-sm-4 {margin-left: 33.333333%;}.offset-sm-5 {margin-left: 41.666667%;}.offset-sm-6 {margin-left: 50%;}.offset-sm-7 {margin-left: 58.333333%;}.offset-sm-8 {margin-left: 66.666667%;}.offset-sm-9 {margin-left: 75%;}.offset-sm-10 {margin-left: 83.333333%;}.offset-sm-11 {margin-left: 91.666667%;}}@media (min-width: 768px) {.col-md {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;max-width: 100%;}.row-cols-md-1 > * {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.row-cols-md-2 > * {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.row-cols-md-3 > * {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.row-cols-md-4 > * {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.row-cols-md-5 > * {-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.row-cols-md-6 > * {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-md-auto {-ms-flex: 0 0 auto;flex: 0 0 auto;width: auto;max-width: 100%;}.col-md-1 {-ms-flex: 0 0 8.333333%;flex: 0 0 8.333333%;max-width: 8.333333%;}.col-md-2 {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-md-3 {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.col-md-4 {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.col-md-5 {-ms-flex: 0 0 41.666667%;flex: 0 0 41.666667%;max-width: 41.666667%;}.col-md-6 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.col-md-7 {-ms-flex: 0 0 58.333333%;flex: 0 0 58.333333%;max-width: 58.333333%;}.col-md-8 {-ms-flex: 0 0 66.666667%;flex: 0 0 66.666667%;max-width: 66.666667%;}.col-md-9 {-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}.col-md-10 {-ms-flex: 0 0 83.333333%;flex: 0 0 83.333333%;max-width: 83.333333%;}.col-md-11 {-ms-flex: 0 0 91.666667%;flex: 0 0 91.666667%;max-width: 91.666667%;}.col-md-12 {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.order-md-first {-ms-flex-order: -1;order: -1;}.order-md-last {-ms-flex-order: 13;order: 13;}.order-md-0 {-ms-flex-order: 0;order: 0;}.order-md-1 {-ms-flex-order: 1;order: 1;}.order-md-2 {-ms-flex-order: 2;order: 2;}.order-md-3 {-ms-flex-order: 3;order: 3;}.order-md-4 {-ms-flex-order: 4;order: 4;}.order-md-5 {-ms-flex-order: 5;order: 5;}.order-md-6 {-ms-flex-order: 6;order: 6;}.order-md-7 {-ms-flex-order: 7;order: 7;}.order-md-8 {-ms-flex-order: 8;order: 8;}.order-md-9 {-ms-flex-order: 9;order: 9;}.order-md-10 {-ms-flex-order: 10;order: 10;}.order-md-11 {-ms-flex-order: 11;order: 11;}.order-md-12 {-ms-flex-order: 12;order: 12;}.offset-md-0 {margin-left: 0;}.offset-md-1 {margin-left: 8.333333%;}.offset-md-2 {margin-left: 16.666667%;}.offset-md-3 {margin-left: 25%;}.offset-md-4 {margin-left: 33.333333%;}.offset-md-5 {margin-left: 41.666667%;}.offset-md-6 {margin-left: 50%;}.offset-md-7 {margin-left: 58.333333%;}.offset-md-8 {margin-left: 66.666667%;}.offset-md-9 {margin-left: 75%;}.offset-md-10 {margin-left: 83.333333%;}.offset-md-11 {margin-left: 91.666667%;}}@media (min-width: 992px) {.col-lg {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;max-width: 100%;}.row-cols-lg-1 > * {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.row-cols-lg-2 > * {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.row-cols-lg-3 > * {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.row-cols-lg-4 > * {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.row-cols-lg-5 > * {-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.row-cols-lg-6 > * {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-lg-auto {-ms-flex: 0 0 auto;flex: 0 0 auto;width: auto;max-width: 100%;}.col-lg-1 {-ms-flex: 0 0 8.333333%;flex: 0 0 8.333333%;max-width: 8.333333%;}.col-lg-2 {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-lg-3 {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.col-lg-4 {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.col-lg-5 {-ms-flex: 0 0 41.666667%;flex: 0 0 41.666667%;max-width: 41.666667%;}.col-lg-6 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.col-lg-7 {-ms-flex: 0 0 58.333333%;flex: 0 0 58.333333%;max-width: 58.333333%;}.col-lg-8 {-ms-flex: 0 0 66.666667%;flex: 0 0 66.666667%;max-width: 66.666667%;}.col-lg-9 {-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}.col-lg-10 {-ms-flex: 0 0 83.333333%;flex: 0 0 83.333333%;max-width: 83.333333%;}.col-lg-11 {-ms-flex: 0 0 91.666667%;flex: 0 0 91.666667%;max-width: 91.666667%;}.col-lg-12 {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.order-lg-first {-ms-flex-order: -1;order: -1;}.order-lg-last {-ms-flex-order: 13;order: 13;}.order-lg-0 {-ms-flex-order: 0;order: 0;}.order-lg-1 {-ms-flex-order: 1;order: 1;}.order-lg-2 {-ms-flex-order: 2;order: 2;}.order-lg-3 {-ms-flex-order: 3;order: 3;}.order-lg-4 {-ms-flex-order: 4;order: 4;}.order-lg-5 {-ms-flex-order: 5;order: 5;}.order-lg-6 {-ms-flex-order: 6;order: 6;}.order-lg-7 {-ms-flex-order: 7;order: 7;}.order-lg-8 {-ms-flex-order: 8;order: 8;}.order-lg-9 {-ms-flex-order: 9;order: 9;}.order-lg-10 {-ms-flex-order: 10;order: 10;}.order-lg-11 {-ms-flex-order: 11;order: 11;}.order-lg-12 {-ms-flex-order: 12;order: 12;}.offset-lg-0 {margin-left: 0;}.offset-lg-1 {margin-left: 8.333333%;}.offset-lg-2 {margin-left: 16.666667%;}.offset-lg-3 {margin-left: 25%;}.offset-lg-4 {margin-left: 33.333333%;}.offset-lg-5 {margin-left: 41.666667%;}.offset-lg-6 {margin-left: 50%;}.offset-lg-7 {margin-left: 58.333333%;}.offset-lg-8 {margin-left: 66.666667%;}.offset-lg-9 {margin-left: 75%;}.offset-lg-10 {margin-left: 83.333333%;}.offset-lg-11 {margin-left: 91.666667%;}}@media (min-width: 1200px) {.col-xl {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;max-width: 100%;}.row-cols-xl-1 > * {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.row-cols-xl-2 > * {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.row-cols-xl-3 > * {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.row-cols-xl-4 > * {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.row-cols-xl-5 > * {-ms-flex: 0 0 20%;flex: 0 0 20%;max-width: 20%;}.row-cols-xl-6 > * {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-xl-auto {-ms-flex: 0 0 auto;flex: 0 0 auto;width: auto;max-width: 100%;}.col-xl-1 {-ms-flex: 0 0 8.333333%;flex: 0 0 8.333333%;max-width: 8.333333%;}.col-xl-2 {-ms-flex: 0 0 16.666667%;flex: 0 0 16.666667%;max-width: 16.666667%;}.col-xl-3 {-ms-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}.col-xl-4 {-ms-flex: 0 0 33.333333%;flex: 0 0 33.333333%;max-width: 33.333333%;}.col-xl-5 {-ms-flex: 0 0 41.666667%;flex: 0 0 41.666667%;max-width: 41.666667%;}.col-xl-6 {-ms-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;}.col-xl-7 {-ms-flex: 0 0 58.333333%;flex: 0 0 58.333333%;max-width: 58.333333%;}.col-xl-8 {-ms-flex: 0 0 66.666667%;flex: 0 0 66.666667%;max-width: 66.666667%;}.col-xl-9 {-ms-flex: 0 0 75%;flex: 0 0 75%;max-width: 75%;}.col-xl-10 {-ms-flex: 0 0 83.333333%;flex: 0 0 83.333333%;max-width: 83.333333%;}.col-xl-11 {-ms-flex: 0 0 91.666667%;flex: 0 0 91.666667%;max-width: 91.666667%;}.col-xl-12 {-ms-flex: 0 0 100%;flex: 0 0 100%;max-width: 100%;}.order-xl-first {-ms-flex-order: -1;order: -1;}.order-xl-last {-ms-flex-order: 13;order: 13;}.order-xl-0 {-ms-flex-order: 0;order: 0;}.order-xl-1 {-ms-flex-order: 1;order: 1;}.order-xl-2 {-ms-flex-order: 2;order: 2;}.order-xl-3 {-ms-flex-order: 3;order: 3;}.order-xl-4 {-ms-flex-order: 4;order: 4;}.order-xl-5 {-ms-flex-order: 5;order: 5;}.order-xl-6 {-ms-flex-order: 6;order: 6;}.order-xl-7 {-ms-flex-order: 7;order: 7;}.order-xl-8 {-ms-flex-order: 8;order: 8;}.order-xl-9 {-ms-flex-order: 9;order: 9;}.order-xl-10 {-ms-flex-order: 10;order: 10;}.order-xl-11 {-ms-flex-order: 11;order: 11;}.order-xl-12 {-ms-flex-order: 12;order: 12;}.offset-xl-0 {margin-left: 0;}.offset-xl-1 {margin-left: 8.333333%;}.offset-xl-2 {margin-left: 16.666667%;}.offset-xl-3 {margin-left: 25%;}.offset-xl-4 {margin-left: 33.333333%;}.offset-xl-5 {margin-left: 41.666667%;}.offset-xl-6 {margin-left: 50%;}.offset-xl-7 {margin-left: 58.333333%;}.offset-xl-8 {margin-left: 66.666667%;}.offset-xl-9 {margin-left: 75%;}.offset-xl-10 {margin-left: 83.333333%;}.offset-xl-11 {margin-left: 91.666667%;}}.table {width: 100%;margin-bottom: 1rem;color: #212529;}.col-form-label, .form-check-label {margin-bottom: 0;}.table td, .table th {padding: 0.75rem;vertical-align: top;border-top: 1px solid #dee2e6;}.table thead th {vertical-align: bottom;border-bottom: 2px solid #dee2e6;}.table tbody + tbody {border-top: 2px solid #dee2e6;}.table-sm td, .table-sm th {padding: 0.3rem;}.table-bordered, .table-bordered td, .table-bordered th {border: 1px solid #dee2e6;}.table-bordered thead td, .table-bordered thead th {border-bottom-width: 2px;}.table-borderless tbody + tbody, .table-borderless td, .table-borderless th, .table-borderless thead th {border: 0;}.table-striped tbody tr:nth-of-type(odd) {background-color: rgba(0, 0, 0, 0.05);}.table-hover tbody tr:hover {color: #212529;background-color: rgba(0, 0, 0, 0.075);}.table-primary, .table-primary > td, .table-primary > th {background-color: #b8daff;}.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-primary thead th {border-color: #7abaff;}.table-hover .table-primary:hover, .table-hover .table-primary:hover > td, .table-hover .table-primary:hover > th {background-color: #9fcdff;}.table-secondary, .table-secondary > td, .table-secondary > th {background-color: #d6d8db;}.table-secondary tbody + tbody, .table-secondary td, .table-secondary th, .table-secondary thead th {border-color: #b3b7bb;}.table-hover .table-secondary:hover, .table-hover .table-secondary:hover > td, .table-hover .table-secondary:hover > th {background-color: #c8cbcf;}.table-success, .table-success > td, .table-success > th {background-color: #c3e6cb;}.table-success tbody + tbody, .table-success td, .table-success th, .table-success thead th {border-color: #8fd19e;}.table-hover .table-success:hover, .table-hover .table-success:hover > td, .table-hover .table-success:hover > th {background-color: #b1dfbb;}.table-info, .table-info > td, .table-info > th {background-color: #bee5eb;}.table-info tbody + tbody, .table-info td, .table-info th, .table-info thead th {border-color: #86cfda;}.table-hover .table-info:hover, .table-hover .table-info:hover > td, .table-hover .table-info:hover > th {background-color: #abdde5;}.table-warning, .table-warning > td, .table-warning > th {background-color: #ffeeba;}.table-warning tbody + tbody, .table-warning td, .table-warning th, .table-warning thead th {border-color: #ffdf7e;}.table-hover .table-warning:hover, .table-hover .table-warning:hover > td, .table-hover .table-warning:hover > th {background-color: #ffe8a1;}.table-danger, .table-danger > td, .table-danger > th {background-color: #f5c6cb;}.table-danger tbody + tbody, .table-danger td, .table-danger th, .table-danger thead th {border-color: #ed969e;}.table-hover .table-danger:hover, .table-hover .table-danger:hover > td, .table-hover .table-danger:hover > th {background-color: #f1b0b7;}.table-light, .table-light > td, .table-light > th {background-color: #fdfdfe;}.table-light tbody + tbody, .table-light td, .table-light th, .table-light thead th {border-color: #fbfcfc;}.table-hover .table-light:hover, .table-hover .table-light:hover > td, .table-hover .table-light:hover > th {background-color: #ececf6;}.table-dark, .table-dark > td, .table-dark > th {background-color: #c6c8ca;}.table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark thead th {border-color: #95999c;}.table-hover .table-dark:hover, .table-hover .table-dark:hover > td, .table-hover .table-dark:hover > th {background-color: #b9bbbe;}.table-active, .table-active > td, .table-active > th, .table-hover .table-active:hover, .table-hover .table-active:hover > td, .table-hover .table-active:hover > th {background-color: rgba(0, 0, 0, 0.075);}.table .thead-dark th {color: #fff;background-color: #343a40;border-color: #454d55;}.table .thead-light th {color: #495057;background-color: #e9ecef;border-color: #dee2e6;}.table-dark {color: #fff;background-color: #343a40;}.table-dark td, .table-dark th, .table-dark thead th {border-color: #454d55;}.table-dark.table-bordered, .table-responsive > .table-bordered {border: 0;}.table-dark.table-striped tbody tr:nth-of-type(odd) {background-color: rgba(255, 255, 255, 0.05);}.table-dark.table-hover tbody tr:hover {color: #fff;background-color: rgba(255, 255, 255, 0.075);}@media (max-width: 575.98px) {.table-responsive-sm {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch;}.table-responsive-sm > .table-bordered {border: 0;}}@media (max-width: 767.98px) {.table-responsive-md {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch;}.table-responsive-md > .table-bordered {border: 0;}}@media (max-width: 991.98px) {.table-responsive-lg {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch;}.table-responsive-lg > .table-bordered {border: 0;}}@media (max-width: 1199.98px) {.table-responsive-xl {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch;}.table-responsive-xl > .table-bordered {border: 0;}}.table-responsive {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch;}.accordion > .card, .collapsing, .modal-open, .progress, .progress-bar, .toast {overflow: hidden;}.form-control {display: block;width: 100%;height: calc(1.5em + 0.75rem + 2px);background-color: #fff;background-clip: padding-box;border-radius: 0.25rem;transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce) {.form-control {transition: none;}}.form-control::-ms-expand {background-color: transparent;border: 0;}.form-control:-moz-focusring {color: transparent;text-shadow: 0 0 0 #495057;}.form-control:focus {color: #495057;background-color: #fff;}.form-control::-webkit-input-placeholder {color: #6c757d;opacity: 1;}.form-control::-moz-placeholder {color: #6c757d;opacity: 1;}.form-control:-ms-input-placeholder {color: #6c757d;opacity: 1;}.form-control::-ms-input-placeholder {color: #6c757d;opacity: 1;}.form-control::placeholder {color: #6c757d;opacity: 1;}.form-control:disabled, .form-control[readonly] {background-color: #e9ecef;opacity: 1;}select.form-control:focus::-ms-value {color: #495057;background-color: #fff;}.form-control-file, .form-control-range {display: block;width: 100%;}.col-form-label {padding-top: calc(0.375rem + 1px);padding-bottom: calc(0.375rem + 1px);font-size: inherit;line-height: 1.5;}.col-form-label-lg {padding-top: calc(0.5rem + 1px);padding-bottom: calc(0.5rem + 1px);font-size: 1.25rem;line-height: 1.5;}.col-form-label-sm {padding-top: calc(0.25rem + 1px);padding-bottom: calc(0.25rem + 1px);font-size: 0.875rem;line-height: 1.5;}.form-control-plaintext {display: block;width: 100%;padding: 0.375rem 0;margin-bottom: 0;font-size: 1rem;line-height: 1.5;color: #212529;background-color: transparent;border: solid transparent;border-width: 1px 0;}.form-control-plaintext.form-control-lg, .form-control-plaintext.form-control-sm {padding-right: 0;padding-left: 0;}.form-control-sm {height: calc(1.5em + 0.5rem + 2px);padding: 0.25rem 0.5rem;font-size: 0.875rem;line-height: 1.5;border-radius: 0.2rem;}.form-control-lg {height: calc(1.5em + 1rem + 2px);padding: 0.5rem 1rem;font-size: 1.25rem;line-height: 1.5;border-radius: 0.3rem;}select.form-control[multiple], select.form-control[size], textarea.form-control {height: auto;}.form-text {display: block;margin-top: 0.25rem;}.form-row {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;margin-right: -5px;margin-left: -5px;}.form-row > .col, .form-row > [class*='col-'] {padding-right: 5px;padding-left: 5px;}.form-check {position: relative;display: block;padding-left: 1.25rem;}.form-check-input {position: absolute;margin-top: 0.3rem;margin-left: -1.25rem;}.form-check-input:disabled ~ .form-check-label, .form-check-input[disabled] ~ .form-check-label {color: #6c757d;}.form-check-inline {display: -ms-inline-flexbox;display: inline-flex;-ms-flex-align: center;align-items: center;padding-left: 0;margin-right: 0.75rem;}.form-check-inline .form-check-input {position: static;margin-top: 0;margin-right: 0.3125rem;margin-left: 0;}.invalid-tooltip, .valid-tooltip {position: absolute;z-index: 5;max-width: 100%;border-radius: 0.25rem;top: 100%;line-height: 1.5;}.valid-feedback {display: none;width: 100%;margin-top: 0.25rem;font-size: 80%;color: #28a745;}.valid-tooltip {display: none;padding: 0.25rem 0.5rem;margin-top: 0.1rem;font-size: 0.875rem;color: #fff;background-color: rgba(40, 167, 69, 0.9);}.form-check-input.is-valid ~ .valid-feedback, .form-check-input.is-valid ~ .valid-tooltip, .is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip, .was-validated .form-check-input:valid ~ .valid-feedback, .was-validated .form-check-input:valid ~ .valid-tooltip, .was-validated :valid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip {display: block;}.custom-control-input.is-valid ~ .custom-control-label, .form-check-input.is-valid ~ .form-check-label, .was-validated .custom-control-input:valid ~ .custom-control-label, .was-validated .form-check-input:valid ~ .form-check-label {color: #28a745;}.form-control.is-valid, .was-validated .form-control:valid {border-color: #28a745;padding-right: calc(1.5em + 0.75rem);background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-repeat: no-repeat;background-position: right calc(0.375em + 0.1875rem) center;background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.form-control.is-valid:focus, .was-validated .form-control:valid:focus {border-color: #28a745;box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.was-validated textarea.form-control:valid, textarea.form-control.is-valid {padding-right: calc(1.5em + 0.75rem);background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.custom-select.is-valid, .was-validated .custom-select:valid {border-color: #28a745;padding-right: calc(0.75em + 2.3125rem);background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") center right 1.75rem / calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) no-repeat #fff;}.custom-select.is-valid:focus, .was-validated .custom-select:valid:focus {border-color: #28a745;box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.custom-control-input.is-valid ~ .custom-control-label::before, .was-validated .custom-control-input:valid ~ .custom-control-label::before {border-color: #28a745;}.custom-control-input.is-valid:checked ~ .custom-control-label::before, .was-validated .custom-control-input:valid:checked ~ .custom-control-label::before {border-color: #34ce57;background-color: #34ce57;}.custom-control-input.is-valid:focus ~ .custom-control-label::before, .was-validated .custom-control-input:valid:focus ~ .custom-control-label::before {box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before, .custom-file-input.is-valid ~ .custom-file-label, .was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .was-validated .custom-file-input:valid ~ .custom-file-label {border-color: #28a745;}.custom-file-input.is-valid:focus ~ .custom-file-label, .was-validated .custom-file-input:valid:focus ~ .custom-file-label {border-color: #28a745;box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.invalid-feedback {display: none;width: 100%;margin-top: 0.25rem;font-size: 80%;color: #dc3545;}.invalid-tooltip {display: none;padding: 0.25rem 0.5rem;margin-top: 0.1rem;font-size: 0.875rem;color: #fff;background-color: rgba(220, 53, 69, 0.9);}.collapsing, .dropdown, .dropleft, .dropright, .dropup {position: relative;}.form-check-input.is-invalid ~ .invalid-feedback, .form-check-input.is-invalid ~ .invalid-tooltip, .is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip, .was-validated .form-check-input:invalid ~ .invalid-feedback, .was-validated .form-check-input:invalid ~ .invalid-tooltip, .was-validated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-tooltip {display: block;}.custom-control-input.is-invalid ~ .custom-control-label, .form-check-input.is-invalid ~ .form-check-label, .was-validated .custom-control-input:invalid ~ .custom-control-label, .was-validated .form-check-input:invalid ~ .form-check-label {color: #dc3545;}.form-control.is-invalid, .was-validated .form-control:invalid {border-color: #dc3545;padding-right: calc(1.5em + 0.75rem);background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-repeat: no-repeat;background-position: right calc(0.375em + 0.1875rem) center;background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus {border-color: #dc3545;box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {padding-right: calc(1.5em + 0.75rem);background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.custom-select.is-invalid, .was-validated .custom-select:invalid {border-color: #dc3545;padding-right: calc(0.75em + 2.3125rem);background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") center right 1.75rem / calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) no-repeat #fff;}.custom-select.is-invalid:focus, .was-validated .custom-select:invalid:focus {border-color: #dc3545;box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.btn, .btn-warning, .btn:hover {color: #212529;}.custom-control-input.is-invalid ~ .custom-control-label::before, .was-validated .custom-control-input:invalid ~ .custom-control-label::before {border-color: #dc3545;}.custom-control-input.is-invalid:checked ~ .custom-control-label::before, .was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before {border-color: #e4606d;background-color: #e4606d;}.custom-control-input.is-invalid:focus ~ .custom-control-label::before, .was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before {box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-file-input.is-invalid ~ .custom-file-label, .was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .was-validated .custom-file-input:invalid ~ .custom-file-label {border-color: #dc3545;}.custom-file-input.is-invalid:focus ~ .custom-file-label, .was-validated .custom-file-input:invalid:focus ~ .custom-file-label {border-color: #dc3545;box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.btn.focus, .btn:focus, .custom-control-input:focus ~ .custom-control-label::before {box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.form-inline {display: -ms-flexbox;display: flex;-ms-flex-flow: row wrap;flex-flow: row wrap;-ms-flex-align: center;align-items: center;}.form-inline .form-check {width: 100%;}@media (min-width: 576px) {.form-inline label {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;-ms-flex-pack: center;justify-content: center;margin-bottom: 0;}.form-inline .form-group {display: -ms-flexbox;display: flex;-ms-flex: 0 0 auto;flex: 0 0 auto;-ms-flex-flow: row wrap;flex-flow: row wrap;-ms-flex-align: center;align-items: center;margin-bottom: 0;}.form-inline .form-control {display: inline-block;width: auto;vertical-align: middle;}.form-inline .form-control-plaintext {display: inline-block;}.form-inline .custom-select, .form-inline .input-group {width: auto;}.form-inline .form-check {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;-ms-flex-pack: center;justify-content: center;width: auto;padding-left: 0;}.form-inline .form-check-input {position: relative;-ms-flex-negative: 0;flex-shrink: 0;margin-top: 0;margin-right: 0.25rem;margin-left: 0;}.form-inline .custom-control {-ms-flex-align: center;align-items: center;-ms-flex-pack: center;justify-content: center;}.form-inline .custom-control-label {margin-bottom: 0;}}.btn-block, input[type='button'].btn-block, input[type='reset'].btn-block, input[type='submit'].btn-block {width: 100%;}.btn {display: inline-block;text-align: center;vertical-align: middle;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;background-color: transparent;transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}.dropdown-toggle::after, .dropup .dropdown-toggle::after {vertical-align: 0.255em;content: '';}@media (prefers-reduced-motion: reduce) {.btn {transition: none;}}.btn.focus, .btn:focus {outline: 0;}.btn-primary.focus, .btn-primary:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);}.btn.disabled, .btn:disabled {opacity: 0.65;}a.btn.disabled, fieldset:disabled a.btn {pointer-events: none;}.btn-primary {background-color: #007bff;}.btn-primary:hover {background-color: #0069d9;}.btn-primary.focus, .btn-primary:focus {color: #fff;background-color: #0069d9;border-color: #0062cc;}.btn-primary.disabled, .btn-primary:disabled {color: #fff;background-color: #007bff;border-color: #007bff;}.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle {color: #fff;background-color: #0062cc;border-color: #005cbf;}.btn-secondary.focus, .btn-secondary:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);}.btn-secondary {color: #fff;background-color: #6c757d;border-color: #6c757d;}.btn-secondary.focus, .btn-secondary:focus, .btn-secondary:hover {background-color: #5a6268;border-color: #545b62;color: #fff;}.btn-secondary.disabled, .btn-secondary:disabled {color: #fff;background-color: #6c757d;border-color: #6c757d;}.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show > .btn-secondary.dropdown-toggle {color: #fff;background-color: #545b62;border-color: #4e555b;}.btn-success.focus, .btn-success:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show > .btn-success.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);}.btn-success {color: #fff;background-color: #28a745;border-color: #28a745;}.btn-success.focus, .btn-success:focus, .btn-success:hover {background-color: #218838;border-color: #1e7e34;color: #fff;}.btn-success.disabled, .btn-success:disabled {color: #fff;background-color: #28a745;border-color: #28a745;}.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active, .show > .btn-success.dropdown-toggle {color: #fff;background-color: #1e7e34;border-color: #1c7430;}.btn-info.focus, .btn-info:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus, .show > .btn-info.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);}.btn-info {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}.btn-info.focus, .btn-info:focus, .btn-info:hover {background-color: #138496;border-color: #117a8b;color: #fff;}.btn-info.disabled, .btn-info:disabled {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active, .show > .btn-info.dropdown-toggle {color: #fff;background-color: #117a8b;border-color: #10707f;}.btn-warning.focus, .btn-warning:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-warning.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);}.btn-warning {background-color: #ffc107;border-color: #ffc107;}.btn-warning.focus, .btn-warning:focus, .btn-warning:hover {background-color: #e0a800;border-color: #d39e00;color: #212529;}.btn-warning.disabled, .btn-warning:disabled {color: #212529;background-color: #ffc107;border-color: #ffc107;}.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active, .show > .btn-warning.dropdown-toggle {color: #212529;background-color: #d39e00;border-color: #c69500;}.btn-danger.focus, .btn-danger:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-danger.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);}.btn-danger {color: #fff;background-color: #dc3545;border-color: #dc3545;}.btn-danger.focus, .btn-danger:focus, .btn-danger:hover {background-color: #c82333;border-color: #bd2130;color: #fff;}.btn-danger.disabled, .btn-danger:disabled {color: #fff;background-color: #dc3545;border-color: #dc3545;}.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show > .btn-danger.dropdown-toggle {color: #fff;background-color: #bd2130;border-color: #b21f2d;}.btn-light.focus, .btn-light:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disabled):not(.disabled):active:focus, .show > .btn-light.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);}.btn-light {color: #212529;background-color: #f8f9fa;border-color: #f8f9fa;}.btn-light.focus, .btn-light:focus, .btn-light:hover {background-color: #e2e6ea;border-color: #dae0e5;color: #212529;}.btn-light.disabled, .btn-light:disabled {color: #212529;background-color: #f8f9fa;border-color: #f8f9fa;}.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active, .show > .btn-light.dropdown-toggle {color: #212529;background-color: #dae0e5;border-color: #d3d9df;}.btn-dark.focus, .btn-dark:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-dark.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);}.btn-dark {color: #fff;background-color: #343a40;border-color: #343a40;}.btn-dark.focus, .btn-dark:focus, .btn-dark:hover {background-color: #23272b;border-color: #1d2124;color: #fff;}.btn-dark.disabled, .btn-dark:disabled {color: #fff;background-color: #343a40;border-color: #343a40;}.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active, .show > .btn-dark.dropdown-toggle {color: #fff;background-color: #1d2124;border-color: #171a1d;}.btn-outline-primary.focus, .btn-outline-primary:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);}.btn-outline-primary {color: #007bff;border-color: #007bff;}.btn-outline-primary:hover {color: #fff;background-color: #007bff;border-color: #007bff;}.btn-outline-primary.disabled, .btn-outline-primary:disabled {color: #007bff;background-color: transparent;}.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show > .btn-outline-primary.dropdown-toggle {color: #fff;background-color: #007bff;border-color: #007bff;}.btn-outline-secondary.focus, .btn-outline-secondary:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);}.btn-outline-secondary {color: #6c757d;border-color: #6c757d;}.btn-outline-secondary:hover {color: #fff;background-color: #6c757d;border-color: #6c757d;}.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {color: #6c757d;background-color: transparent;}.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle {color: #fff;background-color: #6c757d;border-color: #6c757d;}.btn-outline-success.focus, .btn-outline-success:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-success.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);}.btn-outline-success {color: #28a745;border-color: #28a745;}.btn-outline-success:hover {color: #fff;background-color: #28a745;border-color: #28a745;}.btn-outline-success.disabled, .btn-outline-success:disabled {color: #28a745;background-color: transparent;}.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active, .show > .btn-outline-success.dropdown-toggle {color: #fff;background-color: #28a745;border-color: #28a745;}.btn-outline-info.focus, .btn-outline-info:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-info.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);}.btn-outline-info {color: #17a2b8;border-color: #17a2b8;}.btn-outline-info:hover {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}.btn-outline-info.disabled, .btn-outline-info:disabled {color: #17a2b8;background-color: transparent;}.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active, .show > .btn-outline-info.dropdown-toggle {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}.btn-outline-warning.focus, .btn-outline-warning:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);}.btn-outline-warning {color: #ffc107;border-color: #ffc107;}.btn-outline-warning:hover {color: #212529;background-color: #ffc107;border-color: #ffc107;}.btn-outline-warning.disabled, .btn-outline-warning:disabled {color: #ffc107;background-color: transparent;}.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active, .show > .btn-outline-warning.dropdown-toggle {color: #212529;background-color: #ffc107;border-color: #ffc107;}.btn-outline-danger.focus, .btn-outline-danger:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);}.btn-outline-danger {color: #dc3545;border-color: #dc3545;}.btn-outline-danger:hover {color: #fff;background-color: #dc3545;border-color: #dc3545;}.btn-outline-danger.disabled, .btn-outline-danger:disabled {color: #dc3545;background-color: transparent;}.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active, .show > .btn-outline-danger.dropdown-toggle {color: #fff;background-color: #dc3545;border-color: #dc3545;}.btn-outline-light.focus, .btn-outline-light:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .btn-outline-light:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-light.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);}.btn-outline-light {color: #f8f9fa;border-color: #f8f9fa;}.btn-outline-light:hover {color: #212529;background-color: #f8f9fa;border-color: #f8f9fa;}.btn-outline-light.disabled, .btn-outline-light:disabled {color: #f8f9fa;background-color: transparent;}.btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active, .show > .btn-outline-light.dropdown-toggle {color: #212529;background-color: #f8f9fa;border-color: #f8f9fa;}.btn-outline-dark.focus, .btn-outline-dark:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);}.btn-outline-dark {color: #343a40;border-color: #343a40;}.btn-outline-dark:hover {color: #fff;background-color: #343a40;border-color: #343a40;}.btn-outline-dark.disabled, .btn-outline-dark:disabled {color: #343a40;background-color: transparent;}.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active, .show > .btn-outline-dark.dropdown-toggle {color: #fff;background-color: #343a40;border-color: #343a40;}.btn-link:hover {text-decoration: underline;}.btn-link.focus, .btn-link:focus {text-decoration: underline;box-shadow: none;}.btn-link.disabled, .btn-link:disabled {color: #6c757d;pointer-events: none;}.btn-group-lg > .btn, .btn-lg {padding: 0.5rem 1rem;font-size: 1.25rem;line-height: 1.5;border-radius: 0.3rem;}.btn-group-sm > .btn, .btn-sm {padding: 0.25rem 0.5rem;font-size: 0.875rem;line-height: 1.5;border-radius: 0.2rem;}.btn-block {display: block;}.btn-block + .btn-block {margin-top: 0.5rem;}.fade {transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce) {.fade {transition: none;}}.fade:not(.show) {opacity: 0;}.collapse:not(.show) {display: none;}.collapsing {height: 0;transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce) {.collapsing {transition: none;}}.dropdown-toggle::after {display: inline-block;margin-left: 0.255em;border-top: 0.3em solid;border-right: 0.3em solid transparent;border-bottom: 0;border-left: 0.3em solid transparent;}.dropdown-toggle:empty::after {margin-left: 0;}.dropdown-menu {position: absolute;top: 100%;left: 0;z-index: 1000;display: none;float: left;min-width: 10rem;padding: 0.5rem 0;margin: 0.125rem 0 0;font-size: 1rem;color: #212529;text-align: left;list-style: none;background-color: #fff;background-clip: padding-box;border: 1px solid rgba(0, 0, 0, 0.15);border-radius: 0.25rem;}.dropdown-menu-left {right: auto;left: 0;}.dropdown-menu-right {right: 0;left: auto;}@media (min-width: 576px) {.dropdown-menu-sm-left {right: auto;left: 0;}.dropdown-menu-sm-right {right: 0;left: auto;}}@media (min-width: 768px) {.dropdown-menu-md-left {right: auto;left: 0;}.dropdown-menu-md-right {right: 0;left: auto;}}@media (min-width: 992px) {.dropdown-menu-lg-left {right: auto;left: 0;}.dropdown-menu-lg-right {right: 0;left: auto;}}@media (min-width: 1200px) {.dropdown-menu-xl-left {right: auto;left: 0;}.dropdown-menu-xl-right {right: 0;left: auto;}}.dropup .dropdown-menu {top: auto;bottom: 100%;margin-top: 0;margin-bottom: 0.125rem;}.dropup .dropdown-toggle::after {display: inline-block;margin-left: 0.255em;border-top: 0;border-right: 0.3em solid transparent;border-bottom: 0.3em solid;border-left: 0.3em solid transparent;}.dropleft .dropdown-toggle::before, .dropright .dropdown-toggle::after {content: '';border-top: 0.3em solid transparent;border-bottom: 0.3em solid transparent;}.dropup .dropdown-toggle:empty::after {margin-left: 0;}.dropright .dropdown-menu {top: 0;right: auto;left: 100%;margin-top: 0;margin-left: 0.125rem;}.dropright .dropdown-toggle::after {display: inline-block;margin-left: 0.255em;border-right: 0;border-left: 0.3em solid;vertical-align: 0;}.dropright .dropdown-toggle:empty::after {margin-left: 0;}.dropleft .dropdown-menu {top: 0;right: 100%;left: auto;margin-top: 0;margin-right: 0.125rem;}.dropleft .dropdown-toggle::after {margin-left: 0.255em;vertical-align: 0.255em;content: '';display: none;}.dropleft .dropdown-toggle::before {display: inline-block;margin-right: 0.255em;border-right: 0.3em solid;vertical-align: 0;}.dropleft .dropdown-toggle:empty::after {margin-left: 0;}.dropdown-menu[x-placement^='bottom'], .dropdown-menu[x-placement^='left'], .dropdown-menu[x-placement^='right'], .dropdown-menu[x-placement^='top'] {right: auto;bottom: auto;}.dropdown-divider {height: 0;margin: 0.5rem 0;overflow: hidden;border-top: 1px solid #e9ecef;}.btn-group-toggle > .btn, .btn-group-toggle > .btn-group > .btn, .custom-control-label, .custom-file, .dropdown-header, .input-group-text, .nav {margin-bottom: 0;}.dropdown-item {display: block;width: 100%;padding: 0.25rem 1.5rem;clear: both;color: #212529;text-align: inherit;background-color: transparent;border: 0;}.btn-group > .btn-group:not(:last-child) > .btn, .btn-group > .btn:not(:last-child):not(.dropdown-toggle), .input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after, .input-group > .custom-select:not(:last-child), .input-group > .form-control:not(:last-child) {border-top-right-radius: 0;border-bottom-right-radius: 0;}.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:not(:first-child), .input-group > .custom-file:not(:first-child) .custom-file-label, .input-group > .custom-select:not(:first-child), .input-group > .form-control:not(:first-child) {border-top-left-radius: 0;border-bottom-left-radius: 0;}.dropdown-item:focus, .dropdown-item:hover {color: #16181b;background-color: #f8f9fa;}.dropdown-item.active, .dropdown-item:active {color: #fff;background-color: #007bff;}.dropdown-item.disabled, .dropdown-item:disabled {color: #6c757d;pointer-events: none;background-color: transparent;}.dropdown-menu.show {display: block;}.dropdown-header {display: block;padding: 0.5rem 1.5rem;font-size: 0.875rem;color: #6c757d;}.dropdown-item-text {display: block;padding: 0.25rem 1.5rem;color: #212529;}.btn-group, .btn-group-vertical {position: relative;display: -ms-inline-flexbox;display: inline-flex;vertical-align: middle;}.btn-group-vertical > .btn, .btn-group > .btn {position: relative;-ms-flex: 1 1 auto;flex: 1 1 auto;}.btn-group-vertical > .btn.active, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:hover, .btn-group > .btn.active, .btn-group > .btn:active, .btn-group > .btn:focus, .btn-group > .btn:hover {z-index: 1;}.btn-toolbar {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;-ms-flex-pack: start;justify-content: flex-start;}.btn-toolbar .input-group {width: auto;}.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-child) {margin-left: -1px;}.dropdown-toggle-split {padding-right: 0.5625rem;padding-left: 0.5625rem;}.dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after {margin-left: 0;}.input-group-append, .input-group-append .btn + .btn, .input-group-append .btn + .input-group-text, .input-group-append .input-group-text + .btn, .input-group-append .input-group-text + .input-group-text, .input-group-prepend .btn + .btn, .input-group-prepend .btn + .input-group-text, .input-group-prepend .input-group-text + .btn, .input-group-prepend .input-group-text + .input-group-text, .input-group > .custom-file + .custom-file, .input-group > .custom-file + .custom-select, .input-group > .custom-file + .form-control, .input-group > .custom-select + .custom-file, .input-group > .custom-select + .custom-select, .input-group > .custom-select + .form-control, .input-group > .form-control + .custom-file, .input-group > .form-control + .custom-select, .input-group > .form-control + .form-control, .input-group > .form-control-plaintext + .custom-file, .input-group > .form-control-plaintext + .custom-select, .input-group > .form-control-plaintext + .form-control {margin-left: -1px;}.dropleft .dropdown-toggle-split::before {margin-right: 0;}.btn-group-sm > .btn + .dropdown-toggle-split, .btn-sm + .dropdown-toggle-split {padding-right: 0.375rem;padding-left: 0.375rem;}.btn-group-lg > .btn + .dropdown-toggle-split, .btn-lg + .dropdown-toggle-split {padding-right: 0.75rem;padding-left: 0.75rem;}.btn-group-vertical {-ms-flex-direction: column;flex-direction: column;-ms-flex-align: start;align-items: flex-start;-ms-flex-pack: center;justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group {width: 100%;}.btn-group-vertical > .btn-group:not(:first-child), .btn-group-vertical > .btn:not(:first-child) {margin-top: -1px;}.btn-group-vertical > .btn-group:not(:last-child) > .btn, .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {border-bottom-right-radius: 0;border-bottom-left-radius: 0;}.btn-group-vertical > .btn-group:not(:first-child) > .btn, .btn-group-vertical > .btn:not(:first-child) {border-top-left-radius: 0;border-top-right-radius: 0;}.btn-group-toggle > .btn input[type='checkbox'], .btn-group-toggle > .btn input[type='radio'], .btn-group-toggle > .btn-group > .btn input[type='checkbox'], .btn-group-toggle > .btn-group > .btn input[type='radio'] {position: absolute;clip: rect(0, 0, 0, 0);pointer-events: none;}.input-group {position: relative;display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;-ms-flex-align: stretch;align-items: stretch;width: 100%;}.input-group > .custom-file, .input-group > .custom-select, .input-group > .form-control, .input-group > .form-control-plaintext {position: relative;-ms-flex: 1 1 0%;flex: 1 1 0%;min-width: 0;margin-bottom: 0;}.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label, .input-group > .custom-select:focus, .input-group > .form-control:focus {z-index: 3;}.input-group > .custom-file .custom-file-input:focus {z-index: 4;}.input-group > .custom-file {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;}.input-group-append, .input-group-prepend {display: -ms-flexbox;display: flex;}.input-group-append .btn, .input-group-prepend .btn {position: relative;z-index: 2;}.input-group-append .btn:focus, .input-group-prepend .btn:focus {z-index: 3;}.input-group-prepend {margin-right: -1px;}.input-group-text {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;padding: 0.375rem 0.75rem;font-size: 1rem;line-height: 1.5;color: #495057;text-align: center;background-color: #e9ecef;border: 1px solid #ced4da;border-radius: 0.25rem;}.input-group-text input[type='checkbox'], .input-group-text input[type='radio'] {margin-top: 0;}.input-group-lg > .custom-select, .input-group-lg > .form-control:not(textarea) {height: calc(1.5em + 1rem + 2px);}.input-group-lg > .custom-select, .input-group-lg > .form-control, .input-group-lg > .input-group-append > .btn, .input-group-lg > .input-group-append > .input-group-text, .input-group-lg > .input-group-prepend > .btn, .input-group-lg > .input-group-prepend > .input-group-text {padding: 0.5rem 1rem;font-size: 1.25rem;line-height: 1.5;border-radius: 0.3rem;}.input-group-sm > .custom-select, .input-group-sm > .form-control:not(textarea) {height: calc(1.5em + 0.5rem + 2px);}.input-group-sm > .custom-select, .input-group-sm > .form-control, .input-group-sm > .input-group-append > .btn, .input-group-sm > .input-group-append > .input-group-text, .input-group-sm > .input-group-prepend > .btn, .input-group-sm > .input-group-prepend > .input-group-text {padding: 0.25rem 0.5rem;font-size: 0.875rem;line-height: 1.5;border-radius: 0.2rem;}.input-group-lg > .custom-select, .input-group-sm > .custom-select {padding-right: 1.75rem;}.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group > .input-group-append:last-child > .input-group-text:not(:last-child), .input-group > .input-group-append:not(:last-child) > .btn, .input-group > .input-group-append:not(:last-child) > .input-group-text, .input-group > .input-group-prepend > .btn, .input-group > .input-group-prepend > .input-group-text {border-top-right-radius: 0;border-bottom-right-radius: 0;}.input-group > .input-group-append > .btn, .input-group > .input-group-append > .input-group-text, .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child), .input-group > .input-group-prepend:not(:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child) > .input-group-text {border-top-left-radius: 0;border-bottom-left-radius: 0;}.custom-control {position: relative;display: block;min-height: 1.5rem;padding-left: 1.5rem;}.custom-control-inline {display: -ms-inline-flexbox;display: inline-flex;margin-right: 1rem;}.custom-control-input {position: absolute;left: 0;z-index: -1;width: 1rem;height: 1.25rem;opacity: 0;}.custom-control-input:checked ~ .custom-control-label::before {color: #fff;border-color: #007bff;background-color: #007bff;}.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {border-color: #80bdff;}.custom-control-input:not(:disabled):active ~ .custom-control-label::before {color: #fff;background-color: #b3d7ff;border-color: #b3d7ff;}.custom-control-input:disabled ~ .custom-control-label, .custom-control-input[disabled] ~ .custom-control-label {color: #6c757d;}.custom-control-input:disabled ~ .custom-control-label::before, .custom-control-input[disabled] ~ .custom-control-label::before {background-color: #e9ecef;}.custom-control-label {position: relative;vertical-align: top;}.custom-control-label::after, .custom-control-label::before {position: absolute;top: 0.25rem;left: -1.5rem;display: block;width: 1rem;height: 1rem;content: '';}.custom-control-label::before {pointer-events: none;background-color: #fff;border: 1px solid #adb5bd;}.custom-control-label::after {background: 50%/50% 50% no-repeat;}.custom-checkbox .custom-control-label::before {border-radius: 0.25rem;}.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");}.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {border-color: #007bff;background-color: #007bff;}.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before, .custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before, .custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {background-color: rgba(0, 123, 255, 0.5);}.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");}.custom-radio .custom-control-label::before {border-radius: 50%;}.custom-radio .custom-control-input:checked ~ .custom-control-label::after {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");}.custom-switch {padding-left: 2.25rem;}.custom-switch .custom-control-label::before {left: -2.25rem;width: 1.75rem;pointer-events: all;border-radius: 0.5rem;}.custom-switch .custom-control-label::after {top: calc(0.25rem + 2px);left: calc(-2.25rem + 2px);width: calc(1rem - 4px);height: calc(1rem - 4px);background-color: #adb5bd;border-radius: 0.5rem;transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce) {.custom-switch .custom-control-label::after {transition: none;}}.custom-switch .custom-control-input:checked ~ .custom-control-label::after {background-color: #fff;-webkit-transform: translateX(0.75rem);transform: translateX(0.75rem);}.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {background-color: rgba(0, 123, 255, 0.5);}.custom-select {display: inline-block;width: 100%;height: calc(1.5em + 0.75rem + 2px);padding: 0.375rem 1.75rem 0.375rem 0.75rem;font-size: 1rem;line-height: 1.5;color: #495057;vertical-align: middle;background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.75rem center/8px 10px no-repeat #fff;border: 1px solid #ced4da;border-radius: 0.25rem;appearance: none;}.custom-file-input:focus ~ .custom-file-label, .custom-select:focus {border-color: #80bdff;box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.custom-select:focus {outline: 0;}.custom-select:focus::-ms-value {color: #495057;background-color: #fff;}.custom-file-input:disabled ~ .custom-file-label, .custom-file-input[disabled] ~ .custom-file-label, .custom-select:disabled {background-color: #e9ecef;}.custom-select[multiple], .custom-select[size]:not([size='1']) {height: auto;padding-right: 0.75rem;background-image: none;}.custom-select:disabled {color: #6c757d;}.custom-select::-ms-expand {display: none;}.custom-select:-moz-focusring {color: transparent;text-shadow: 0 0 0 #495057;}.custom-select-sm {height: calc(1.5em + 0.5rem + 2px);padding-top: 0.25rem;padding-bottom: 0.25rem;padding-left: 0.5rem;font-size: 0.875rem;}.custom-select-lg {height: calc(1.5em + 1rem + 2px);padding-top: 0.5rem;padding-bottom: 0.5rem;padding-left: 1rem;font-size: 1.25rem;}.custom-file, .custom-file-input, .custom-file-label {height: calc(1.5em + 0.75rem + 2px);}.custom-file {position: relative;display: inline-block;width: 100%;}.custom-file-input {position: relative;z-index: 2;width: 100%;margin: 0;opacity: 0;}.custom-file-label, .custom-file-label::after {position: absolute;padding: 0.375rem 0.75rem;line-height: 1.5;color: #495057;top: 0;right: 0;}.custom-file-input ~ .custom-file-label[data-browse]::after {content: attr(data-browse);}.custom-file-label {left: 0;z-index: 1;font-weight: 400;background-color: #fff;border: 1px solid #ced4da;border-radius: 0.25rem;}.custom-file-label::after {bottom: 0;z-index: 3;display: block;height: calc(1.5em + 0.75rem);content: 'Browse';background-color: #e9ecef;border-left: inherit;border-radius: 0 0.25rem 0.25rem 0;}.nav, .navbar {display: -ms-flexbox;}.custom-range {width: 100%;height: 1.4rem;padding: 0;background-color: transparent;appearance: none;}.custom-range:focus {outline: 0;}.custom-range:focus::-webkit-slider-thumb {box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.custom-range:focus::-moz-range-thumb {box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.custom-range:focus::-ms-thumb {box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.custom-range::-moz-focus-outer {border: 0;}.custom-range::-webkit-slider-thumb {width: 1rem;height: 1rem;margin-top: -0.25rem;background-color: #007bff;border: 0;border-radius: 1rem;-webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-webkit-appearance: none;appearance: none;}@media (prefers-reduced-motion: reduce) {.custom-range::-webkit-slider-thumb {-webkit-transition: none;transition: none;}}.custom-range::-webkit-slider-thumb:active {background-color: #b3d7ff;}.custom-range::-webkit-slider-runnable-track {width: 100%;height: 0.5rem;color: transparent;cursor: pointer;background-color: #dee2e6;border-color: transparent;border-radius: 1rem;}.custom-range::-moz-range-thumb {width: 1rem;height: 1rem;background-color: #007bff;border: 0;border-radius: 1rem;-moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-moz-appearance: none;appearance: none;}@media (prefers-reduced-motion: reduce) {.custom-range::-moz-range-thumb {-moz-transition: none;transition: none;}}.custom-range::-moz-range-thumb:active {background-color: #b3d7ff;}.custom-range::-moz-range-track {width: 100%;height: 0.5rem;color: transparent;cursor: pointer;background-color: #dee2e6;border-color: transparent;border-radius: 1rem;}.custom-range::-ms-thumb {width: 1rem;height: 1rem;margin-top: 0;margin-right: 0.2rem;margin-left: 0.2rem;background-color: #007bff;border: 0;border-radius: 1rem;-ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;appearance: none;}@media (prefers-reduced-motion: reduce) {.custom-range::-ms-thumb {-ms-transition: none;transition: none;}}.custom-range::-ms-thumb:active {background-color: #b3d7ff;}.custom-range::-ms-track {width: 100%;height: 0.5rem;color: transparent;cursor: pointer;background-color: transparent;border-color: transparent;border-width: 0.5rem;}.custom-range::-ms-fill-lower {background-color: #dee2e6;border-radius: 1rem;}.custom-range::-ms-fill-upper {margin-right: 15px;background-color: #dee2e6;border-radius: 1rem;}.custom-range:disabled::-webkit-slider-thumb {background-color: #adb5bd;}.custom-range:disabled::-webkit-slider-runnable-track {cursor: default;}.custom-range:disabled::-moz-range-thumb {background-color: #adb5bd;}.custom-range:disabled::-moz-range-track {cursor: default;}.custom-range:disabled::-ms-thumb {background-color: #adb5bd;}.custom-control-label::before, .custom-file-label, .custom-select {transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce) {.custom-control-label::before, .custom-file-label, .custom-select {transition: none;}}.nav {display: flex;flex-wrap: wrap;padding-left: 0;list-style: none;}.nav-link, .navbar {padding: 0.5rem 1rem;}.nav-link {display: block;}.nav-link.disabled {color: #6c757d;pointer-events: none;cursor: default;}.nav-tabs {border-bottom: 1px solid #dee2e6;}.nav-tabs .nav-item {margin-bottom: -1px;}.nav-tabs .nav-link {border: 1px solid transparent;border-top-left-radius: 0.25rem;border-top-right-radius: 0.25rem;}.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {border-color: #e9ecef #e9ecef #dee2e6;}.nav-tabs .nav-link.disabled {color: #6c757d;background-color: transparent;border-color: transparent;}.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {color: #495057;background-color: #fff;border-color: #dee2e6 #dee2e6 #fff;}.nav-tabs .dropdown-menu {margin-top: -1px;border-top-left-radius: 0;border-top-right-radius: 0;}.nav-pills .nav-link {border-radius: 0.25rem;}.nav-pills .nav-link.active, .nav-pills .show > .nav-link {color: #fff;background-color: #007bff;}.nav-fill .nav-item {-ms-flex: 1 1 auto;flex: 1 1 auto;text-align: center;}.nav-justified .nav-item {-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;text-align: center;}.tab-content > .tab-pane {display: block;}.tab-content > .active {display: block;}.navbar {position: relative;display: flex;flex-wrap: wrap;-ms-flex-align: center;align-items: center;-ms-flex-pack: justify;justify-content: space-between;}.navbar .container, .navbar .container-fluid, .navbar .container-lg, .navbar .container-md, .navbar .container-sm, .navbar .container-xl {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;-ms-flex-align: center;align-items: center;-ms-flex-pack: justify;justify-content: space-between;}.navbar-brand {display: inline-block;padding-top: 0.3125rem;padding-bottom: 0.3125rem;margin-right: 1rem;font-size: 1.25rem;line-height: inherit;}.navbar-brand:focus, .navbar-brand:hover {text-decoration: none;}.navbar-nav {display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;padding-left: 0;margin-bottom: 0;list-style: none;}.navbar-nav .nav-link {padding-right: 0;padding-left: 0;}.navbar-nav .dropdown-menu {position: static;float: none;}.navbar-text {display: inline-block;padding-top: 0.5rem;padding-bottom: 0.5rem;}.navbar-collapse {-ms-flex-preferred-size: 100%;flex-basis: 100%;-ms-flex-positive: 1;flex-grow: 1;-ms-flex-align: center;align-items: center;}.navbar-toggler {padding: 0.25rem 0.75rem;font-size: 1.25rem;line-height: 1;background-color: transparent;border: 1px solid transparent;border-radius: 0.25rem;}.navbar-toggler-icon {display: inline-block;width: 1.5em;height: 1.5em;vertical-align: middle;content: '';background: center center no-repeat;background-size: 100% 100%;}@media (max-width: 575.98px) {.navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-xl {padding-right: 0;padding-left: 0;}}@media (min-width: 576px) {.navbar-expand-sm {-ms-flex-flow: row nowrap;flex-flow: row nowrap;-ms-flex-pack: start;justify-content: flex-start;}.navbar-expand-sm .navbar-nav {-ms-flex-direction: row;flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu {position: absolute;}.navbar-expand-sm .navbar-nav .nav-link {padding-right: 0.5rem;padding-left: 0.5rem;}.navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-xl {-ms-flex-wrap: nowrap;flex-wrap: nowrap;}.navbar-expand-sm .navbar-collapse {display: -ms-flexbox !important;display: flex !important;-ms-flex-preferred-size: auto;flex-basis: auto;}.navbar-expand-sm .navbar-toggler {display: none;}}@media (max-width: 767.98px) {.navbar-expand-md > .container, .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-md, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-xl {padding-right: 0;padding-left: 0;}}@media (min-width: 768px) {.navbar-expand-md {-ms-flex-flow: row nowrap;flex-flow: row nowrap;-ms-flex-pack: start;justify-content: flex-start;}.navbar-expand-md .navbar-nav {-ms-flex-direction: row;flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu {position: absolute;}.navbar-expand-md .navbar-nav .nav-link {padding-right: 0.5rem;padding-left: 0.5rem;}.navbar-expand-md > .container, .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-md, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-xl {-ms-flex-wrap: nowrap;flex-wrap: nowrap;}.navbar-expand-md .navbar-collapse {display: -ms-flexbox !important;display: flex !important;-ms-flex-preferred-size: auto;flex-basis: auto;}.navbar-expand-md .navbar-toggler {display: none;}}@media (max-width: 991.98px) {.navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-xl {padding-right: 0;padding-left: 0;}}@media (min-width: 992px) {.navbar-expand-lg {-ms-flex-flow: row nowrap;flex-flow: row nowrap;-ms-flex-pack: start;justify-content: flex-start;}.navbar-expand-lg .navbar-nav {-ms-flex-direction: row;flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu {position: absolute;}.navbar-expand-lg .navbar-nav .nav-link {padding-right: 0.5rem;padding-left: 0.5rem;}.navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-xl {-ms-flex-wrap: nowrap;flex-wrap: nowrap;}.navbar-expand-lg .navbar-collapse {display: -ms-flexbox !important;display: flex !important;-ms-flex-preferred-size: auto;flex-basis: auto;}.navbar-expand-lg .navbar-toggler {display: none;}}@media (max-width: 1199.98px) {.navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-xl {padding-right: 0;padding-left: 0;}}@media (min-width: 1200px) {.navbar-expand-xl {-ms-flex-flow: row nowrap;flex-flow: row nowrap;-ms-flex-pack: start;justify-content: flex-start;}.navbar-expand-xl .navbar-nav {-ms-flex-direction: row;flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu {position: absolute;}.navbar-expand-xl .navbar-nav .nav-link {padding-right: 0.5rem;padding-left: 0.5rem;}.navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-xl {-ms-flex-wrap: nowrap;flex-wrap: nowrap;}.navbar-expand-xl .navbar-collapse {display: -ms-flexbox !important;display: flex !important;-ms-flex-preferred-size: auto;flex-basis: auto;}.navbar-expand-xl .navbar-toggler {display: none;}}.navbar-expand {-ms-flex-flow: row nowrap;flex-flow: row nowrap;-ms-flex-pack: start;justify-content: flex-start;}.navbar-expand > .container, .navbar-expand > .container-fluid, .navbar-expand > .container-lg, .navbar-expand > .container-md, .navbar-expand > .container-sm, .navbar-expand > .container-xl {padding-right: 0;padding-left: 0;}.navbar-expand .navbar-nav {-ms-flex-direction: row;flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu {position: absolute;}.navbar-expand .navbar-nav .nav-link {padding-right: 0.5rem;padding-left: 0.5rem;}.navbar-expand > .container, .navbar-expand > .container-fluid, .navbar-expand > .container-lg, .navbar-expand > .container-md, .navbar-expand > .container-sm, .navbar-expand > .container-xl {-ms-flex-wrap: nowrap;flex-wrap: nowrap;}.navbar-expand .navbar-collapse {display: -ms-flexbox !important;display: flex !important;-ms-flex-preferred-size: auto;flex-basis: auto;}.navbar-expand .navbar-toggler {display: none;}.navbar-light .navbar-brand, .navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-nav .nav-link {color: rgba(0, 0, 0, 0.5);}.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-link:hover {color: rgba(0, 0, 0, 0.7);}.navbar-light .navbar-nav .nav-link.disabled {color: rgba(0, 0, 0, 0.3);}.navbar-light .navbar-nav .active > .nav-link, .navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .show > .nav-link {color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-toggler {color: rgba(0, 0, 0, 0.5);border-color: rgba(0, 0, 0, 0.1);}.navbar-light .navbar-toggler-icon {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-light .navbar-text {color: rgba(0, 0, 0, 0.5);}.navbar-light .navbar-text a, .navbar-light .navbar-text a:focus, .navbar-light .navbar-text a:hover {color: rgba(0, 0, 0, 0.9);}.navbar-dark .navbar-brand, .navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover {color: #fff;}.navbar-dark .navbar-nav .nav-link {color: rgba(255, 255, 255, 0.5);}.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {color: rgba(255, 255, 255, 0.75);}.navbar-dark .navbar-nav .nav-link.disabled {color: rgba(255, 255, 255, 0.25);}.navbar-dark .navbar-nav .active > .nav-link, .navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar-nav .show > .nav-link {color: #fff;}.navbar-dark .navbar-toggler {color: rgba(255, 255, 255, 0.5);border-color: rgba(255, 255, 255, 0.1);}.navbar-dark .navbar-toggler-icon {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-dark .navbar-text {color: rgba(255, 255, 255, 0.5);}.navbar-dark .navbar-text a, .navbar-dark .navbar-text a:focus, .navbar-dark .navbar-text a:hover {color: #fff;}.card {position: relative;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;min-width: 0;background-color: #fff;background-clip: border-box;border: 1px solid rgba(0, 0, 0, 0.125);border-radius: 0.25rem;}.card > hr {margin-right: 0;margin-left: 0;}.card > .list-group:first-child .list-group-item:first-child {border-top-left-radius: 0.25rem;border-top-right-radius: 0.25rem;}.card > .list-group:last-child .list-group-item:last-child {border-bottom-right-radius: 0.25rem;border-bottom-left-radius: 0.25rem;}.card-body {-ms-flex: 1 1 auto;flex: 1 1 auto;min-height: 1px;padding: 1.25rem;}.card-footer, .card-header {padding: 0.75rem 1.25rem;background-color: rgba(0, 0, 0, 0.03);}.card-title {margin-bottom: 0.75rem;}.card-header, .card-subtitle, .card-text:last-child {margin-bottom: 0;}.card-subtitle {margin-top: -0.375rem;}.card-link + .card-link {margin-left: 1.25rem;}.card-header-pills, .card-header-tabs {margin-right: -0.625rem;margin-left: -0.625rem;}.card-header {border-bottom: 1px solid rgba(0, 0, 0, 0.125);}.card-header:first-child {border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;}.card-header + .list-group .list-group-item:first-child {border-top: 0;}.card-footer {border-top: 1px solid rgba(0, 0, 0, 0.125);}.card-footer:last-child {border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);}.card-header-tabs {margin-bottom: -0.75rem;border-bottom: 0;}.card-img-overlay {position: absolute;top: 0;right: 0;bottom: 0;left: 0;padding: 1.25rem;}.alert, .btn .badge, .page-link {position: relative;}.card-img, .card-img-bottom, .card-img-top {-ms-flex-negative: 0;flex-shrink: 0;width: 100%;}.card-img, .card-img-top {border-top-left-radius: calc(0.25rem - 1px);border-top-right-radius: calc(0.25rem - 1px);}.card-img, .card-img-bottom {border-bottom-right-radius: calc(0.25rem - 1px);border-bottom-left-radius: calc(0.25rem - 1px);}.card-deck .card {margin-bottom: 15px;}@media (min-width: 576px) {.card-deck {display: -ms-flexbox;display: flex;-ms-flex-flow: row wrap;flex-flow: row wrap;margin-right: -15px;margin-left: -15px;}.card-deck .card {-ms-flex: 1 0 0%;flex: 1 0 0%;margin-right: 15px;margin-bottom: 0;margin-left: 15px;}}.card-group > .card {margin-bottom: 15px;}@media (min-width: 576px) {.card-group {display: -ms-flexbox;display: flex;-ms-flex-flow: row wrap;flex-flow: row wrap;}.card-group > .card {-ms-flex: 1 0 0%;flex: 1 0 0%;margin-bottom: 0;}.card-group > .card + .card {margin-left: 0;border-left: 0;}.card-group > .card:not(:last-child) {border-top-right-radius: 0;border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-header, .card-group > .card:not(:last-child) .card-img-top {border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-footer, .card-group > .card:not(:last-child) .card-img-bottom {border-bottom-right-radius: 0;}.card-group > .card:not(:first-child) {border-top-left-radius: 0;border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-header, .card-group > .card:not(:first-child) .card-img-top {border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-footer, .card-group > .card:not(:first-child) .card-img-bottom {border-bottom-left-radius: 0;}.card-columns {-webkit-column-count: 3;-moz-column-count: 3;column-count: 3;-webkit-column-gap: 1.25rem;-moz-column-gap: 1.25rem;column-gap: 1.25rem;orphans: 1;widows: 1;}.card-columns .card {display: inline-block;width: 100%;}}.card-columns .card {margin-bottom: 0.75rem;}.accordion > .card:not(:last-of-type) {border-bottom: 0;border-bottom-right-radius: 0;border-bottom-left-radius: 0;}.accordion > .card:not(:first-of-type) {border-top-left-radius: 0;border-top-right-radius: 0;}.accordion > .card > .card-header {border-radius: 0;margin-bottom: -1px;}.breadcrumb {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;margin-bottom: 1rem;list-style: none;background-color: #e9ecef;}.breadcrumb-item + .breadcrumb-item {padding-left: 0.5rem;}.breadcrumb-item + .breadcrumb-item::before {display: inline-block;padding-right: 0.5rem;color: #6c757d;content: '/';}.breadcrumb-item.active {color: #6c757d;}.pagination {display: -ms-flexbox;display: flex;padding-left: 0;list-style: none;border-radius: 0.25rem;}.page-link {display: block;padding: 0.5rem 0.75rem;margin-left: -1px;line-height: 1.25;color: #007bff;background-color: #fff;border: 1px solid #dee2e6;}.page-link:hover {z-index: 2;color: #0056b3;background-color: #e9ecef;border-color: #dee2e6;}.page-link:focus {z-index: 3;outline: 0;box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);}.page-item:first-child .page-link {margin-left: 0;border-top-left-radius: 0.25rem;border-bottom-left-radius: 0.25rem;}.page-item:last-child .page-link {border-top-right-radius: 0.25rem;border-bottom-right-radius: 0.25rem;}.page-item.active .page-link {z-index: 3;color: #fff;background-color: #007bff;border-color: #007bff;}.page-item.disabled .page-link {color: #6c757d;pointer-events: none;cursor: auto;background-color: #fff;border-color: #dee2e6;}.pagination-lg .page-link {padding: 0.75rem 1.5rem;font-size: 1.25rem;line-height: 1.5;}.pagination-lg .page-item:first-child .page-link {border-top-left-radius: 0.3rem;border-bottom-left-radius: 0.3rem;}.pagination-lg .page-item:last-child .page-link {border-top-right-radius: 0.3rem;border-bottom-right-radius: 0.3rem;}.pagination-sm .page-link {padding: 0.25rem 0.5rem;font-size: 0.875rem;line-height: 1.5;}.badge, .close {font-weight: 700;line-height: 1;}.pagination-sm .page-item:first-child .page-link {border-top-left-radius: 0.2rem;border-bottom-left-radius: 0.2rem;}.pagination-sm .page-item:last-child .page-link {border-top-right-radius: 0.2rem;border-bottom-right-radius: 0.2rem;}.badge {display: inline-block;padding: 0.25em 0.4em;font-size: 75%;text-align: center;border-radius: 0.25rem;transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce) {.badge {transition: none;}}.badge:empty {display: none;}.btn .badge {top: -1px;}.badge-pill {padding-right: 0.6em;padding-left: 0.6em;border-radius: 10rem;}.badge-primary {color: #fff;background-color: #007bff;}a.badge-primary:focus, a.badge-primary:hover {color: #fff;background-color: #0062cc;}a.badge-primary.focus, a.badge-primary:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);}.badge-secondary {color: #fff;background-color: #6c757d;}a.badge-secondary:focus, a.badge-secondary:hover {color: #fff;background-color: #545b62;}a.badge-secondary.focus, a.badge-secondary:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);}.badge-success {color: #fff;background-color: #28a745;}a.badge-success:focus, a.badge-success:hover {color: #fff;background-color: #1e7e34;}a.badge-success.focus, a.badge-success:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);}.badge-info {color: #fff;background-color: #17a2b8;}a.badge-info:focus, a.badge-info:hover {color: #fff;background-color: #117a8b;}a.badge-info.focus, a.badge-info:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);}.badge-warning {color: #212529;background-color: #ffc107;}a.badge-warning:focus, a.badge-warning:hover {color: #212529;background-color: #d39e00;}a.badge-warning.focus, a.badge-warning:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);}.badge-danger {color: #fff;background-color: #dc3545;}a.badge-danger:focus, a.badge-danger:hover {color: #fff;background-color: #bd2130;}a.badge-danger.focus, a.badge-danger:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);}.badge-light {color: #212529;background-color: #f8f9fa;}a.badge-light:focus, a.badge-light:hover {color: #212529;background-color: #dae0e5;}a.badge-light.focus, a.badge-light:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);}.badge-dark {color: #fff;background-color: #343a40;}a.badge-dark:focus, a.badge-dark:hover {color: #fff;background-color: #1d2124;}a.badge-dark.focus, a.badge-dark:focus {outline: 0;box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);}.jumbotron {padding: 2rem 1rem;margin-bottom: 2rem;background-color: #e9ecef;border-radius: 0.3rem;}@media (min-width: 576px) {.jumbotron {padding: 4rem 2rem;}}.jumbotron-fluid {padding-right: 0;padding-left: 0;border-radius: 0;}.alert {padding: 0.75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-radius: 0.25rem;}.alert-heading {color: inherit;}.alert-link {font-weight: 700;}.alert-dismissible {padding-right: 4rem;}.alert-dismissible .close {position: absolute;top: 0;right: 0;padding: 0.75rem 1.25rem;color: inherit;}.alert-primary {color: #004085;background-color: #cce5ff;border-color: #b8daff;}.alert-primary hr {border-top-color: #9fcdff;}.alert-primary .alert-link {color: #002752;}.alert-secondary {color: #383d41;background-color: #e2e3e5;border-color: #d6d8db;}.alert-secondary hr {border-top-color: #c8cbcf;}.alert-secondary .alert-link {color: #202326;}.alert-success {color: #155724;background-color: #d4edda;border-color: #c3e6cb;}.alert-success hr {border-top-color: #b1dfbb;}.alert-success .alert-link {color: #0b2e13;}.alert-info {color: #0c5460;background-color: #d1ecf1;border-color: #bee5eb;}.alert-info hr {border-top-color: #abdde5;}.alert-info .alert-link {color: #062c33;}.alert-warning {color: #856404;background-color: #fff3cd;border-color: #ffeeba;}.alert-warning hr {border-top-color: #ffe8a1;}.alert-warning .alert-link {color: #533f03;}.alert-danger {color: #721c24;background-color: #f8d7da;border-color: #f5c6cb;}.alert-danger hr {border-top-color: #f1b0b7;}.alert-danger .alert-link {color: #491217;}.alert-light {color: #818182;background-color: #fefefe;border-color: #fdfdfe;}.alert-light hr {border-top-color: #ececf6;}.alert-light .alert-link {color: #686868;}.alert-dark {color: #1b1e21;background-color: #d6d8d9;border-color: #c6c8ca;}.alert-dark hr {border-top-color: #b9bbbe;}.alert-dark .alert-link {color: #040505;}@-webkit-keyframes progress-bar-stripes {from {background-position: 1rem 0;}to {background-position: 0 0;}}@keyframes progress-bar-stripes {from {background-position: 1rem 0;}to {background-position: 0 0;}}.progress {display: -ms-flexbox;display: flex;height: 1rem;font-size: 0.75rem;background-color: #e9ecef;border-radius: 0.25rem;}.progress-bar {display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;-ms-flex-pack: center;justify-content: center;color: #fff;text-align: center;background-color: #007bff;transition: width 0.6s ease;}.progress-bar-striped {background-image: linear-gradient( 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent );background-size: 1rem 1rem;}.progress-bar-animated {-webkit-animation: progress-bar-stripes 1s linear infinite;animation: progress-bar-stripes 1s linear infinite;}@media (prefers-reduced-motion: reduce) {.progress-bar {transition: none;}.progress-bar-animated {-webkit-animation: none;animation: none;}}.media {display: -ms-flexbox;display: flex;-ms-flex-align: start;align-items: flex-start;}.media-body {-ms-flex: 1;flex: 1;}.list-group {display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;padding-left: 0;margin-bottom: 0;}.list-group-item-action {width: 100%;color: #495057;text-align: inherit;}.list-group-item-action:focus, .list-group-item-action:hover {z-index: 1;color: #495057;background-color: #f8f9fa;}.list-group-item-action:active {color: #212529;background-color: #e9ecef;}.list-group-item {position: relative;display: block;padding: 0.75rem 1.25rem;background-color: #fff;border: 1px solid rgba(0, 0, 0, 0.125);}.list-group-item:first-child {border-top-left-radius: 0.25rem;border-top-right-radius: 0.25rem;}.list-group-item:last-child {border-bottom-right-radius: 0.25rem;border-bottom-left-radius: 0.25rem;}.list-group-item.disabled, .list-group-item:disabled {color: #6c757d;pointer-events: none;background-color: #fff;}.list-group-item.active {z-index: 2;color: #fff;background-color: #007bff;border-color: #007bff;}.list-group-item + .list-group-item {border-top-width: 0;}.list-group-item + .list-group-item.active {margin-top: -1px;border-top-width: 1px;}.list-group-horizontal {-ms-flex-direction: row;flex-direction: row;}.list-group-horizontal .list-group-item:first-child {border-bottom-left-radius: 0.25rem;border-top-right-radius: 0;}.list-group-horizontal .list-group-item:last-child {border-top-right-radius: 0.25rem;border-bottom-left-radius: 0;}.list-group-horizontal .list-group-item.active {margin-top: 0;}.list-group-horizontal .list-group-item + .list-group-item {border-top-width: 1px;border-left-width: 0;}.list-group-horizontal .list-group-item + .list-group-item.active {margin-left: -1px;border-left-width: 1px;}@media (min-width: 576px) {.list-group-horizontal-sm {-ms-flex-direction: row;flex-direction: row;}.list-group-horizontal-sm .list-group-item:first-child {border-bottom-left-radius: 0.25rem;border-top-right-radius: 0;}.list-group-horizontal-sm .list-group-item:last-child {border-top-right-radius: 0.25rem;border-bottom-left-radius: 0;}.list-group-horizontal-sm .list-group-item.active {margin-top: 0;}.list-group-horizontal-sm .list-group-item + .list-group-item {border-top-width: 1px;border-left-width: 0;}.list-group-horizontal-sm .list-group-item + .list-group-item.active {margin-left: -1px;border-left-width: 1px;}}@media (min-width: 768px) {.list-group-horizontal-md {-ms-flex-direction: row;flex-direction: row;}.list-group-horizontal-md .list-group-item:first-child {border-bottom-left-radius: 0.25rem;border-top-right-radius: 0;}.list-group-horizontal-md .list-group-item:last-child {border-top-right-radius: 0.25rem;border-bottom-left-radius: 0;}.list-group-horizontal-md .list-group-item.active {margin-top: 0;}.list-group-horizontal-md .list-group-item + .list-group-item {border-top-width: 1px;border-left-width: 0;}.list-group-horizontal-md .list-group-item + .list-group-item.active {margin-left: -1px;border-left-width: 1px;}}@media (min-width: 992px) {.list-group-horizontal-lg {-ms-flex-direction: row;flex-direction: row;}.list-group-horizontal-lg .list-group-item:first-child {border-bottom-left-radius: 0.25rem;border-top-right-radius: 0;}.list-group-horizontal-lg .list-group-item:last-child {border-top-right-radius: 0.25rem;border-bottom-left-radius: 0;}.list-group-horizontal-lg .list-group-item.active {margin-top: 0;}.list-group-horizontal-lg .list-group-item + .list-group-item {border-top-width: 1px;border-left-width: 0;}.list-group-horizontal-lg .list-group-item + .list-group-item.active {margin-left: -1px;border-left-width: 1px;}}@media (min-width: 1200px) {.list-group-horizontal-xl {-ms-flex-direction: row;flex-direction: row;}.list-group-horizontal-xl .list-group-item:first-child {border-bottom-left-radius: 0.25rem;border-top-right-radius: 0;}.list-group-horizontal-xl .list-group-item:last-child {border-top-right-radius: 0.25rem;border-bottom-left-radius: 0;}.list-group-horizontal-xl .list-group-item.active {margin-top: 0;}.list-group-horizontal-xl .list-group-item + .list-group-item {border-top-width: 1px;border-left-width: 0;}.list-group-horizontal-xl .list-group-item + .list-group-item.active {margin-left: -1px;border-left-width: 1px;}}.list-group-flush .list-group-item {border-right-width: 0;border-left-width: 0;border-radius: 0;}.list-group-flush .list-group-item:first-child {border-top-width: 0;}.list-group-flush:last-child .list-group-item:last-child {border-bottom-width: 0;}.list-group-item-primary {color: #004085;background-color: #b8daff;}.list-group-item-primary.list-group-item-action:focus, .list-group-item-primary.list-group-item-action:hover {color: #004085;background-color: #9fcdff;}.list-group-item-primary.list-group-item-action.active {color: #fff;background-color: #004085;border-color: #004085;}.list-group-item-secondary {color: #383d41;background-color: #d6d8db;}.list-group-item-secondary.list-group-item-action:focus, .list-group-item-secondary.list-group-item-action:hover {color: #383d41;background-color: #c8cbcf;}.list-group-item-secondary.list-group-item-action.active {color: #fff;background-color: #383d41;border-color: #383d41;}.list-group-item-success {color: #155724;background-color: #c3e6cb;}.list-group-item-success.list-group-item-action:focus, .list-group-item-success.list-group-item-action:hover {color: #155724;background-color: #b1dfbb;}.list-group-item-success.list-group-item-action.active {color: #fff;background-color: #155724;border-color: #155724;}.list-group-item-info {color: #0c5460;background-color: #bee5eb;}.list-group-item-info.list-group-item-action:focus, .list-group-item-info.list-group-item-action:hover {color: #0c5460;background-color: #abdde5;}.list-group-item-info.list-group-item-action.active {color: #fff;background-color: #0c5460;border-color: #0c5460;}.list-group-item-warning {color: #856404;background-color: #ffeeba;}.list-group-item-warning.list-group-item-action:focus, .list-group-item-warning.list-group-item-action:hover {color: #856404;background-color: #ffe8a1;}.list-group-item-warning.list-group-item-action.active {color: #fff;background-color: #856404;border-color: #856404;}.list-group-item-danger {color: #721c24;background-color: #f5c6cb;}.list-group-item-danger.list-group-item-action:focus, .list-group-item-danger.list-group-item-action:hover {color: #721c24;background-color: #f1b0b7;}.list-group-item-danger.list-group-item-action.active {color: #fff;background-color: #721c24;border-color: #721c24;}.list-group-item-light {color: #818182;background-color: #fdfdfe;}.list-group-item-light.list-group-item-action:focus, .list-group-item-light.list-group-item-action:hover {color: #818182;background-color: #ececf6;}.list-group-item-light.list-group-item-action.active {color: #fff;background-color: #818182;border-color: #818182;}.list-group-item-dark {color: #1b1e21;background-color: #c6c8ca;}.list-group-item-dark.list-group-item-action:focus, .list-group-item-dark.list-group-item-action:hover {color: #1b1e21;background-color: #b9bbbe;}.list-group-item-dark.list-group-item-action.active {color: #fff;background-color: #1b1e21;border-color: #1b1e21;}.close {float: right;color: #000;text-shadow: 0 1px 0 #fff;opacity: 0.5;}.popover, .tooltip {font-style: normal;font-weight: 400;text-transform: none;letter-spacing: normal;word-break: normal;word-spacing: normal;white-space: normal;line-break: auto;font-size: 0.875rem;text-decoration: none;word-wrap: break-word;}.modal-title, .popover, .tooltip {line-height: 1.5;}.popover, .text-hide, .tooltip {text-shadow: none;}.close:hover {color: #000;text-decoration: none;}.close:not(:disabled):not(.disabled):focus, .close:not(:disabled):not(.disabled):hover {opacity: 0.75;}button.close {padding: 0;background-color: transparent;border: 0;-webkit-appearance: none;-moz-appearance: none;appearance: none;}.toast, .toast-header {background-color: rgba(255, 255, 255, 0.85);background-clip: padding-box;}a.close.disabled {pointer-events: none;}.toast {max-width: 350px;font-size: 0.875rem;border: 1px solid rgba(0, 0, 0, 0.1);box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);-webkit-backdrop-filter: blur(10px);backdrop-filter: blur(10px);opacity: 0;border-radius: 0.25rem;}.toast:not(:last-child) {margin-bottom: 0.75rem;}.toast.showing {opacity: 1;}.toast.show {display: block;opacity: 1;}.toast.hide {display: none;}.toast-header {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;padding: 0.25rem 0.75rem;color: #6c757d;border-bottom: 1px solid rgba(0, 0, 0, 0.05);}.toast-body {padding: 0.75rem;}.modal-open .modal {overflow-x: hidden;overflow-y: auto;}.modal {position: fixed;top: 0;left: 0;z-index: 1050;display: none;width: 100%;height: 100%;overflow: hidden;outline: 0;}.modal-dialog {position: relative;width: auto;margin: 0.5rem;pointer-events: none;}.modal.fade .modal-dialog {transition: -webkit-transform 0.3s ease-out;transition: transform 0.3s ease-out;transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;-webkit-transform: translate(0, -50px);transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce) {.modal.fade .modal-dialog {transition: none;}}.modal.show .modal-dialog {-webkit-transform: none;transform: none;}.modal.modal-static .modal-dialog {-webkit-transform: scale(1.02);transform: scale(1.02);}.modal-dialog-scrollable {display: -ms-flexbox;display: flex;max-height: calc(100% - 1rem);}.modal-dialog-scrollable .modal-content {max-height: calc(100vh - 1rem);overflow: hidden;}.modal-dialog-scrollable .modal-footer, .modal-dialog-scrollable .modal-header {-ms-flex-negative: 0;flex-shrink: 0;}.modal-dialog-scrollable .modal-body {overflow-y: auto;}.modal-dialog-centered {display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;min-height: calc(100% - 1rem);}.modal-dialog-centered::before {display: block;height: calc(100vh - 1rem);content: '';}.modal-dialog-centered.modal-dialog-scrollable {-ms-flex-direction: column;flex-direction: column;-ms-flex-pack: center;justify-content: center;height: 100%;}.modal-dialog-centered.modal-dialog-scrollable .modal-content {max-height: none;}.modal-dialog-centered.modal-dialog-scrollable::before {content: none;}.carousel-inner::after, .clearfix::after, .embed-responsive::before, .popover .arrow::after, .popover .arrow::before, .stretched-link::after, .tooltip .arrow::before {content: '';}.modal-content {position: relative;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;width: 100%;pointer-events: auto;background-color: #fff;background-clip: padding-box;outline: 0;}.modal-backdrop {position: fixed;top: 0;left: 0;z-index: 1040;width: 100vw;height: 100vh;background-color: #000;}.modal-backdrop.fade {opacity: 0;}.modal-backdrop.show {opacity: 0.5;}.modal-header {display: -ms-flexbox;display: flex;-ms-flex-align: start;align-items: flex-start;-ms-flex-pack: justify;justify-content: space-between;padding: 1rem;border-bottom: 1px solid #dee2e6;border-top-left-radius: calc(0.3rem - 1px);border-top-right-radius: calc(0.3rem - 1px);}.modal-header .close {padding: 1rem;margin: -1rem -1rem -1rem auto;}.modal-title {margin-bottom: 0;}.modal-body {position: relative;-ms-flex: 1 1 auto;flex: 1 1 auto;padding: 1rem;}.modal-footer {display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap;-ms-flex-align: center;align-items: center;-ms-flex-pack: end;justify-content: flex-end;padding: 0.75rem;border-top: 1px solid #dee2e6;border-bottom-right-radius: calc(0.3rem - 1px);border-bottom-left-radius: calc(0.3rem - 1px);}.popover, .popover .arrow, .popover .arrow::after, .popover .arrow::before, .tooltip, .tooltip .arrow {position: absolute;display: block;}.modal-footer > * {margin: 0.25rem;}.modal-scrollbar-measure {position: absolute;top: -9999px;width: 50px;height: 50px;overflow: scroll;}@media (min-width: 576px) {.modal-dialog {max-width: 500px;margin: 1.75rem auto;}.modal-dialog-scrollable {max-height: calc(100% - 3.5rem);}.modal-dialog-scrollable .modal-content {max-height: calc(100vh - 3.5rem);}.modal-dialog-centered {min-height: calc(100% - 3.5rem);}.modal-dialog-centered::before {height: calc(100vh - 3.5rem);}.modal-sm {max-width: 300px;}}@media (min-width: 992px) {.modal-lg, .modal-xl {max-width: 800px;}}@media (min-width: 1200px) {.modal-xl {max-width: 1140px;}}.tooltip {z-index: 1070;margin: 0;text-align: left;text-align: start;opacity: 0;}.tooltip.show {opacity: 0.9;}.tooltip .arrow {width: 0.8rem;height: 0.4rem;}.tooltip .arrow::before {position: absolute;border-color: transparent;border-style: solid;}.bs-tooltip-auto[x-placement^='top'], .bs-tooltip-top {padding: 0.4rem 0;}.bs-tooltip-auto[x-placement^='top'] .arrow, .bs-tooltip-top .arrow {bottom: 0;}.bs-tooltip-auto[x-placement^='top'] .arrow::before, .bs-tooltip-top .arrow::before {top: 0;border-width: 0.4rem 0.4rem 0;border-top-color: #000;}.bs-tooltip-auto[x-placement^='right'], .bs-tooltip-right {padding: 0 0.4rem;}.bs-tooltip-auto[x-placement^='right'] .arrow, .bs-tooltip-right .arrow {left: 0;width: 0.4rem;height: 0.8rem;}.bs-tooltip-auto[x-placement^='right'] .arrow::before, .bs-tooltip-right .arrow::before {right: 0;border-width: 0.4rem 0.4rem 0.4rem 0;border-right-color: #000;}.bs-tooltip-auto[x-placement^='bottom'], .bs-tooltip-bottom {padding: 0.4rem 0;}.bs-tooltip-auto[x-placement^='bottom'] .arrow, .bs-tooltip-bottom .arrow {top: 0;}.bs-tooltip-auto[x-placement^='bottom'] .arrow::before, .bs-tooltip-bottom .arrow::before {bottom: 0;border-width: 0 0.4rem 0.4rem;border-bottom-color: #000;}.bs-tooltip-auto[x-placement^='left'], .bs-tooltip-left {padding: 0 0.4rem;}.bs-tooltip-auto[x-placement^='left'] .arrow, .bs-tooltip-left .arrow {right: 0;width: 0.4rem;height: 0.8rem;}.bs-tooltip-auto[x-placement^='left'] .arrow::before, .bs-tooltip-left .arrow::before {left: 0;border-width: 0.4rem 0 0.4rem 0.4rem;border-left-color: #000;}.tooltip-inner {max-width: 200px;padding: 0.25rem 0.5rem;color: #fff;text-align: center;background-color: #000;border-radius: 0.25rem;}.popover {top: 0;left: 0;z-index: 1060;max-width: 276px;text-align: left;text-align: start;background-color: #fff;background-clip: padding-box;border: 1px solid rgba(0, 0, 0, 0.2);border-radius: 0.3rem;}.popover .arrow {width: 1rem;height: 0.5rem;margin: 0 0.3rem;}.popover .arrow::after, .popover .arrow::before {border-color: transparent;border-style: solid;}.bs-popover-auto[x-placement^='top'], .bs-popover-top {margin-bottom: 0.5rem;}.bs-popover-auto[x-placement^='top'] > .arrow, .bs-popover-top > .arrow {bottom: calc(-0.5rem - 1px);}.bs-popover-auto[x-placement^='top'] > .arrow::before, .bs-popover-top > .arrow::before {bottom: 0;border-width: 0.5rem 0.5rem 0;border-top-color: rgba(0, 0, 0, 0.25);}.bs-popover-auto[x-placement^='top'] > .arrow::after, .bs-popover-top > .arrow::after {bottom: 1px;border-width: 0.5rem 0.5rem 0;border-top-color: #fff;}.bs-popover-auto[x-placement^='right'], .bs-popover-right {margin-left: 0.5rem;}.bs-popover-auto[x-placement^='right'] > .arrow, .bs-popover-right > .arrow {left: calc(-0.5rem - 1px);width: 0.5rem;height: 1rem;margin: 0.3rem 0;}.bs-popover-auto[x-placement^='right'] > .arrow::before, .bs-popover-right > .arrow::before {left: 0;border-width: 0.5rem 0.5rem 0.5rem 0;border-right-color: rgba(0, 0, 0, 0.25);}.bs-popover-auto[x-placement^='right'] > .arrow::after, .bs-popover-right > .arrow::after {left: 1px;border-width: 0.5rem 0.5rem 0.5rem 0;border-right-color: #fff;}.bs-popover-auto[x-placement^='bottom'], .bs-popover-bottom {margin-top: 0.5rem;}.bs-popover-auto[x-placement^='bottom'] > .arrow, .bs-popover-bottom > .arrow {top: calc(-0.5rem - 1px);}.bs-popover-auto[x-placement^='bottom'] > .arrow::before, .bs-popover-bottom > .arrow::before {top: 0;border-width: 0 0.5rem 0.5rem;border-bottom-color: rgba(0, 0, 0, 0.25);}.bs-popover-auto[x-placement^='bottom'] > .arrow::after, .bs-popover-bottom > .arrow::after {top: 1px;border-width: 0 0.5rem 0.5rem;border-bottom-color: #fff;}.bs-popover-auto[x-placement^='bottom'] .popover-header::before, .bs-popover-bottom .popover-header::before {position: absolute;top: 0;left: 50%;display: block;width: 1rem;margin-left: -0.5rem;content: '';border-bottom: 1px solid #f7f7f7;}.carousel, .carousel-inner, .carousel-item {position: relative;}.bs-popover-auto[x-placement^='left'], .bs-popover-left {margin-right: 0.5rem;}.bs-popover-auto[x-placement^='left'] > .arrow, .bs-popover-left > .arrow {right: calc(-0.5rem - 1px);width: 0.5rem;height: 1rem;margin: 0.3rem 0;}.bs-popover-auto[x-placement^='left'] > .arrow::before, .bs-popover-left > .arrow::before {right: 0;border-width: 0.5rem 0 0.5rem 0.5rem;border-left-color: rgba(0, 0, 0, 0.25);}.bs-popover-auto[x-placement^='left'] > .arrow::after, .bs-popover-left > .arrow::after {right: 1px;border-width: 0.5rem 0 0.5rem 0.5rem;border-left-color: #fff;}.popover-header {padding: 0.5rem 0.75rem;margin-bottom: 0;font-size: 1rem;background-color: #f7f7f7;border-bottom: 1px solid #ebebeb;border-top-left-radius: calc(0.3rem - 1px);border-top-right-radius: calc(0.3rem - 1px);}.popover-header:empty {display: none;}.popover-body {padding: 0.5rem 0.75rem;color: #212529;}.carousel.pointer-event {-ms-touch-action: pan-y;touch-action: pan-y;}.carousel-inner {width: 100%;overflow: hidden;}.carousel-inner::after {display: block;clear: both;}.carousel-item {display: none;float: left;width: 100%;margin-right: -100%;-webkit-backface-visibility: hidden;backface-visibility: hidden;transition: -webkit-transform 0.6s ease-in-out;transition: transform 0.6s ease-in-out;transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce) {.carousel-item {transition: none;}}.carousel-item-next, .carousel-item-prev, .carousel-item.active {display: block;}.active.carousel-item-right, .carousel-item-next:not(.carousel-item-left) {-webkit-transform: translateX(100%);transform: translateX(100%);}.active.carousel-item-left, .carousel-item-prev:not(.carousel-item-right) {-webkit-transform: translateX(-100%);transform: translateX(-100%);}.carousel-fade .carousel-item {opacity: 0;transition-property: opacity;-webkit-transform: none;transform: none;}.carousel-fade .carousel-item-next.carousel-item-left, .carousel-fade .carousel-item-prev.carousel-item-right, .carousel-fade .carousel-item.active {z-index: 1;opacity: 1;}.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right {z-index: 0;opacity: 0;transition: opacity 0s 0.6s;}@media (prefers-reduced-motion: reduce) {.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right {transition: none;}}.carousel-control-next, .carousel-control-prev {position: absolute;top: 0;bottom: 0;z-index: 1;display: -ms-flexbox;display: flex;-ms-flex-align: center;align-items: center;-ms-flex-pack: center;justify-content: center;width: 15%;color: #fff;text-align: center;opacity: 0.5;transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce) {.carousel-control-next, .carousel-control-prev {transition: none;}}.carousel-control-next:focus, .carousel-control-next:hover, .carousel-control-prev:focus, .carousel-control-prev:hover {color: #fff;text-decoration: none;outline: 0;opacity: 0.9;}.carousel-control-prev {left: 0;}.carousel-control-next {right: 0;}.carousel-control-next-icon, .carousel-control-prev-icon {display: inline-block;width: 20px;height: 20px;background: 50%/100% 100% no-repeat;}.carousel-control-prev-icon {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");}.carousel-control-next-icon {background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");}.carousel-indicators {position: absolute;right: 0;bottom: 0;left: 0;z-index: 15;display: -ms-flexbox;display: flex;-ms-flex-pack: center;justify-content: center;padding-left: 0;margin-right: 15%;margin-left: 15%;list-style: none;}.spinner-border, .spinner-grow {display: inline-block;vertical-align: text-bottom;}.carousel-indicators li {box-sizing: content-box;-ms-flex: 0 1 auto;flex: 0 1 auto;width: 30px;height: 3px;margin-right: 3px;margin-left: 3px;text-indent: -999px;cursor: pointer;background-color: #fff;background-clip: padding-box;border-top: 10px solid transparent;border-bottom: 10px solid transparent;opacity: 0.5;transition: opacity 0.6s ease;}*, .plyr--full-ui {box-sizing: border-box;}@media (prefers-reduced-motion: reduce) {.carousel-indicators li {transition: none;}}.carousel-indicators .active {opacity: 1;}.carousel-caption {position: absolute;right: 15%;bottom: 20px;left: 15%;z-index: 10;padding-top: 20px;padding-bottom: 20px;color: #fff;text-align: center;}@-webkit-keyframes spinner-border {to {-webkit-transform: rotate(360deg);transform: rotate(360deg);}}@keyframes spinner-border {to {-webkit-transform: rotate(360deg);transform: rotate(360deg);}}.spinner-border {width: 2rem;height: 2rem;border: 0.25em solid currentColor;border-right-color: transparent;border-radius: 50%;-webkit-animation: spinner-border 0.75s linear infinite;animation: spinner-border 0.75s linear infinite;}.spinner-border-sm {width: 1rem;height: 1rem;border-width: 0.2em;}@-webkit-keyframes spinner-grow {0% {-webkit-transform: scale(0);transform: scale(0);}50% {opacity: 1;}}@keyframes spinner-grow {0% {-webkit-transform: scale(0);transform: scale(0);}50% {opacity: 1;}}.spinner-grow {width: 2rem;height: 2rem;background-color: currentColor;border-radius: 50%;opacity: 0;-webkit-animation: spinner-grow 0.75s linear infinite;animation: spinner-grow 0.75s linear infinite;}.spinner-grow-sm {width: 1rem;height: 1rem;}.align-baseline {vertical-align: baseline !important;}.align-top {vertical-align: top !important;}.align-middle {vertical-align: middle !important;}.align-bottom {vertical-align: bottom !important;}.align-text-bottom {vertical-align: text-bottom !important;}.align-text-top {vertical-align: text-top !important;}.bg-primary {background-color: #007bff !important;}a.bg-primary:focus, a.bg-primary:hover, button.bg-primary:focus, button.bg-primary:hover {background-color: #0062cc !important;}.bg-secondary {background-color: #6c757d !important;}a.bg-secondary:focus, a.bg-secondary:hover, button.bg-secondary:focus, button.bg-secondary:hover {background-color: #545b62 !important;}.bg-success {background-color: #28a745 !important;}a.bg-success:focus, a.bg-success:hover, button.bg-success:focus, button.bg-success:hover {background-color: #1e7e34 !important;}.bg-info {background-color: #17a2b8 !important;}a.bg-info:focus, a.bg-info:hover, button.bg-info:focus, button.bg-info:hover {background-color: #117a8b !important;}.bg-warning {background-color: #ffc107 !important;}a.bg-warning:focus, a.bg-warning:hover, button.bg-warning:focus, button.bg-warning:hover {background-color: #d39e00 !important;}.bg-danger {background-color: #dc3545 !important;}a.bg-danger:focus, a.bg-danger:hover, button.bg-danger:focus, button.bg-danger:hover {background-color: #bd2130 !important;}.bg-light {background-color: #f8f9fa !important;}a.bg-light:focus, a.bg-light:hover, button.bg-light:focus, button.bg-light:hover {background-color: #dae0e5 !important;}.bg-dark {background-color: #343a40 !important;}a.bg-dark:focus, a.bg-dark:hover, button.bg-dark:focus, button.bg-dark:hover {background-color: #1d2124 !important;}.bg-white {background-color: #fff !important;}.bg-transparent {background-color: transparent !important;}.border {border: 1px solid #dee2e6 !important;}.border-top {border-top: 1px solid #dee2e6 !important;}.border-right {border-right: 1px solid #dee2e6 !important;}.border-bottom {border-bottom: 1px solid #dee2e6 !important;}.border-left {border-left: 1px solid #dee2e6 !important;}.border-0 {border: 0 !important;}.border-top-0 {border-top: 0 !important;}.border-right-0 {border-right: 0 !important;}.border-bottom-0 {border-bottom: 0 !important;}.border-left-0 {border-left: 0 !important;}.border-primary {border-color: #007bff !important;}.border-secondary {border-color: #6c757d !important;}.border-success {border-color: #28a745 !important;}.border-info {border-color: #17a2b8 !important;}.border-warning {border-color: #ffc107 !important;}.border-danger {border-color: #dc3545 !important;}.border-light {border-color: #f8f9fa !important;}.border-dark {border-color: #343a40 !important;}.border-white {border-color: #fff !important;}.rounded-sm {border-radius: 0.2rem !important;}.rounded-right, .rounded-top {border-top-right-radius: 0.25rem !important;}.rounded-bottom, .rounded-right {border-bottom-right-radius: 0.25rem !important;}.rounded-left, .rounded-top {border-top-left-radius: 0.25rem !important;}.rounded-bottom, .rounded-left {border-bottom-left-radius: 0.25rem !important;}.rounded {border-radius: 0.25rem !important;}.rounded-lg {border-radius: 0.3rem !important;}.rounded-circle {border-radius: 50% !important;}.rounded-pill {border-radius: 50rem !important;}.rounded-0 {border-radius: 0 !important;}.clearfix::after {display: block;clear: both;}.d-none {display: none !important;}.d-inline {display: inline !important;}.d-inline-block {display: inline-block !important;}.d-block {display: block !important;}.d-table {display: table !important;}.d-table-row {display: table-row !important;}.d-table-cell {display: table-cell !important;}.d-flex {display: -ms-flexbox !important;display: flex !important;}.d-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}@media (min-width: 576px) {.d-sm-none {display: none !important;}.d-sm-inline {display: inline !important;}.d-sm-inline-block {display: inline-block !important;}.d-sm-block {display: block !important;}.d-sm-table {display: table !important;}.d-sm-table-row {display: table-row !important;}.d-sm-table-cell {display: table-cell !important;}.d-sm-flex {display: -ms-flexbox !important;display: flex !important;}.d-sm-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}}@media (min-width: 768px) {.d-md-none {display: none !important;}.d-md-inline {display: inline !important;}.d-md-inline-block {display: inline-block !important;}.d-md-block {display: block !important;}.d-md-table {display: table !important;}.d-md-table-row {display: table-row !important;}.d-md-table-cell {display: table-cell !important;}.d-md-flex {display: -ms-flexbox !important;display: flex !important;}.d-md-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}}@media (min-width: 992px) {.d-lg-none {display: none !important;}.d-lg-inline {display: inline !important;}.d-lg-inline-block {display: inline-block !important;}.d-lg-block {display: block !important;}.d-lg-table {display: table !important;}.d-lg-table-row {display: table-row !important;}.d-lg-table-cell {display: table-cell !important;}.d-lg-flex {display: -ms-flexbox !important;display: flex !important;}.d-lg-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}}@media (min-width: 1200px) {.d-xl-none {display: none !important;}.d-xl-inline {display: inline !important;}.d-xl-inline-block {display: inline-block !important;}.d-xl-block {display: block !important;}.d-xl-table {display: table !important;}.d-xl-table-row {display: table-row !important;}.d-xl-table-cell {display: table-cell !important;}.d-xl-flex {display: -ms-flexbox !important;display: flex !important;}.d-xl-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}}@media print {.d-print-none {display: none !important;}.d-print-inline {display: inline !important;}.d-print-inline-block {display: inline-block !important;}.d-print-block {display: block !important;}.d-print-table {display: table !important;}.d-print-table-row {display: table-row !important;}.d-print-table-cell {display: table-cell !important;}.d-print-flex {display: -ms-flexbox !important;display: flex !important;}.d-print-inline-flex {display: -ms-inline-flexbox !important;display: inline-flex !important;}}.embed-responsive {position: relative;display: block;width: 100%;padding: 0;overflow: hidden;}.embed-responsive::before {display: block;}.embed-responsive .embed-responsive-item, .embed-responsive embed, .embed-responsive iframe, .embed-responsive object, .embed-responsive video {position: absolute;top: 0;bottom: 0;left: 0;width: 100%;height: 100%;border: 0;}.embed-responsive-21by9::before {padding-top: 42.857143%;}.embed-responsive-16by9::before {padding-top: 56.25%;}.embed-responsive-4by3::before {padding-top: 75%;}.embed-responsive-1by1::before {padding-top: 100%;}.flex-row {-ms-flex-direction: row !important;flex-direction: row !important;}.flex-column {-ms-flex-direction: column !important;flex-direction: column !important;}.flex-row-reverse {-ms-flex-direction: row-reverse !important;flex-direction: row-reverse !important;}.flex-column-reverse {-ms-flex-direction: column-reverse !important;flex-direction: column-reverse !important;}.flex-wrap {-ms-flex-wrap: wrap !important;flex-wrap: wrap !important;}.flex-nowrap {-ms-flex-wrap: nowrap !important;flex-wrap: nowrap !important;}.flex-wrap-reverse {-ms-flex-wrap: wrap-reverse !important;flex-wrap: wrap-reverse !important;}.flex-fill {-ms-flex: 1 1 auto !important;flex: 1 1 auto !important;}.flex-grow-0 {-ms-flex-positive: 0 !important;flex-grow: 0 !important;}.flex-grow-1 {-ms-flex-positive: 1 !important;flex-grow: 1 !important;}.flex-shrink-0 {-ms-flex-negative: 0 !important;flex-shrink: 0 !important;}.flex-shrink-1 {-ms-flex-negative: 1 !important;flex-shrink: 1 !important;}.justify-content-start {-ms-flex-pack: start !important;justify-content: flex-start !important;}.justify-content-end {-ms-flex-pack: end !important;justify-content: flex-end !important;}.justify-content-center {-ms-flex-pack: center !important;justify-content: center !important;}.justify-content-between {-ms-flex-pack: justify !important;justify-content: space-between !important;}.justify-content-around {-ms-flex-pack: distribute !important;justify-content: space-around !important;}.align-items-start {-ms-flex-align: start !important;align-items: flex-start !important;}.align-items-end {-ms-flex-align: end !important;align-items: flex-end !important;}.align-items-center {-ms-flex-align: center !important;align-items: center !important;}.align-items-baseline {-ms-flex-align: baseline !important;align-items: baseline !important;}.align-items-stretch {-ms-flex-align: stretch !important;align-items: stretch !important;}.align-content-start {-ms-flex-line-pack: start !important;align-content: flex-start !important;}.align-content-end {-ms-flex-line-pack: end !important;align-content: flex-end !important;}.align-content-center {-ms-flex-line-pack: center !important;align-content: center !important;}.align-content-between {-ms-flex-line-pack: justify !important;align-content: space-between !important;}.align-content-around {-ms-flex-line-pack: distribute !important;align-content: space-around !important;}.align-content-stretch {-ms-flex-line-pack: stretch !important;align-content: stretch !important;}.align-self-auto {-ms-flex-item-align: auto !important;align-self: auto !important;}.align-self-start {-ms-flex-item-align: start !important;align-self: flex-start !important;}.align-self-end {-ms-flex-item-align: end !important;align-self: flex-end !important;}.align-self-center {-ms-flex-item-align: center !important;align-self: center !important;}.align-self-baseline {-ms-flex-item-align: baseline !important;align-self: baseline !important;}.align-self-stretch {-ms-flex-item-align: stretch !important;align-self: stretch !important;}@media (min-width: 576px) {.flex-sm-row {-ms-flex-direction: row !important;flex-direction: row !important;}.flex-sm-column {-ms-flex-direction: column !important;flex-direction: column !important;}.flex-sm-row-reverse {-ms-flex-direction: row-reverse !important;flex-direction: row-reverse !important;}.flex-sm-column-reverse {-ms-flex-direction: column-reverse !important;flex-direction: column-reverse !important;}.flex-sm-wrap {-ms-flex-wrap: wrap !important;flex-wrap: wrap !important;}.flex-sm-nowrap {-ms-flex-wrap: nowrap !important;flex-wrap: nowrap !important;}.flex-sm-wrap-reverse {-ms-flex-wrap: wrap-reverse !important;flex-wrap: wrap-reverse !important;}.flex-sm-fill {-ms-flex: 1 1 auto !important;flex: 1 1 auto !important;}.flex-sm-grow-0 {-ms-flex-positive: 0 !important;flex-grow: 0 !important;}.flex-sm-grow-1 {-ms-flex-positive: 1 !important;flex-grow: 1 !important;}.flex-sm-shrink-0 {-ms-flex-negative: 0 !important;flex-shrink: 0 !important;}.flex-sm-shrink-1 {-ms-flex-negative: 1 !important;flex-shrink: 1 !important;}.justify-content-sm-start {-ms-flex-pack: start !important;justify-content: flex-start !important;}.justify-content-sm-end {-ms-flex-pack: end !important;justify-content: flex-end !important;}.justify-content-sm-center {-ms-flex-pack: center !important;justify-content: center !important;}.justify-content-sm-between {-ms-flex-pack: justify !important;justify-content: space-between !important;}.justify-content-sm-around {-ms-flex-pack: distribute !important;justify-content: space-around !important;}.align-items-sm-start {-ms-flex-align: start !important;align-items: flex-start !important;}.align-items-sm-end {-ms-flex-align: end !important;align-items: flex-end !important;}.align-items-sm-center {-ms-flex-align: center !important;align-items: center !important;}.align-items-sm-baseline {-ms-flex-align: baseline !important;align-items: baseline !important;}.align-items-sm-stretch {-ms-flex-align: stretch !important;align-items: stretch !important;}.align-content-sm-start {-ms-flex-line-pack: start !important;align-content: flex-start !important;}.align-content-sm-end {-ms-flex-line-pack: end !important;align-content: flex-end !important;}.align-content-sm-center {-ms-flex-line-pack: center !important;align-content: center !important;}.align-content-sm-between {-ms-flex-line-pack: justify !important;align-content: space-between !important;}.align-content-sm-around {-ms-flex-line-pack: distribute !important;align-content: space-around !important;}.align-content-sm-stretch {-ms-flex-line-pack: stretch !important;align-content: stretch !important;}.align-self-sm-auto {-ms-flex-item-align: auto !important;align-self: auto !important;}.align-self-sm-start {-ms-flex-item-align: start !important;align-self: flex-start !important;}.align-self-sm-end {-ms-flex-item-align: end !important;align-self: flex-end !important;}.align-self-sm-center {-ms-flex-item-align: center !important;align-self: center !important;}.align-self-sm-baseline {-ms-flex-item-align: baseline !important;align-self: baseline !important;}.align-self-sm-stretch {-ms-flex-item-align: stretch !important;align-self: stretch !important;}}@media (min-width: 768px) {.flex-md-row {-ms-flex-direction: row !important;flex-direction: row !important;}.flex-md-column {-ms-flex-direction: column !important;flex-direction: column !important;}.flex-md-row-reverse {-ms-flex-direction: row-reverse !important;flex-direction: row-reverse !important;}.flex-md-column-reverse {-ms-flex-direction: column-reverse !important;flex-direction: column-reverse !important;}.flex-md-wrap {-ms-flex-wrap: wrap !important;flex-wrap: wrap !important;}.flex-md-nowrap {-ms-flex-wrap: nowrap !important;flex-wrap: nowrap !important;}.flex-md-wrap-reverse {-ms-flex-wrap: wrap-reverse !important;flex-wrap: wrap-reverse !important;}.flex-md-fill {-ms-flex: 1 1 auto !important;flex: 1 1 auto !important;}.flex-md-grow-0 {-ms-flex-positive: 0 !important;flex-grow: 0 !important;}.flex-md-grow-1 {-ms-flex-positive: 1 !important;flex-grow: 1 !important;}.flex-md-shrink-0 {-ms-flex-negative: 0 !important;flex-shrink: 0 !important;}.flex-md-shrink-1 {-ms-flex-negative: 1 !important;flex-shrink: 1 !important;}.justify-content-md-start {-ms-flex-pack: start !important;justify-content: flex-start !important;}.justify-content-md-end {-ms-flex-pack: end !important;justify-content: flex-end !important;}.justify-content-md-center {-ms-flex-pack: center !important;justify-content: center !important;}.justify-content-md-between {-ms-flex-pack: justify !important;justify-content: space-between !important;}.justify-content-md-around {-ms-flex-pack: distribute !important;justify-content: space-around !important;}.align-items-md-start {-ms-flex-align: start !important;align-items: flex-start !important;}.align-items-md-end {-ms-flex-align: end !important;align-items: flex-end !important;}.align-items-md-center {-ms-flex-align: center !important;align-items: center !important;}.align-items-md-baseline {-ms-flex-align: baseline !important;align-items: baseline !important;}.align-items-md-stretch {-ms-flex-align: stretch !important;align-items: stretch !important;}.align-content-md-start {-ms-flex-line-pack: start !important;align-content: flex-start !important;}.align-content-md-end {-ms-flex-line-pack: end !important;align-content: flex-end !important;}.align-content-md-center {-ms-flex-line-pack: center !important;align-content: center !important;}.align-content-md-between {-ms-flex-line-pack: justify !important;align-content: space-between !important;}.align-content-md-around {-ms-flex-line-pack: distribute !important;align-content: space-around !important;}.align-content-md-stretch {-ms-flex-line-pack: stretch !important;align-content: stretch !important;}.align-self-md-auto {-ms-flex-item-align: auto !important;align-self: auto !important;}.align-self-md-start {-ms-flex-item-align: start !important;align-self: flex-start !important;}.align-self-md-end {-ms-flex-item-align: end !important;align-self: flex-end !important;}.align-self-md-center {-ms-flex-item-align: center !important;align-self: center !important;}.align-self-md-baseline {-ms-flex-item-align: baseline !important;align-self: baseline !important;}.align-self-md-stretch {-ms-flex-item-align: stretch !important;align-self: stretch !important;}}.float-left {float: left !important;}.float-right {float: right !important;}.float-none {float: none !important;}@media (min-width: 576px) {.float-sm-left {float: left !important;}.float-sm-right {float: right !important;}.float-sm-none {float: none !important;}}@media (min-width: 768px) {.float-md-left {float: left !important;}.float-md-right {float: right !important;}.float-md-none {float: none !important;}}.overflow-auto {overflow: auto !important;}.overflow-hidden {overflow: hidden !important;}.position-static {position: static !important;}.position-relative {position: relative !important;}.position-absolute {position: absolute !important;}.position-fixed {position: fixed !important;}.position-sticky {position: -webkit-sticky !important;position: sticky !important;}.fixed-bottom, .fixed-top {position: fixed;z-index: 1030;right: 0;left: 0;}.fixed-top {top: 0;}.fixed-bottom {bottom: 0;}@supports ((position: -webkit-sticky) or (position: sticky)) {.sticky-top {position: -webkit-sticky;position: sticky;top: 0;z-index: 1020;}}.sr-only {white-space: nowrap;}.sr-only-focusable:active, .sr-only-focusable:focus {white-space: normal;}.shadow-sm {box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;}.shadow {box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;}.shadow-lg {box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;}.shadow-none {box-shadow: none !important;}.w-25 {width: 25% !important;}.w-50 {width: 50% !important;}.w-75 {width: 75% !important;}.w-100 {width: 100% !important;}.w-auto {width: auto !important;}.h-25 {height: 25% !important;}.h-50 {height: 50% !important;}.h-75 {height: 75% !important;}.h-100 {height: 100% !important;}.h-auto {height: auto !important;}.mw-100 {max-width: 100% !important;}.mh-100 {max-height: 100% !important;}.min-vw-100 {min-width: 100vw !important;}.min-vh-100 {min-height: 100vh !important;}.vw-100 {width: 100vw !important;}.vh-100 {height: 100vh !important;}.stretched-link::after {position: absolute;top: 0;right: 0;bottom: 0;left: 0;z-index: 1;pointer-events: auto;background-color: rgba(0, 0, 0, 0);}.m-0 {margin: 0 !important;}.mt-0, .my-0 {margin-top: 0 !important;}.mr-0, .mx-0 {margin-right: 0 !important;}.mb-0, .my-0 {margin-bottom: 0 !important;}.ml-0, .mx-0 {margin-left: 0 !important;}.m-1 {margin: 0.25rem !important;}.mt-1, .my-1 {margin-top: 0.25rem !important;}.mr-1, .mx-1 {margin-right: 0.25rem !important;}.mb-1, .my-1 {margin-bottom: 0.25rem !important;}.ml-1, .mx-1 {margin-left: 0.25rem !important;}.m-2 {margin: 0.5rem !important;}.mt-2, .my-2 {margin-top: 0.5rem !important;}.mr-2, .mx-2 {margin-right: 0.5rem !important;}.mb-2, .my-2 {margin-bottom: 0.5rem !important;}.ml-2, .mx-2 {margin-left: 0.5rem !important;}.m-3 {margin: 1rem !important;}.mt-3, .my-3 {margin-top: 1rem !important;}.mr-3, .mx-3 {margin-right: 1rem !important;}.mb-3, .my-3 {margin-bottom: 1rem !important;}.ml-3, .mx-3 {margin-left: 1rem !important;}.m-4 {margin: 1.5rem !important;}.mt-4, .my-4 {margin-top: 1.5rem !important;}.mr-4, .mx-4 {margin-right: 1.5rem !important;}.mb-4, .my-4 {margin-bottom: 1.5rem !important;}.ml-4, .mx-4 {margin-left: 1.5rem !important;}.m-5 {margin: 3rem !important;}.mt-5, .my-5 {margin-top: 3rem !important;}.mr-5, .mx-5 {margin-right: 3rem !important;}.mb-5, .my-5 {margin-bottom: 3rem !important;}.ml-5, .mx-5 {margin-left: 3rem !important;}.p-0 {padding: 0 !important;}.pt-0, .py-0 {padding-top: 0 !important;}.pr-0, .px-0 {padding-right: 0 !important;}.pb-0, .py-0 {padding-bottom: 0 !important;}.pl-0, .px-0 {padding-left: 0 !important;}.p-1 {padding: 0.25rem !important;}.pt-1, .py-1 {padding-top: 0.25rem !important;}.pr-1, .px-1 {padding-right: 0.25rem !important;}.pb-1, .py-1 {padding-bottom: 0.25rem !important;}.pl-1, .px-1 {padding-left: 0.25rem !important;}.p-2 {padding: 0.5rem !important;}.pt-2, .py-2 {padding-top: 0.5rem !important;}.pr-2, .px-2 {padding-right: 0.5rem !important;}.pb-2, .py-2 {padding-bottom: 0.5rem !important;}.pl-2, .px-2 {padding-left: 0.5rem !important;}.p-3 {padding: 1rem !important;}.pt-3, .py-3 {padding-top: 1rem !important;}.pr-3, .px-3 {padding-right: 1rem !important;}.pb-3, .py-3 {padding-bottom: 1rem !important;}.pl-3, .px-3 {padding-left: 1rem !important;}.p-4 {padding: 1.5rem !important;}.pt-4, .py-4 {padding-top: 1.5rem !important;}.pr-4, .px-4 {padding-right: 1.5rem !important;}.pb-4, .py-4 {padding-bottom: 1.5rem !important;}.pl-4, .px-4 {padding-left: 1.5rem !important;}.p-5 {padding: 3rem !important;}.pt-5, .py-5 {padding-top: 3rem !important;}.pr-5, .px-5 {padding-right: 3rem !important;}.pb-5, .py-5 {padding-bottom: 3rem !important;}.pl-5, .px-5 {padding-left: 3rem !important;}.m-n1 {margin: -0.25rem !important;}.mt-n1, .my-n1 {margin-top: -0.25rem !important;}.mr-n1, .mx-n1 {margin-right: -0.25rem !important;}.mb-n1, .my-n1 {margin-bottom: -0.25rem !important;}.ml-n1, .mx-n1 {margin-left: -0.25rem !important;}.m-n2 {margin: -0.5rem !important;}.mt-n2, .my-n2 {margin-top: -0.5rem !important;}.mr-n2, .mx-n2 {margin-right: -0.5rem !important;}.mb-n2, .my-n2 {margin-bottom: -0.5rem !important;}.ml-n2, .mx-n2 {margin-left: -0.5rem !important;}.m-n3 {margin: -1rem !important;}.mt-n3, .my-n3 {margin-top: -1rem !important;}.mr-n3, .mx-n3 {margin-right: -1rem !important;}.mb-n3, .my-n3 {margin-bottom: -1rem !important;}.ml-n3, .mx-n3 {margin-left: -1rem !important;}.m-n4 {margin: -1.5rem !important;}.mt-n4, .my-n4 {margin-top: -1.5rem !important;}.mr-n4, .mx-n4 {margin-right: -1.5rem !important;}.mb-n4, .my-n4 {margin-bottom: -1.5rem !important;}.ml-n4, .mx-n4 {margin-left: -1.5rem !important;}.m-n5 {margin: -3rem !important;}.mt-n5, .my-n5 {margin-top: -3rem !important;}.mr-n5, .mx-n5 {margin-right: -3rem !important;}.mb-n5, .my-n5 {margin-bottom: -3rem !important;}.ml-n5, .mx-n5 {margin-left: -3rem !important;}.m-auto {margin: auto !important;}.mt-auto, .my-auto {margin-top: auto !important;}.mr-auto, .mx-auto {margin-right: auto !important;}.mb-auto, .my-auto {margin-bottom: auto !important;}.ml-auto, .mx-auto {margin-left: auto !important;}@media (min-width: 576px) {.m-sm-0 {margin: 0 !important;}.mt-sm-0, .my-sm-0 {margin-top: 0 !important;}.mr-sm-0, .mx-sm-0 {margin-right: 0 !important;}.mb-sm-0, .my-sm-0 {margin-bottom: 0 !important;}.ml-sm-0, .mx-sm-0 {margin-left: 0 !important;}.m-sm-1 {margin: 0.25rem !important;}.mt-sm-1, .my-sm-1 {margin-top: 0.25rem !important;}.mr-sm-1, .mx-sm-1 {margin-right: 0.25rem !important;}.mb-sm-1, .my-sm-1 {margin-bottom: 0.25rem !important;}.ml-sm-1, .mx-sm-1 {margin-left: 0.25rem !important;}.m-sm-2 {margin: 0.5rem !important;}.mt-sm-2, .my-sm-2 {margin-top: 0.5rem !important;}.mr-sm-2, .mx-sm-2 {margin-right: 0.5rem !important;}.mb-sm-2, .my-sm-2 {margin-bottom: 0.5rem !important;}.ml-sm-2, .mx-sm-2 {margin-left: 0.5rem !important;}.m-sm-3 {margin: 1rem !important;}.mt-sm-3, .my-sm-3 {margin-top: 1rem !important;}.mr-sm-3, .mx-sm-3 {margin-right: 1rem !important;}.mb-sm-3, .my-sm-3 {margin-bottom: 1rem !important;}.ml-sm-3, .mx-sm-3 {margin-left: 1rem !important;}.m-sm-4 {margin: 1.5rem !important;}.mt-sm-4, .my-sm-4 {margin-top: 1.5rem !important;}.mr-sm-4, .mx-sm-4 {margin-right: 1.5rem !important;}.mb-sm-4, .my-sm-4 {margin-bottom: 1.5rem !important;}.ml-sm-4, .mx-sm-4 {margin-left: 1.5rem !important;}.m-sm-5 {margin: 3rem !important;}.mt-sm-5, .my-sm-5 {margin-top: 3rem !important;}.mr-sm-5, .mx-sm-5 {margin-right: 3rem !important;}.mb-sm-5, .my-sm-5 {margin-bottom: 3rem !important;}.ml-sm-5, .mx-sm-5 {margin-left: 3rem !important;}.p-sm-0 {padding: 0 !important;}.pt-sm-0, .py-sm-0 {padding-top: 0 !important;}.pr-sm-0, .px-sm-0 {padding-right: 0 !important;}.pb-sm-0, .py-sm-0 {padding-bottom: 0 !important;}.pl-sm-0, .px-sm-0 {padding-left: 0 !important;}.p-sm-1 {padding: 0.25rem !important;}.pt-sm-1, .py-sm-1 {padding-top: 0.25rem !important;}.pr-sm-1, .px-sm-1 {padding-right: 0.25rem !important;}.pb-sm-1, .py-sm-1 {padding-bottom: 0.25rem !important;}.pl-sm-1, .px-sm-1 {padding-left: 0.25rem !important;}.p-sm-2 {padding: 0.5rem !important;}.pt-sm-2, .py-sm-2 {padding-top: 0.5rem !important;}.pr-sm-2, .px-sm-2 {padding-right: 0.5rem !important;}.pb-sm-2, .py-sm-2 {padding-bottom: 0.5rem !important;}.pl-sm-2, .px-sm-2 {padding-left: 0.5rem !important;}.p-sm-3 {padding: 1rem !important;}.pt-sm-3, .py-sm-3 {padding-top: 1rem !important;}.pr-sm-3, .px-sm-3 {padding-right: 1rem !important;}.pb-sm-3, .py-sm-3 {padding-bottom: 1rem !important;}.pl-sm-3, .px-sm-3 {padding-left: 1rem !important;}.p-sm-4 {padding: 1.5rem !important;}.pt-sm-4, .py-sm-4 {padding-top: 1.5rem !important;}.pr-sm-4, .px-sm-4 {padding-right: 1.5rem !important;}.pb-sm-4, .py-sm-4 {padding-bottom: 1.5rem !important;}.pl-sm-4, .px-sm-4 {padding-left: 1.5rem !important;}.p-sm-5 {padding: 3rem !important;}.pt-sm-5, .py-sm-5 {padding-top: 3rem !important;}.pr-sm-5, .px-sm-5 {padding-right: 3rem !important;}.pb-sm-5, .py-sm-5 {padding-bottom: 3rem !important;}.pl-sm-5, .px-sm-5 {padding-left: 3rem !important;}.m-sm-n1 {margin: -0.25rem !important;}.mt-sm-n1, .my-sm-n1 {margin-top: -0.25rem !important;}.mr-sm-n1, .mx-sm-n1 {margin-right: -0.25rem !important;}.mb-sm-n1, .my-sm-n1 {margin-bottom: -0.25rem !important;}.ml-sm-n1, .mx-sm-n1 {margin-left: -0.25rem !important;}.m-sm-n2 {margin: -0.5rem !important;}.mt-sm-n2, .my-sm-n2 {margin-top: -0.5rem !important;}.mr-sm-n2, .mx-sm-n2 {margin-right: -0.5rem !important;}.mb-sm-n2, .my-sm-n2 {margin-bottom: -0.5rem !important;}.ml-sm-n2, .mx-sm-n2 {margin-left: -0.5rem !important;}.m-sm-n3 {margin: -1rem !important;}.mt-sm-n3, .my-sm-n3 {margin-top: -1rem !important;}.mr-sm-n3, .mx-sm-n3 {margin-right: -1rem !important;}.mb-sm-n3, .my-sm-n3 {margin-bottom: -1rem !important;}.ml-sm-n3, .mx-sm-n3 {margin-left: -1rem !important;}.m-sm-n4 {margin: -1.5rem !important;}.mt-sm-n4, .my-sm-n4 {margin-top: -1.5rem !important;}.mr-sm-n4, .mx-sm-n4 {margin-right: -1.5rem !important;}.mb-sm-n4, .my-sm-n4 {margin-bottom: -1.5rem !important;}.ml-sm-n4, .mx-sm-n4 {margin-left: -1.5rem !important;}.m-sm-n5 {margin: -3rem !important;}.mt-sm-n5, .my-sm-n5 {margin-top: -3rem !important;}.mr-sm-n5, .mx-sm-n5 {margin-right: -3rem !important;}.mb-sm-n5, .my-sm-n5 {margin-bottom: -3rem !important;}.ml-sm-n5, .mx-sm-n5 {margin-left: -3rem !important;}.m-sm-auto {margin: auto !important;}.mt-sm-auto, .my-sm-auto {margin-top: auto !important;}.mr-sm-auto, .mx-sm-auto {margin-right: auto !important;}.mb-sm-auto, .my-sm-auto {margin-bottom: auto !important;}.ml-sm-auto, .mx-sm-auto {margin-left: auto !important;}}@media (min-width: 768px) {.m-md-0 {margin: 0 !important;}.mt-md-0, .my-md-0 {margin-top: 0 !important;}.mr-md-0, .mx-md-0 {margin-right: 0 !important;}.mb-md-0, .my-md-0 {margin-bottom: 0 !important;}.ml-md-0, .mx-md-0 {margin-left: 0 !important;}.m-md-1 {margin: 0.25rem !important;}.mt-md-1, .my-md-1 {margin-top: 0.25rem !important;}.mr-md-1, .mx-md-1 {margin-right: 0.25rem !important;}.mb-md-1, .my-md-1 {margin-bottom: 0.25rem !important;}.ml-md-1, .mx-md-1 {margin-left: 0.25rem !important;}.m-md-2 {margin: 0.5rem !important;}.mt-md-2, .my-md-2 {margin-top: 0.5rem !important;}.mr-md-2, .mx-md-2 {margin-right: 0.5rem !important;}.mb-md-2, .my-md-2 {margin-bottom: 0.5rem !important;}.ml-md-2, .mx-md-2 {margin-left: 0.5rem !important;}.m-md-3 {margin: 1rem !important;}.mt-md-3, .my-md-3 {margin-top: 1rem !important;}.mr-md-3, .mx-md-3 {margin-right: 1rem !important;}.mb-md-3, .my-md-3 {margin-bottom: 1rem !important;}.ml-md-3, .mx-md-3 {margin-left: 1rem !important;}.m-md-4 {margin: 1.5rem !important;}.mt-md-4, .my-md-4 {margin-top: 1.5rem !important;}.mr-md-4, .mx-md-4 {margin-right: 1.5rem !important;}.mb-md-4, .my-md-4 {margin-bottom: 1.5rem !important;}.ml-md-4, .mx-md-4 {margin-left: 1.5rem !important;}.m-md-5 {margin: 3rem !important;}.mt-md-5, .my-md-5 {margin-top: 3rem !important;}.mr-md-5, .mx-md-5 {margin-right: 3rem !important;}.mb-md-5, .my-md-5 {margin-bottom: 3rem !important;}.ml-md-5, .mx-md-5 {margin-left: 3rem !important;}.p-md-0 {padding: 0 !important;}.pt-md-0, .py-md-0 {padding-top: 0 !important;}.pr-md-0, .px-md-0 {padding-right: 0 !important;}.pb-md-0, .py-md-0 {padding-bottom: 0 !important;}.pl-md-0, .px-md-0 {padding-left: 0 !important;}.p-md-1 {padding: 0.25rem !important;}.pt-md-1, .py-md-1 {padding-top: 0.25rem !important;}.pr-md-1, .px-md-1 {padding-right: 0.25rem !important;}.pb-md-1, .py-md-1 {padding-bottom: 0.25rem !important;}.pl-md-1, .px-md-1 {padding-left: 0.25rem !important;}.p-md-2 {padding: 0.5rem !important;}.pt-md-2, .py-md-2 {padding-top: 0.5rem !important;}.pr-md-2, .px-md-2 {padding-right: 0.5rem !important;}.pb-md-2, .py-md-2 {padding-bottom: 0.5rem !important;}.pl-md-2, .px-md-2 {padding-left: 0.5rem !important;}.p-md-3 {padding: 1rem !important;}.pt-md-3, .py-md-3 {padding-top: 1rem !important;}.pr-md-3, .px-md-3 {padding-right: 1rem !important;}.pb-md-3, .py-md-3 {padding-bottom: 1rem !important;}.pl-md-3, .px-md-3 {padding-left: 1rem !important;}.p-md-4 {padding: 1.5rem !important;}.pt-md-4, .py-md-4 {padding-top: 1.5rem !important;}.pr-md-4, .px-md-4 {padding-right: 1.5rem !important;}.pb-md-4, .py-md-4 {padding-bottom: 1.5rem !important;}.pl-md-4, .px-md-4 {padding-left: 1.5rem !important;}.p-md-5 {padding: 3rem !important;}.pt-md-5, .py-md-5 {padding-top: 3rem !important;}.pr-md-5, .px-md-5 {padding-right: 3rem !important;}.pb-md-5, .py-md-5 {padding-bottom: 3rem !important;}.pl-md-5, .px-md-5 {padding-left: 3rem !important;}.m-md-n1 {margin: -0.25rem !important;}.mt-md-n1, .my-md-n1 {margin-top: -0.25rem !important;}.mr-md-n1, .mx-md-n1 {margin-right: -0.25rem !important;}.mb-md-n1, .my-md-n1 {margin-bottom: -0.25rem !important;}.ml-md-n1, .mx-md-n1 {margin-left: -0.25rem !important;}.m-md-n2 {margin: -0.5rem !important;}.mt-md-n2, .my-md-n2 {margin-top: -0.5rem !important;}.mr-md-n2, .mx-md-n2 {margin-right: -0.5rem !important;}.mb-md-n2, .my-md-n2 {margin-bottom: -0.5rem !important;}.ml-md-n2, .mx-md-n2 {margin-left: -0.5rem !important;}.m-md-n3 {margin: -1rem !important;}.mt-md-n3, .my-md-n3 {margin-top: -1rem !important;}.mr-md-n3, .mx-md-n3 {margin-right: -1rem !important;}.mb-md-n3, .my-md-n3 {margin-bottom: -1rem !important;}.ml-md-n3, .mx-md-n3 {margin-left: -1rem !important;}.m-md-n4 {margin: -1.5rem !important;}.mt-md-n4, .my-md-n4 {margin-top: -1.5rem !important;}.mr-md-n4, .mx-md-n4 {margin-right: -1.5rem !important;}.mb-md-n4, .my-md-n4 {margin-bottom: -1.5rem !important;}.ml-md-n4, .mx-md-n4 {margin-left: -1.5rem !important;}.m-md-n5 {margin: -3rem !important;}.mt-md-n5, .my-md-n5 {margin-top: -3rem !important;}.mr-md-n5, .mx-md-n5 {margin-right: -3rem !important;}.mb-md-n5, .my-md-n5 {margin-bottom: -3rem !important;}.ml-md-n5, .mx-md-n5 {margin-left: -3rem !important;}.m-md-auto {margin: auto !important;}.mt-md-auto, .my-md-auto {margin-top: auto !important;}.mr-md-auto, .mx-md-auto {margin-right: auto !important;}.mb-md-auto, .my-md-auto {margin-bottom: auto !important;}.ml-md-auto, .mx-md-auto {margin-left: auto !important;}}@media (min-width: 992px) {.flex-lg-row {-ms-flex-direction: row !important;flex-direction: row !important;}.flex-lg-column {-ms-flex-direction: column !important;flex-direction: column !important;}.flex-lg-row-reverse {-ms-flex-direction: row-reverse !important;flex-direction: row-reverse !important;}.flex-lg-column-reverse {-ms-flex-direction: column-reverse !important;flex-direction: column-reverse !important;}.flex-lg-wrap {-ms-flex-wrap: wrap !important;flex-wrap: wrap !important;}.flex-lg-nowrap {-ms-flex-wrap: nowrap !important;flex-wrap: nowrap !important;}.flex-lg-wrap-reverse {-ms-flex-wrap: wrap-reverse !important;flex-wrap: wrap-reverse !important;}.flex-lg-fill {-ms-flex: 1 1 auto !important;flex: 1 1 auto !important;}.flex-lg-grow-0 {-ms-flex-positive: 0 !important;flex-grow: 0 !important;}.flex-lg-grow-1 {-ms-flex-positive: 1 !important;flex-grow: 1 !important;}.flex-lg-shrink-0 {-ms-flex-negative: 0 !important;flex-shrink: 0 !important;}.flex-lg-shrink-1 {-ms-flex-negative: 1 !important;flex-shrink: 1 !important;}.justify-content-lg-start {-ms-flex-pack: start !important;justify-content: flex-start !important;}.justify-content-lg-end {-ms-flex-pack: end !important;justify-content: flex-end !important;}.justify-content-lg-center {-ms-flex-pack: center !important;justify-content: center !important;}.justify-content-lg-between {-ms-flex-pack: justify !important;justify-content: space-between !important;}.justify-content-lg-around {-ms-flex-pack: distribute !important;justify-content: space-around !important;}.align-items-lg-start {-ms-flex-align: start !important;align-items: flex-start !important;}.align-items-lg-end {-ms-flex-align: end !important;align-items: flex-end !important;}.align-items-lg-center {-ms-flex-align: center !important;align-items: center !important;}.align-items-lg-baseline {-ms-flex-align: baseline !important;align-items: baseline !important;}.align-items-lg-stretch {-ms-flex-align: stretch !important;align-items: stretch !important;}.align-content-lg-start {-ms-flex-line-pack: start !important;align-content: flex-start !important;}.align-content-lg-end {-ms-flex-line-pack: end !important;align-content: flex-end !important;}.align-content-lg-center {-ms-flex-line-pack: center !important;align-content: center !important;}.align-content-lg-between {-ms-flex-line-pack: justify !important;align-content: space-between !important;}.align-content-lg-around {-ms-flex-line-pack: distribute !important;align-content: space-around !important;}.align-content-lg-stretch {-ms-flex-line-pack: stretch !important;align-content: stretch !important;}.align-self-lg-auto {-ms-flex-item-align: auto !important;align-self: auto !important;}.align-self-lg-start {-ms-flex-item-align: start !important;align-self: flex-start !important;}.align-self-lg-end {-ms-flex-item-align: end !important;align-self: flex-end !important;}.align-self-lg-center {-ms-flex-item-align: center !important;align-self: center !important;}.align-self-lg-baseline {-ms-flex-item-align: baseline !important;align-self: baseline !important;}.align-self-lg-stretch {-ms-flex-item-align: stretch !important;align-self: stretch !important;}.float-lg-left {float: left !important;}.float-lg-right {float: right !important;}.float-lg-none {float: none !important;}.m-lg-0 {margin: 0 !important;}.mt-lg-0, .my-lg-0 {margin-top: 0 !important;}.mr-lg-0, .mx-lg-0 {margin-right: 0 !important;}.mb-lg-0, .my-lg-0 {margin-bottom: 0 !important;}.ml-lg-0, .mx-lg-0 {margin-left: 0 !important;}.m-lg-1 {margin: 0.25rem !important;}.mt-lg-1, .my-lg-1 {margin-top: 0.25rem !important;}.mr-lg-1, .mx-lg-1 {margin-right: 0.25rem !important;}.mb-lg-1, .my-lg-1 {margin-bottom: 0.25rem !important;}.ml-lg-1, .mx-lg-1 {margin-left: 0.25rem !important;}.m-lg-2 {margin: 0.5rem !important;}.mt-lg-2, .my-lg-2 {margin-top: 0.5rem !important;}.mr-lg-2, .mx-lg-2 {margin-right: 0.5rem !important;}.mb-lg-2, .my-lg-2 {margin-bottom: 0.5rem !important;}.ml-lg-2, .mx-lg-2 {margin-left: 0.5rem !important;}.m-lg-3 {margin: 1rem !important;}.mt-lg-3, .my-lg-3 {margin-top: 1rem !important;}.mr-lg-3, .mx-lg-3 {margin-right: 1rem !important;}.mb-lg-3, .my-lg-3 {margin-bottom: 1rem !important;}.ml-lg-3, .mx-lg-3 {margin-left: 1rem !important;}.m-lg-4 {margin: 1.5rem !important;}.mt-lg-4, .my-lg-4 {margin-top: 1.5rem !important;}.mr-lg-4, .mx-lg-4 {margin-right: 1.5rem !important;}.mb-lg-4, .my-lg-4 {margin-bottom: 1.5rem !important;}.ml-lg-4, .mx-lg-4 {margin-left: 1.5rem !important;}.m-lg-5 {margin: 3rem !important;}.mt-lg-5, .my-lg-5 {margin-top: 3rem !important;}.mr-lg-5, .mx-lg-5 {margin-right: 3rem !important;}.mb-lg-5, .my-lg-5 {margin-bottom: 3rem !important;}.ml-lg-5, .mx-lg-5 {margin-left: 3rem !important;}.p-lg-0 {padding: 0 !important;}.pt-lg-0, .py-lg-0 {padding-top: 0 !important;}.pr-lg-0, .px-lg-0 {padding-right: 0 !important;}.pb-lg-0, .py-lg-0 {padding-bottom: 0 !important;}.pl-lg-0, .px-lg-0 {padding-left: 0 !important;}.p-lg-1 {padding: 0.25rem !important;}.pt-lg-1, .py-lg-1 {padding-top: 0.25rem !important;}.pr-lg-1, .px-lg-1 {padding-right: 0.25rem !important;}.pb-lg-1, .py-lg-1 {padding-bottom: 0.25rem !important;}.pl-lg-1, .px-lg-1 {padding-left: 0.25rem !important;}.p-lg-2 {padding: 0.5rem !important;}.pt-lg-2, .py-lg-2 {padding-top: 0.5rem !important;}.pr-lg-2, .px-lg-2 {padding-right: 0.5rem !important;}.pb-lg-2, .py-lg-2 {padding-bottom: 0.5rem !important;}.pl-lg-2, .px-lg-2 {padding-left: 0.5rem !important;}.p-lg-3 {padding: 1rem !important;}.pt-lg-3, .py-lg-3 {padding-top: 1rem !important;}.pr-lg-3, .px-lg-3 {padding-right: 1rem !important;}.pb-lg-3, .py-lg-3 {padding-bottom: 1rem !important;}.pl-lg-3, .px-lg-3 {padding-left: 1rem !important;}.p-lg-4 {padding: 1.5rem !important;}.pt-lg-4, .py-lg-4 {padding-top: 1.5rem !important;}.pr-lg-4, .px-lg-4 {padding-right: 1.5rem !important;}.pb-lg-4, .py-lg-4 {padding-bottom: 1.5rem !important;}.pl-lg-4, .px-lg-4 {padding-left: 1.5rem !important;}.p-lg-5 {padding: 3rem !important;}.pt-lg-5, .py-lg-5 {padding-top: 3rem !important;}.pr-lg-5, .px-lg-5 {padding-right: 3rem !important;}.pb-lg-5, .py-lg-5 {padding-bottom: 3rem !important;}.pl-lg-5, .px-lg-5 {padding-left: 3rem !important;}.m-lg-n1 {margin: -0.25rem !important;}.mt-lg-n1, .my-lg-n1 {margin-top: -0.25rem !important;}.mr-lg-n1, .mx-lg-n1 {margin-right: -0.25rem !important;}.mb-lg-n1, .my-lg-n1 {margin-bottom: -0.25rem !important;}.ml-lg-n1, .mx-lg-n1 {margin-left: -0.25rem !important;}.m-lg-n2 {margin: -0.5rem !important;}.mt-lg-n2, .my-lg-n2 {margin-top: -0.5rem !important;}.mr-lg-n2, .mx-lg-n2 {margin-right: -0.5rem !important;}.mb-lg-n2, .my-lg-n2 {margin-bottom: -0.5rem !important;}.ml-lg-n2, .mx-lg-n2 {margin-left: -0.5rem !important;}.m-lg-n3 {margin: -1rem !important;}.mt-lg-n3, .my-lg-n3 {margin-top: -1rem !important;}.mr-lg-n3, .mx-lg-n3 {margin-right: -1rem !important;}.mb-lg-n3, .my-lg-n3 {margin-bottom: -1rem !important;}.ml-lg-n3, .mx-lg-n3 {margin-left: -1rem !important;}.m-lg-n4 {margin: -1.5rem !important;}.mt-lg-n4, .my-lg-n4 {margin-top: -1.5rem !important;}.mr-lg-n4, .mx-lg-n4 {margin-right: -1.5rem !important;}.mb-lg-n4, .my-lg-n4 {margin-bottom: -1.5rem !important;}.ml-lg-n4, .mx-lg-n4 {margin-left: -1.5rem !important;}.m-lg-n5 {margin: -3rem !important;}.mt-lg-n5, .my-lg-n5 {margin-top: -3rem !important;}.mr-lg-n5, .mx-lg-n5 {margin-right: -3rem !important;}.mb-lg-n5, .my-lg-n5 {margin-bottom: -3rem !important;}.ml-lg-n5, .mx-lg-n5 {margin-left: -3rem !important;}.m-lg-auto {margin: auto !important;}.mt-lg-auto, .my-lg-auto {margin-top: auto !important;}.mr-lg-auto, .mx-lg-auto {margin-right: auto !important;}.mb-lg-auto, .my-lg-auto {margin-bottom: auto !important;}.ml-lg-auto, .mx-lg-auto {margin-left: auto !important;}}@media (min-width: 1200px) {.flex-xl-row {-ms-flex-direction: row !important;flex-direction: row !important;}.flex-xl-column {-ms-flex-direction: column !important;flex-direction: column !important;}.flex-xl-row-reverse {-ms-flex-direction: row-reverse !important;flex-direction: row-reverse !important;}.flex-xl-column-reverse {-ms-flex-direction: column-reverse !important;flex-direction: column-reverse !important;}.flex-xl-wrap {-ms-flex-wrap: wrap !important;flex-wrap: wrap !important;}.flex-xl-nowrap {-ms-flex-wrap: nowrap !important;flex-wrap: nowrap !important;}.flex-xl-wrap-reverse {-ms-flex-wrap: wrap-reverse !important;flex-wrap: wrap-reverse !important;}.flex-xl-fill {-ms-flex: 1 1 auto !important;flex: 1 1 auto !important;}.flex-xl-grow-0 {-ms-flex-positive: 0 !important;flex-grow: 0 !important;}.flex-xl-grow-1 {-ms-flex-positive: 1 !important;flex-grow: 1 !important;}.flex-xl-shrink-0 {-ms-flex-negative: 0 !important;flex-shrink: 0 !important;}.flex-xl-shrink-1 {-ms-flex-negative: 1 !important;flex-shrink: 1 !important;}.justify-content-xl-start {-ms-flex-pack: start !important;justify-content: flex-start !important;}.justify-content-xl-end {-ms-flex-pack: end !important;justify-content: flex-end !important;}.justify-content-xl-center {-ms-flex-pack: center !important;justify-content: center !important;}.justify-content-xl-between {-ms-flex-pack: justify !important;justify-content: space-between !important;}.justify-content-xl-around {-ms-flex-pack: distribute !important;justify-content: space-around !important;}.align-items-xl-start {-ms-flex-align: start !important;align-items: flex-start !important;}.align-items-xl-end {-ms-flex-align: end !important;align-items: flex-end !important;}.align-items-xl-center {-ms-flex-align: center !important;align-items: center !important;}.align-items-xl-baseline {-ms-flex-align: baseline !important;align-items: baseline !important;}.align-items-xl-stretch {-ms-flex-align: stretch !important;align-items: stretch !important;}.align-content-xl-start {-ms-flex-line-pack: start !important;align-content: flex-start !important;}.align-content-xl-end {-ms-flex-line-pack: end !important;align-content: flex-end !important;}.align-content-xl-center {-ms-flex-line-pack: center !important;align-content: center !important;}.align-content-xl-between {-ms-flex-line-pack: justify !important;align-content: space-between !important;}.align-content-xl-around {-ms-flex-line-pack: distribute !important;align-content: space-around !important;}.align-content-xl-stretch {-ms-flex-line-pack: stretch !important;align-content: stretch !important;}.align-self-xl-auto {-ms-flex-item-align: auto !important;align-self: auto !important;}.align-self-xl-start {-ms-flex-item-align: start !important;align-self: flex-start !important;}.align-self-xl-end {-ms-flex-item-align: end !important;align-self: flex-end !important;}.align-self-xl-center {-ms-flex-item-align: center !important;align-self: center !important;}.align-self-xl-baseline {-ms-flex-item-align: baseline !important;align-self: baseline !important;}.align-self-xl-stretch {-ms-flex-item-align: stretch !important;align-self: stretch !important;}.float-xl-left {float: left !important;}.float-xl-right {float: right !important;}.float-xl-none {float: none !important;}.m-xl-0 {margin: 0 !important;}.mt-xl-0, .my-xl-0 {margin-top: 0 !important;}.mr-xl-0, .mx-xl-0 {margin-right: 0 !important;}.mb-xl-0, .my-xl-0 {margin-bottom: 0 !important;}.ml-xl-0, .mx-xl-0 {margin-left: 0 !important;}.m-xl-1 {margin: 0.25rem !important;}.mt-xl-1, .my-xl-1 {margin-top: 0.25rem !important;}.mr-xl-1, .mx-xl-1 {margin-right: 0.25rem !important;}.mb-xl-1, .my-xl-1 {margin-bottom: 0.25rem !important;}.ml-xl-1, .mx-xl-1 {margin-left: 0.25rem !important;}.m-xl-2 {margin: 0.5rem !important;}.mt-xl-2, .my-xl-2 {margin-top: 0.5rem !important;}.mr-xl-2, .mx-xl-2 {margin-right: 0.5rem !important;}.mb-xl-2, .my-xl-2 {margin-bottom: 0.5rem !important;}.ml-xl-2, .mx-xl-2 {margin-left: 0.5rem !important;}.m-xl-3 {margin: 1rem !important;}.mt-xl-3, .my-xl-3 {margin-top: 1rem !important;}.mr-xl-3, .mx-xl-3 {margin-right: 1rem !important;}.mb-xl-3, .my-xl-3 {margin-bottom: 1rem !important;}.ml-xl-3, .mx-xl-3 {margin-left: 1rem !important;}.m-xl-4 {margin: 1.5rem !important;}.mt-xl-4, .my-xl-4 {margin-top: 1.5rem !important;}.mr-xl-4, .mx-xl-4 {margin-right: 1.5rem !important;}.mb-xl-4, .my-xl-4 {margin-bottom: 1.5rem !important;}.ml-xl-4, .mx-xl-4 {margin-left: 1.5rem !important;}.m-xl-5 {margin: 3rem !important;}.mt-xl-5, .my-xl-5 {margin-top: 3rem !important;}.mr-xl-5, .mx-xl-5 {margin-right: 3rem !important;}.mb-xl-5, .my-xl-5 {margin-bottom: 3rem !important;}.ml-xl-5, .mx-xl-5 {margin-left: 3rem !important;}.p-xl-0 {padding: 0 !important;}.pt-xl-0, .py-xl-0 {padding-top: 0 !important;}.pr-xl-0, .px-xl-0 {padding-right: 0 !important;}.pb-xl-0, .py-xl-0 {padding-bottom: 0 !important;}.pl-xl-0, .px-xl-0 {padding-left: 0 !important;}.p-xl-1 {padding: 0.25rem !important;}.pt-xl-1, .py-xl-1 {padding-top: 0.25rem !important;}.pr-xl-1, .px-xl-1 {padding-right: 0.25rem !important;}.pb-xl-1, .py-xl-1 {padding-bottom: 0.25rem !important;}.pl-xl-1, .px-xl-1 {padding-left: 0.25rem !important;}.p-xl-2 {padding: 0.5rem !important;}.pt-xl-2, .py-xl-2 {padding-top: 0.5rem !important;}.pr-xl-2, .px-xl-2 {padding-right: 0.5rem !important;}.pb-xl-2, .py-xl-2 {padding-bottom: 0.5rem !important;}.pl-xl-2, .px-xl-2 {padding-left: 0.5rem !important;}.p-xl-3 {padding: 1rem !important;}.pt-xl-3, .py-xl-3 {padding-top: 1rem !important;}.pr-xl-3, .px-xl-3 {padding-right: 1rem !important;}.pb-xl-3, .py-xl-3 {padding-bottom: 1rem !important;}.pl-xl-3, .px-xl-3 {padding-left: 1rem !important;}.p-xl-4 {padding: 1.5rem !important;}.pt-xl-4, .py-xl-4 {padding-top: 1.5rem !important;}.pr-xl-4, .px-xl-4 {padding-right: 1.5rem !important;}.pb-xl-4, .py-xl-4 {padding-bottom: 1.5rem !important;}.pl-xl-4, .px-xl-4 {padding-left: 1.5rem !important;}.p-xl-5 {padding: 3rem !important;}.pt-xl-5, .py-xl-5 {padding-top: 3rem !important;}.pr-xl-5, .px-xl-5 {padding-right: 3rem !important;}.pb-xl-5, .py-xl-5 {padding-bottom: 3rem !important;}.pl-xl-5, .px-xl-5 {padding-left: 3rem !important;}.m-xl-n1 {margin: -0.25rem !important;}.mt-xl-n1, .my-xl-n1 {margin-top: -0.25rem !important;}.mr-xl-n1, .mx-xl-n1 {margin-right: -0.25rem !important;}.mb-xl-n1, .my-xl-n1 {margin-bottom: -0.25rem !important;}.ml-xl-n1, .mx-xl-n1 {margin-left: -0.25rem !important;}.m-xl-n2 {margin: -0.5rem !important;}.mt-xl-n2, .my-xl-n2 {margin-top: -0.5rem !important;}.mr-xl-n2, .mx-xl-n2 {margin-right: -0.5rem !important;}.mb-xl-n2, .my-xl-n2 {margin-bottom: -0.5rem !important;}.ml-xl-n2, .mx-xl-n2 {margin-left: -0.5rem !important;}.m-xl-n3 {margin: -1rem !important;}.mt-xl-n3, .my-xl-n3 {margin-top: -1rem !important;}.mr-xl-n3, .mx-xl-n3 {margin-right: -1rem !important;}.mb-xl-n3, .my-xl-n3 {margin-bottom: -1rem !important;}.ml-xl-n3, .mx-xl-n3 {margin-left: -1rem !important;}.m-xl-n4 {margin: -1.5rem !important;}.mt-xl-n4, .my-xl-n4 {margin-top: -1.5rem !important;}.mr-xl-n4, .mx-xl-n4 {margin-right: -1.5rem !important;}.mb-xl-n4, .my-xl-n4 {margin-bottom: -1.5rem !important;}.ml-xl-n4, .mx-xl-n4 {margin-left: -1.5rem !important;}.m-xl-n5 {margin: -3rem !important;}.mt-xl-n5, .my-xl-n5 {margin-top: -3rem !important;}.mr-xl-n5, .mx-xl-n5 {margin-right: -3rem !important;}.mb-xl-n5, .my-xl-n5 {margin-bottom: -3rem !important;}.ml-xl-n5, .mx-xl-n5 {margin-left: -3rem !important;}.m-xl-auto {margin: auto !important;}.mt-xl-auto, .my-xl-auto {margin-top: auto !important;}.mr-xl-auto, .mx-xl-auto {margin-right: auto !important;}.mb-xl-auto, .my-xl-auto {margin-bottom: auto !important;}.ml-xl-auto, .mx-xl-auto {margin-left: auto !important;}}.core-content.open-side-menu, .core-content.open-side-menu nav.nav-core-sticky.on-scroll {margin-left: -280px;margin-right: 280px;}.text-monospace {font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;}.text-justify {text-align: justify !important;}.text-wrap {white-space: normal !important;}.text-nowrap {white-space: nowrap !important;}.text-truncate {overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}.text-left {text-align: left !important;}.text-right {text-align: right !important;}.text-center {text-align: center !important;}@media (min-width: 576px) {.text-sm-left {text-align: left !important;}.text-sm-right {text-align: right !important;}.text-sm-center {text-align: center !important;}}@media (min-width: 768px) {.text-md-left {text-align: left !important;}.text-md-right {text-align: right !important;}.text-md-center {text-align: center !important;}}@media (min-width: 992px) {.text-lg-left {text-align: left !important;}.text-lg-right {text-align: right !important;}.text-lg-center {text-align: center !important;}}@media (min-width: 1200px) {.text-xl-left {text-align: left !important;}.text-xl-right {text-align: right !important;}.text-xl-center {text-align: center !important;}}.text-lowercase {text-transform: lowercase !important;}.text-uppercase {text-transform: uppercase !important;}.text-capitalize {text-transform: capitalize !important;}.font-weight-light {font-weight: 300 !important;}.font-weight-lighter {font-weight: lighter !important;}.font-weight-normal {font-weight: 400 !important;}.font-weight-bold {font-weight: 700 !important;}.font-weight-bolder {font-weight: bolder !important;}.font-italic {font-style: italic !important;}.text-white {color: #fff !important;}.text-primary {color: #007bff !important;}a.text-primary:focus, a.text-primary:hover {color: #0056b3 !important;}.text-secondary {color: #6c757d !important;}a.text-secondary:focus, a.text-secondary:hover {color: #494f54 !important;}.text-success {color: #28a745 !important;}a.text-success:focus, a.text-success:hover {color: #19692c !important;}.text-info {color: #17a2b8 !important;}a.text-info:focus, a.text-info:hover {color: #0f6674 !important;}.text-warning {color: #ffc107 !important;}a.text-warning:focus, a.text-warning:hover {color: #ba8b00 !important;}.text-danger {color: #dc3545 !important;}a.text-danger:focus, a.text-danger:hover {color: #a71d2a !important;}.text-light {color: #f8f9fa !important;}a.text-light:focus, a.text-light:hover {color: #cbd3da !important;}.text-dark {color: #343a40 !important;}a.text-dark:focus, a.text-dark:hover {color: #121416 !important;}.text-body {color: #212529 !important;}.text-muted {color: #6c757d !important;}.text-black-50 {color: rgba(0, 0, 0, 0.5) !important;}.text-white-50 {color: rgba(255, 255, 255, 0.5) !important;}.text-hide {font: 0/0 a;color: transparent;background-color: transparent;border: 0;}.text-decoration-none {text-decoration: none !important;}.text-break {word-break: break-word !important;overflow-wrap: break-word !important;}.text-reset {color: inherit !important;}.visible {visibility: visible !important;}.invisible {visibility: hidden !important;}@media print {blockquote, img, pre, tr {page-break-inside: avoid;}*, ::after, ::before {text-shadow: none !important;box-shadow: none !important;}a:not(.btn) {text-decoration: underline;}abbr[title]::after {content: ' (' attr(title) ')';}pre {white-space: pre-wrap !important;}blockquote, pre {border: 1px solid #adb5bd;}thead {display: table-header-group;}h2, h3, p {orphans: 3;widows: 3;}h2, h3 {page-break-after: avoid;}@page {size: a3;}.container, body {min-width: 992px !important;}.navbar {display: none;}.badge {border: 1px solid #000;}.table {border-collapse: collapse !important;}.table td, .table th {background-color: #fff !important;}.table-bordered td, .table-bordered th {border: 1px solid #dee2e6 !important;}.table-dark {color: inherit;}.table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark thead th {border-color: #dee2e6;}.table .thead-dark th {color: inherit;border-color: #dee2e6;}}.core-content {-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;min-height: 100vh;}.core-content .brand {outline: 0;}.core-content .brand img {border: 0;outline: 0;}.core-content .side-menu {position: fixed;overflow-y: auto;top: 0;right: -280px;width: 280px;height: 100%;display: block;border-left: solid 1px #efefef;-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;z-index: 9;}.core-content.open-side-menu .side-menu {right: 0;}.core-content .wrap-search-top {display: none;border-bottom: solid 1px #efefef;}.core-content .wrap-search-top input {border: 0;padding: 15px;display: block;width: 100%;outline: 0;font-size: 14px;color: #6a6a6a;}.core-content .wrap-search-top ::-webkit-input-placeholder {font-size: 14px;font-style: italic;}.core-content .wrap-search-top ::-moz-placeholder {font-size: 14px;font-style: italic;}.core-content .wrap-search-top :-ms-input-placeholder {font-size: 14px;font-style: italic;}.core-content .wrap-search-top :-moz-placeholder {font-size: 14px;font-style: italic;}.core-content .wrap-search-fullscreen {position: fixed;z-index: 9999;top: 0;left: 0;width: 100%;height: 100%;background: #fff;display: none;}.core-content .wrap-search-fullscreen .nav-container {position: relative;padding-top: 50px;padding-left: 15px;padding-right: 15px;}.core-content .wrap-search-fullscreen.open {display: block;}.core-content .wrap-search-fullscreen .close-search {position: absolute;right: 0;top: 15px;padding: 15px;border: 0;background: 0 0;outline: 0;cursor: pointer;color: #6a6a6a;z-index: 3;font-size: 24px;}.core-content .wrap-search-fullscreen input {display: block;width: 100%;border: 0;border-bottom: solid 1px #efefef;padding: 15px;outline: 0;font-size: 24px;}.core-nav {display: block;position: relative;}.core-nav.open-dropdown {z-index: 99999999;}.core-nav .nav-header {display: block;position: absolute;z-index: 2;}.core-nav .nav-header.left {position: relative;display: inline-block;}.core-nav .nav-header.center .brand {display: block;margin: auto;}.core-nav .nav-header .content-header {float: right;}.core-nav .core-nav-toggle {display: none;border: 0;padding: 0;background: 0 0;outline: 0;cursor: pointer;}.core-nav .wrap-core-nav-list {text-align: left;position: relative;z-index: 1;}.core-nav .wrap-core-nav-list.right {text-align: right;}.core-nav .wrap-core-nav-list.center {text-align: center;}.core-nav .wrap-core-nav-list .core-nav-list {display: inline-block;margin: 0 0 -5px;padding: 0;list-style: none;}.core-nav .wrap-core-nav-list .core-nav-list li {display: block;float: left;}.core-nav.nav-core-fixed {position: fixed;top: 0;left: 0;width: 100%;z-index: 999;}.core-nav.nav-core-sticky {position: relative;}.core-nav.nav-core-sticky.on-scroll {position: fixed;top: 0;left: 0;width: 100%;}@media (min-width: 992px) {.core-nav .nav-header.bottom, .core-nav .wrap-core-nav-list.bottom {position: relative;float: none;display: block;width: 100%;}}@media (max-width: 992px) {.core-nav .nav-header {position: relative;width: 100%;display: block;}.core-nav .nav-header.left {float: none;display: block;position: relative;}.core-nav .nav-header::after {content: '';clear: both;display: table;}.core-nav .nav-header .content-header {float: none;}.core-nav .core-nav-toggle {display: inline-block;}.core-nav .wrap-core-nav-list {width: 100%;max-height: 300px;overflow: auto;left: 0;margin-top: -100vh;position: absolute;border-bottom: solid 1px #dfdfdf;border-top: solid 1px #dfdfdf;filter: alpha(opacity=0);-ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';-webkit-opacity: 0;-khtml-opacity: 0;-moz-opacity: 0;-ms-opacity: 0;-o-opacity: 0;opacity: 0;transition: all 0.5s ease-in-out;}.core-nav.open-responsive .wrap-core-nav-list, .core-responsive-slide {filter: alpha(opacity=100);}.core-nav .wrap-core-nav-list .core-nav-list {margin: 0;display: block;}.core-nav .wrap-core-nav-list .core-nav-list li {float: none;display: block;width: 100%;}.core-nav .wrap-core-nav-list .core-nav-list li a {display: block;width: 100%;text-align: left;}.core-nav .nav-header.bottom {border: 0;}.core-nav.open-responsive .wrap-core-nav-list {margin-top: 0;-khtml-opacity: 1;opacity: 1;}.core-responsive-slide {transition: all 0.5s ease-in-out;-khtml-opacity: 1;opacity: 1;}.core-responsive-slide .core-nav .wrap-core-nav-list {top: 0;width: 270px;max-height: 100vh;height: 100vh;margin-top: 0;margin-left: -270px;border: 0;border-right: solid 1px #dfdfdf;position: fixed;-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;}.core-responsive-slide.open {margin-left: 270px;margin-right: -270px;}.core-responsive-slide.open .core-nav .wrap-core-nav-list {top: 0;margin-top: 0;margin-left: 0;filter: alpha(opacity=100);-ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=100)';-webkit-opacity: 1;-khtml-opacity: 1;-moz-opacity: 1;-ms-opacity: 1;-o-opacity: 1;opacity: 1;}}@media (min-width: 920px) {.core-nav.fullscreen .nav-header {width: 100%;text-align: left;padding: 0;position: relative;}.core-nav.fullscreen .nav-header .brand {top: 0;margin: 15px;}.core-nav.fullscreen .core-nav-toggle {position: absolute;right: 15px;top: 15px;font-size: 20px;display: inline-block;}.core-nav.fullscreen .wrap-core-nav-list {display: none;position: fixed;left: 0;top: 0;width: 100%;height: 100%;z-index: 99999;background-color: #fff;}.core-nav.fullscreen .wrap-core-nav-list.center {text-align: center;}.core-nav.fullscreen .wrap-core-nav-list.right {text-align: right;}.core-nav.fullscreen .wrap-core-nav-list .nav-container {height: 100vh;display: table;position: relative;padding: 0 30px;}.core-nav.fullscreen .wrap-core-nav-list .menu {display: table-cell;vertical-align: middle;}.core-nav.fullscreen .wrap-core-nav-list .menu li {float: none;display: block;}.core-nav.fullscreen.open-fullscreen .core-nav-toggle {right: 30px;}.core-nav.fullscreen.open-fullscreen .wrap-core-nav-list {display: block;}}.core-nav .wrap-core-nav-list.dropdown-accordion.right .dropdown > .dropdown-menu .dropdown > .dropdown-menu, .core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu {right: 0;}@media (max-width: 920px) {.core-nav.fullscreen .wrap-core-nav-list .nav-container .core-nav-toggle {display: none;}}@media (min-width: 992px) {.core-content {position: relative;min-height: 100vh;}.core-content.core-sidebar {padding-left: 270px;}.core-content.core-sidebar .dropdown-overlay {left: 270px;}.core-content.core-sidebar ul.attributes {position: fixed;width: 270px;left: 0;bottom: 0;display: flex;justify-content: space-around;z-index: 3;}.nav-sidebar .nav-header, .nav-sidebar .nav-header .brand, .nav-sidebar .wrap-core-nav-list .core-nav-list {display: block;}.nav-sidebar {position: absolute;left: 0;top: 0;height: 100%;width: 270px !important;border-right: solid 1px #dfdfdf;}.nav-sidebar .nav-header {width: 100%;}.nav-sidebar .nav-header .brand img {width: 100%;}.nav-sidebar .wrap-core-nav-list .core-nav-list li {display: block;float: none;}}@media (max-width: 992px) {.nav-sidebar .nav-header .nav-container, .nav-sidebar .wrap-core-nav-list .nav-container {padding-left: 0;padding-right: 0;}.nav-sidebar {border-right: 0;}.nav-sidebar .wrap-core-nav-list {padding: 0;border-bottom: solid 1px #dfdfdf;}.core-content.core-sidebar.core-responsive-slide nav.core-nav.nav-core-fixed.nav-sidebar {-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;}.core-content.core-sidebar.core-responsive-slide.open nav.core-nav.nav-core-fixed.nav-sidebar {margin-left: 270px;margin-right: -270px;}.core-content.core-sidebar.core-responsive-slide.open .dropdown-overlay.open-dropdown {left: 270px;}}@media (min-width: 920px) {.core-nav.brand-center .full-container, .core-nav.brand-center .nav-container {position: relative;}.core-nav.brand-center .nav-header {position: absolute;width: 100%;top: 0;left: 0;}.core-nav.brand-center .nav-header .brand {margin: auto;}.core-nav.brand-center .wrap-core-nav-list .col-menu {width: 50%;display: inline-block;}.core-nav.brand-center .wrap-core-nav-list .col-menu.right {text-align: right;}.core-nav.brand-center .wrap-core-nav-list.center .col-menu.left {text-align: right;padding-right: 50px;}.core-nav.brand-center .wrap-core-nav-list.center .col-menu.right {text-align: left;padding-left: 50px;}}@media (min-width: 992px) {.core-content {position: relative;min-height: 100vh;}.core-content.core-sidebar-toggle {padding-left: 75px;}.sidebar-toggle {width: 75px;position: absolute;left: 0;top: 0;height: 100%;border-right: solid 1px #dfdfdf;}.sidebar-toggle .nav-header {display: block;width: 100%;height: 100vh;}.sidebar-toggle .nav-header .brand {display: block;}.sidebar-toggle .nav-header .brand img {width: 100%;}.sidebar-toggle .core-nav-toggle {display: block;width: 75px;height: 75px;text-align: center;position: absolute;top: 50%;left: 0;font-size: 18px;color: #6a6a6a;}.sidebar-toggle .wrap-core-nav-list {position: absolute;height: 100%;width: 270px;margin-left: -270px;border-right: solid 1px #dfdfdf;-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;}.sidebar-toggle .wrap-core-nav-list .core-nav-list {display: block;}.sidebar-toggle .wrap-core-nav-list .core-nav-list li {display: block;float: none;}.sidebar-toggle .wrap-core-nav-list .core-nav-list li a {border-bottom: solid 1px #dfdfdf;}.sidebar-toggle .wrap-core-nav-list.full-width {width: calc(100vw - 75px);margin-left: -100vw;}.sidebar-toggle .wrap-core-nav-list.full-width .core-nav-list {display: inline-block;width: 100%;margin: 0;}.sidebar-toggle .wrap-core-nav-list.full-width .core-nav-list li {background: #fafafa;float: left;display: table;height: 100vh;text-align: center;vertical-align: middle;}.sidebar-toggle .wrap-core-nav-list.full-width .core-nav-list li a {border: 0;height: 100vh;display: table-cell;vertical-align: middle;border-right: solid 1px #dfdfdf;}.sidebar-toggle.open-responsive .wrap-core-nav-list {margin-left: 75px;}}.core-nav-section .core-nav {position: fixed;top: 0;left: 0;z-index: 1000;}@media (min-width: 920px) {.core-nav-section .core-nav {height: 100%;background: 0 0;}.core-nav-section .core-nav .nav-header {padding: 50px;}.core-nav-section .core-nav .nav-header .brand {width: 100px;display: block;}.core-nav-section .core-nav .nav-header .brand img {width: 100%;}.core-nav-section .core-nav .core-nav-list {display: block;background: 0 0;position: absolute;padding-left: 50px;width: 150px;}.core-nav-section .core-nav .core-nav-list li {float: none;display: block;}.core-nav-section .core-nav .core-nav-list li a {background: 0 0;color: #6a6a6a;text-decoration: none;margin: 15px 0;display: block;font-size: 14px;}.core-nav-section .core-nav .core-nav-list li.active a {font-size: 18px;color: #e74c3c;}}@media (max-width: 920px) {.core-nav-section .core-nav {width: 100%;}}@media (min-width: 992px) {.core-content {position: relative;min-height: 100vh;}.core-content.core-side-icon {padding-left: 75px;}.core-content .nav-side-icon {-webkit-transition: all 0.5s ease-in-out;-khtml-transition: all 0.5s ease-in-out;-moz-transition: all 0.5s ease-in-out;-ms-transition: all 0.5s ease-in-out;-o-transition: all 0.5s ease-in-out;transition: all 0.5s ease-in-out;width: 75px;position: absolute;left: 0;top: 0;height: 100%;border-right: solid 1px #dfdfdf;}.core-content .nav-side-icon .nav-header {display: block;width: 100%;}.core-content .nav-side-icon .nav-header .brand {display: block;}.core-content .nav-side-icon .nav-header .brand img {width: 100%;}.core-content .nav-side-icon .wrap-core-nav-list {display: block;position: relative;margin-top: 100px;}.core-content .nav-side-icon .wrap-core-nav-list ul.core-nav-list {display: block;}.core-content .nav-side-icon .wrap-core-nav-list ul.core-nav-list > li {float: none;display: block;width: 100%;position: relative;}.core-content .nav-side-icon .wrap-core-nav-list ul.core-nav-list > li > a {display: block;width: 100%;text-align: center;padding: 10px 5px;}.core-content .nav-side-icon .wrap-core-nav-list ul.core-nav-list > li.dropdown > ul.dropdown-menu {top: 0;left: 100%;}.core-content .nav-side-icon .wrap-core-nav-list ul.core-nav-list .text-icon {display: none;}.core-content.open-side-icon {padding-left: 250px;}.core-content.open-side-icon .nav-side-icon {width: 250px;}.core-content.open-side-icon .wrap-core-nav-list ul.core-nav-list > li > a {position: relative;text-align: left;}.core-content.open-side-icon .wrap-core-nav-list ul.core-nav-list .icon {position: absolute;left: 20px;top: 12px;}.core-content.open-side-icon .wrap-core-nav-list ul.core-nav-list .text-icon {display: inline-block;margin-left: 40px;}}@media (max-width: 992px) {.nav-side-icon .wrap-core-nav-list li {position: relative;}.nav-side-icon .wrap-core-nav-list li .icon {position: absolute;left: 10px;top: 12px;}.nav-side-icon .wrap-core-nav-list li .text-icon {display: inline-block;margin-left: 25px;}}.dropdown-overlay {filter: alpha(opacity=0);-ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';}.dropdown-overlay.open-dropdown {display: block;z-index: 9999999;}.core-nav .dropdown {position: relative;}.core-nav .dropdown .dropdown-menu {display: none;}.core-nav .dropdown > .dropdown-menu {width: 230px;background: #fff;border: 1px solid #eee;position: absolute;top: 100%;padding: 0;}.core-nav .dropdown > .dropdown-menu > li {float: none;display: block;}.core-nav .dropdown > .dropdown-menu > li > a {border-bottom: solid 1px #eee;padding: 10px 15px;text-align: left;}.core-nav .dropdown.open > .dropdown-menu {display: block;}.core-nav .wrap-core-nav-list.center .dropdown > .dropdown-menu, .core-nav .wrap-core-nav-list.left .dropdown > .dropdown-menu {left: 0;}.core-nav .wrap-core-nav-list.center .dropdown > .dropdown-menu .dropdown > .dropdown-menu, .core-nav .wrap-core-nav-list.left .dropdown > .dropdown-menu .dropdown > .dropdown-menu {top: 0;left: 100%;}.core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list li.dropdown > .dropdown-menu {position: relative;width: 100%;border: 0;padding-left: 15px;}.core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown > .dropdown-menu {position: absolute !important;width: 230px;border: 1px solid #efefef;padding-left: 0;}.core-nav .wrap-core-nav-list.dropdown-accordion.center .dropdown > .dropdown-menu .dropdown > .dropdown-menu, .core-nav .wrap-core-nav-list.dropdown-accordion.left .dropdown > .dropdown-menu .dropdown > .dropdown-menu {left: 0;}.core-sidebar .core-nav .wrap-core-nav-list.center > .core-nav-list > li.dropdown > ul.dropdown-menu, .core-sidebar .core-nav .wrap-core-nav-list.left > .core-nav-list > li.dropdown > ul.dropdown-menu, .core-sidebar .core-nav .wrap-core-nav-list.right > .core-nav-list > li.dropdown > ul.dropdown-menu, .core-sidebar-toggle .core-nav .wrap-core-nav-list.center > .core-nav-list > li.dropdown > ul.dropdown-menu, .core-sidebar-toggle .core-nav .wrap-core-nav-list.left > .core-nav-list > li.dropdown > ul.dropdown-menu, .core-sidebar-toggle .core-nav .wrap-core-nav-list.right > .core-nav-list > li.dropdown > ul.dropdown-menu {left: 100%;top: 0;}.core-sidebar .core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown > .dropdown-menu, .core-sidebar-toggle .core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown > .dropdown-menu {left: 0 !important;position: relative !important;width: 100%;border: 1px solid #efefef;padding-left: 0;}@media (max-width: 920px) {.core-nav .dropdown .dropdown-menu {position: relative;width: 100%;border: 0;padding-left: 15px;top: 0;left: 0 !important;right: 0 !important;}.core-nav ul.attributes .dropdown .dropdown-menu {border: 1px solid #efefef;padding: 0;position: absolute;display: none;top: 100%;left: 0;right: 0;width: 230px;}.core-nav ul.attributes .dropdown.open .dropdown-menu {display: block;}.core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown > a {display: block;position: relative;}.core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown .dropdown-menu {position: relative !important;width: 100% !important;}.core-nav .wrap-core-nav-list.dropdown-accordion .core-nav-list > li.dropdown.open > .dropdown-menu {display: block;position: relative;border: 0 !important;padding-left: 15px;}}.core-nav .megamenu > .megamenu-content {position: absolute;top: 100%;width: 100%;background: #fff;text-align: left;border: 1px solid #efefef;display: none;}.core-nav .megamenu.open > .megamenu-content {display: block;}.core-nav .wrap-core-nav-list.center .megamenu > .megamenu-content, .core-nav .wrap-core-nav-list.left .megamenu > .megamenu-content {left: 0;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content {right: 0;}@media (max-width: 920px) {.core-nav .megamenu > .megamenu-content {position: relative;width: 100% !important;border: 0;border-bottom: solid 1px #efefef;}.core-nav ul.attributes .megamenu .megamenu-content {border: 1px solid #efefef;padding: 0;position: absolute;display: none;width: 300px !important;top: 100%;left: 0;right: 0;}.core-nav ul.attributes .megamenu.open .megamenu-content {display: block;}}ul.attributes {padding: 0;margin: 0 0 -7px;list-style: none;display: inline-block;}ul.attributes li {float: left;display: block;}ul.attributes li a {display: block;padding: 20px 10px;color: #6a6a6a;}ul.attributes li a:hover {color: #e74c3c;}.nav-header ul.attributes {position: relative;z-index: 9;margin-right: 10px;}.nav-header ul.attributes li a {padding: 20px 8px;}@media (min-width: 920px) {.nav-header ul.attributes {display: none;}.wrap-core-nav-listul.attributes {display: inline-block;}}@media (max-width: 920px) {.nav-header ul.attributes {display: inline-block;}.wrap-core-nav-list ul.attributes {display: none;}}.core-nav .nav-row::after {content: '';clear: both;display: table;}.core-nav .nav-row [class*='col-'] {float: left;padding: 15px;}.core-nav .nav-container {margin-right: auto;margin-left: auto;}@media (min-width: 768px) {.core-nav .nav-row .col-sm-1 {width: 8.33%;}.core-nav .nav-row .col-sm-2 {width: 16.66%;}.core-nav .nav-row .col-sm-3 {width: 25%;}.core-nav .nav-row .col-sm-4 {width: 33.33%;}.core-nav .nav-row .col-sm-5 {width: 41.66%;}.core-nav .nav-row .col-sm-6 {width: 50%;}.core-nav .nav-row .col-sm-7 {width: 58.33%;}.core-nav .nav-row .col-sm-8 {width: 66.66%;}.core-nav .nav-row .col-sm-9 {width: 75%;}.core-nav .nav-row .col-sm-10 {width: 83.33%;}.core-nav .nav-row .col-sm-11 {width: 91.66%;}.core-nav .nav-row .col-sm-12 {width: 100%;}.core-nav .nav-container {width: 750px;}}@media (min-width: 992px) {.core-nav .nav-row .col-md-1 {width: 8.33%;}.core-nav .nav-row .col-md-2 {width: 16.66%;}.core-nav .nav-row .col-md-3 {width: 25%;}.core-nav .nav-row .col-md-4 {width: 33.33%;}.core-nav .nav-row .col-md-5 {width: 41.66%;}.core-nav .nav-row .col-md-6 {width: 50%;}.core-nav .nav-row .col-md-7 {width: 58.33%;}.core-nav .nav-row .col-md-8 {width: 66.66%;}.core-nav .nav-row .col-md-9 {width: 75%;}.core-nav .nav-row .col-md-10 {width: 83.33%;}.core-nav .nav-row .col-md-11 {width: 91.66%;}.core-nav .nav-row .col-md-12 {width: 100%;}.core-nav .nav-container {width: 970px;}.core-nav .full-container {padding: 0 15px;}}@media (min-width: 1200px) {.core-nav .nav-container {width: 1170px;padding-right: 15px;padding-left: 15px;}}@media (max-width: 767px) {.core-nav .nav-row .col-xs-1 {width: 8.33%;}.core-nav .nav-row .col-xs-2 {width: 16.66%;}.core-nav .nav-row .col-xs-3 {width: 25%;}.core-nav .nav-row .col-xs-4 {width: 33.33%;}.core-nav .nav-row .col-xs-5 {width: 41.66%;}.core-nav .nav-row .col-xs-6 {width: 50%;}.core-nav .nav-row .col-xs-7 {width: 58.33%;}.core-nav .nav-row .col-xs-8 {width: 66.66%;}.core-nav .nav-row .col-xs-9 {width: 75%;}.core-nav .nav-row .col-xs-10 {width: 83.33%;}.core-nav .nav-row .col-xs-11 {width: 91.66%;}.core-nav .nav-row .col-xs-12 {width: 100%;}}@font-face {font-family: 'Montserrat Light';src: url(../fonts/montserrat/montserrat-light.ttf) format('truetype');}@font-face {font-family: Montserrat;src: url(../fonts/montserrat/montserrat-regular.ttf) format('truetype');}@font-face {font-family: 'Montserrat Medium';src: url(../fonts/montserrat/montserrat-medium.ttf) format('truetype');}@font-face {font-family: 'Montserrat Bold';src: url(../fonts/montserrat/montserrat-bold.ttf) format('truetype');}@font-face {font-family: 'Montserrat SemiBold';src: url(../fonts/montserrat/montserrat-semi-bold.ttf) format('truetype');}@font-face {font-family: FontAwesome;src: url(../fonts/fontawesome/fontawesome-webfont3e6e.eot?v=4.7.0);src: url(../fonts/fontawesome/fontawesome-webfontd41d.eot?#iefix&v=4.7.0) format('embedded-opentype'), url(../fonts/fontawesome/fontawesome-webfont3e6e.woff2?v=4.7.0) format('woff2'), url(../fonts/fontawesome/fontawesome-webfont3e6e.woff?v=4.7.0) format('woff'), url(../fonts/fontawesome/fontawesome-webfont3e6e.ttf?v=4.7.0) format('truetype'), url(../fonts/fontawesome/fontawesome-webfont3e6e.svg?v=4.7.0#fontawesomeregular) format('svg');font-weight: 400;font-style: normal;}.fa {font: normal normal normal 14px/1 FontAwesome;font-size: inherit;text-rendering: auto;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;}.fa-lg {font-size: 1.33333333em;line-height: 0.75em;vertical-align: -15%;}.fa-1x {font-size: 1.6em;}.fa-2x {font-size: 2em;}.fa-3x {font-size: 3em;}.fa-4x {font-size: 4em;}.fa-5x {font-size: 5em;}.fa-fw {width: 1.28571429em;}.fa-ul {padding-left: 0;margin-left: 2.14285714em;list-style-type: none;}.fa.fa-pull-right, .fa.pull-right {margin-left: 0.3em;}.fa-ul > li {position: relative;}.fa-li {position: absolute;left: -2.14285714em;width: 2.14285714em;top: 0.14285714em;}.fa-li.fa-lg {left: -1.85714286em;}.fa-border {padding: 0.2em 0.25em 0.15em;border: 0.08em solid #eee;border-radius: 0.1em;}.fa-pull-left {float: left;}.fa-pull-right, .pull-right {float: right;}.pull-left, .select2-container .select2-search--inline {float: left;}.fa-spin {-webkit-animation: fa-spin 2s infinite linear;animation: fa-spin 2s infinite linear;}.fa-pulse {-webkit-animation: fa-spin 1s infinite steps(8);animation: fa-spin 1s infinite steps(8);}@-webkit-keyframes fa-spin {0% {-webkit-transform: rotate(0);transform: rotate(0);}100% {-webkit-transform: rotate(359deg);transform: rotate(359deg);}}@keyframes fa-spin {0% {-webkit-transform: rotate(0);transform: rotate(0);}100% {-webkit-transform: rotate(359deg);transform: rotate(359deg);}}.fa-rotate-90 {-webkit-transform: rotate(90deg);transform: rotate(90deg);}.fa-rotate-180 {-webkit-transform: rotate(180deg);transform: rotate(180deg);}.fa-rotate-270 {-webkit-transform: rotate(270deg);transform: rotate(270deg);}.fa-flip-horizontal {-webkit-transform: scale(-1, 1);transform: scale(-1, 1);}.fa-flip-vertical {-webkit-transform: scale(1, -1);transform: scale(1, -1);}:root .fa-flip-horizontal, :root .fa-flip-vertical, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-rotate-90 {-webkit-filter: none;filter: none;}.fa-stack {position: relative;width: 2em;height: 2em;line-height: 2em;vertical-align: middle;}.fa-stack-1x, .fa-stack-2x {position: absolute;left: 0;width: 100%;text-align: center;}.fa-stack-1x {line-height: inherit;}.fa-stack-2x {font-size: 2em;}.sr-only {position: absolute;width: 1px;height: 1px;padding: 0;margin: -1px;overflow: hidden;clip: rect(0, 0, 0, 0);border: 0;}.sr-only-focusable:active, .sr-only-focusable:focus {position: static;width: auto;height: auto;margin: 0;overflow: visible;clip: auto;}.icon-1d {background: 0 0;width: 60px;height: 60px;background-size: 60px auto;margin-bottom: 10px;}.icon {margin-bottom: 10px;background-size: 30px auto;width: 20px;height: 20px;}@media (max-width: 991px) {.icon-1d {margin-bottom: 5px;background-size: 30px auto;width: 30px;height: 30px;}}.icon-lg {width: 120px;height: 120px;background-size: 120px auto;}@media (max-width: 991px) {.icon-lg {margin-bottom: 5px;background-size: 60px auto;width: 60px;height: 60px;}}.icon-chart {background-image: url(../../assets/Standard_2.png);}.icon-compare {background-image: url(../../assets/inverter.png);}.icon-map {background-image: url(../../assets/AIe.png);}.icon-3d {background-image: url(../../assets/cloud.png);}.icon-hotspot {background-image: url(../../assets/Deep.png);}.icon-users {background-image: url(../../assets/3d.svg);}.icon-paper-plane {background-image: url(../../assets/3d.svg);}.icon-question {background-image: url(../../assets/mapgps.png);}.pricing__content-price, .pricing__grid-header--icon, .section__heading, h1 {font-family: 'Montserrat Bold', sans-serif;font-size: 36px;line-height: 48px;color: #2c2d2d;margin-top: 0;margin-bottom: 20px;}@media (min-width: 768px) and (max-width: 991px) {.pricing__content-price, .pricing__grid-header--icon, .section__heading, h1 {font-size: 28px;line-height: 42px;margin-bottom: 10px;}}@media (max-width: 767px) {.pricing__content-price, .pricing__grid-header--icon, .section__heading, h1 {font-size: 24px;line-height: 36px;margin-bottom: 10px;}}.blockquote blockquote::after, .blockquote blockquote::before, .headline::before, .tabs__navigation-item--icon, .tabs__navigation-item--icon1, .tabs__navigation-item--icon2, .tabs__navigation-item--icon3, .testimoni blockquote::after, .testimoni blockquote::before, h2 {font-family: 'Montserrat Bold', sans-serif;font-size: 1.75rem;line-height: 2.62rem;margin-top: 0;margin-bottom: 1.25rem;}@media (min-width: 768px) and (max-width: 991px) {.blockquote blockquote::after, .blockquote blockquote::before, .headline::before, .testimoni blockquote::after, .testimoni blockquote::before, h2 {font-size: 24px;line-height: 36px;margin-bottom: 10px;}}@media (max-width: 767px) {.blockquote blockquote::after, .blockquote blockquote::before, .headline::before, .tabs__navigation-item--icon, .testimoni blockquote::after, .testimoni blockquote::before, h2 {font-size: 18px;line-height: 28px;margin-bottom: 10px;}}.blockquote blockquote, .pricing__popular .pricing__content p, .team__card--join-link, .testimoni blockquote, h3 {font-family: 'Montserrat Bold', sans-serif;font-size: 24px;line-height: 36px;margin-top: 0;margin-bottom: 20px;}@media (min-width: 768px) and (max-width: 991px) {.blockquote blockquote, .pricing__popular .pricing__content p, .team__card--join-link, .testimoni blockquote, h3 {font-size: 18px;line-height: 28px;margin-bottom: 10px;}}@media (max-width: 767px) {.blockquote blockquote, .pricing__popular .pricing__content p, .team__card--join-link, .testimoni blockquote, h3 {font-size: 16px;line-height: 24px;margin-bottom: 10px;}}.article__card-comment, .footer__widget-contact .fa, .form__subscribe-button, .masthead__content-subtitle, .pricing__content p, .pricing__grid-price, .pricing__popular .pricing__content-currency, .pricing__popular .pricing__content-package, .testimoni__author, h4 {font-family: 'Montserrat Bold', sans-serif;font-size: 18px;line-height: 28px;margin-top: 0;margin-bottom: 20px;}@media (min-width: 768px) and (max-width: 991px) {.article__card-comment, .footer__widget-contact .fa, .form__subscribe-button, .masthead__content-subtitle, .pricing__content p, .pricing__grid-price, .pricing__popular .pricing__content-currency, .pricing__popular .pricing__content-package, .testimoni__author, h4 {font-size: 16px;line-height: 24px;margin-bottom: 10px;}}@media (max-width: 767px) {.article__card-comment, .footer__widget-contact .fa, .form__subscribe-button, .masthead__content-subtitle, .pricing__content p, .pricing__grid-price, .pricing__popular .pricing__content-currency, .pricing__popular .pricing__content-package, .testimoni__author, h4 {font-size: 14px;line-height: 22px;margin-bottom: 10px;}}.package__content ul li, .pricing__content-currency, .pricing__content-package, .pricing__grid-content, .section__cta-subscribe-input, .tabs__navigation-item--link, h5 {font-family: 'Montserrat Bold', sans-serif;font-size: 16px;line-height: 24px;margin-top: 0;margin-bottom: 20px;}@media (min-width: 768px) and (max-width: 991px) {.package__content ul li, .pricing__content-currency, .pricing__content-package, .pricing__grid-content, .section__cta-subscribe-input, .tabs__navigation-item--link, h5 {font-size: 14px;line-height: 22px;margin-bottom: 10px;}}@media (max-width: 767px) {.package__content ul li, .pricing__content-currency, .pricing__content-package, .pricing__grid-content, .section__cta-subscribe-input, .tabs__navigation-item--link, h5 {font-size: 14px;line-height: 24px;margin-bottom: 10px;}}h6 {font-family: 'Montserrat Bold', sans-serif;font-size: 14px;line-height: normal;margin-top: 0;margin-bottom: 20px;}.body-copy, .fine-print, .form-control, .headline, .section__cta .column__boxed, body, html {font-family: Montserrat, sans-serif;}@media (min-width: 768px) and (max-width: 991px) {h6 {font-size: 14px;line-height: 24px;margin-bottom: 10px;}}@media (max-width: 767px) {h6 {font-size: 12px;line-height: 22px;margin-bottom: 10px;}}.body-copy, .section__cta .column__boxed, body, html {font-size: 14px;line-height: 24px;color: #2c2d2d;margin-top: 0;margin-bottom: 20px;}@media (max-width: 991px) {.body-copy, .section__cta .column__boxed, body, html {font-size: 12px;line-height: 22px;margin-bottom: 10px;}.fine-print, .form-control {font-size: 12px;line-height: 22px;}}.fine-print, .form-control {font-size: 12px;line-height: 22px;color: #2c2d2d;margin: 0;}.headline {position: relative;font-size: 24px;line-height: 36px;padding-left: 40px;padding-top: 5px;margin-bottom: 20px;}@media (min-width: 768px) and (max-width: 991px) {.headline {font-size: 18px;line-height: 28px;margin-bottom: 10px;}.container, .core-nav .nav-container {width: 720px;padding-left: 15px;padding-right: 15px;}}@media (min-width: 768px) and (max-width: 991px) {.container {width: 750px;}}@media (min-width: 768px) and (max-width: 991px) {.container {width: 750px;}}@media (max-width: 767px) {.headline {font-size: 16px;line-height: 24px;margin-bottom: 10px;}}.headline::before {position: absolute;content: '\f10d';font-family: FontAwesome;color: #1e5ee5;top: -5px;left: 0;}.mfp-arrow:after, .mfp-arrow:before, .mfp-container:before, .mfp-figure:after {content: '';}a {transition: all 0.3s ease-in-out !important;color: #1e5ee5;}a:active, a:focus, a:hover {color: #0f44b2;}p {margin-bottom: 20px;}strong {font-family: 'Montserrat Bold', sans-serif !important;}.text-normal {font-family: Montserrat, sans-serif;}body, html {color: #2c2d2d;margin-bottom: 0;overflow-x: hidden !important;}@media (max-width: 767px) {.container, .core-nav .nav-container {width: 100%;padding-left: 15px;padding-right: 15px;}}.container__fullwidth, .core-nav .nav-container__fullwidth {width: 100% !important;}@media (min-width: 1400px) {.container, .core-nav .nav-container {max-width: 1200px;}.container__fullwidth, .core-nav .nav-container__fullwidth {padding-left: 80px;padding-right: 80px;}}@media (min-width: 1200px) and (max-width: 1399px) {.container, .core-nav .nav-container {max-width: 1140px;}.container__fullwidth, .core-nav .nav-container__fullwidth {padding-left: 40px;padding-right: 40px;}}@media (max-width: 991px) {p {margin-bottom: 10px;}.container__fullwidth, .core-nav .nav-container__fullwidth {padding-left: 15px;padding-right: 15px;}}@media (min-width: 1400px) {.core-nav .nav-container {padding-left: 0;padding-right: 0;}}.divider {position: relative;display: block;height: 1px;background: #d7deee;margin: 60px 0;}@media (max-width: 767px) {.divider {margin: 30px 0;}}@-webkit-keyframes flyIn {0%, 100% {-webkit-transform: translateY(0);transform: translateY(0);}50% {-webkit-transform: translateY(1rem);transform: translateY(1rem);}}@keyframes flyIn {0%, 100% {-webkit-transform: translateY(0);transform: translateY(0);}50% {-webkit-transform: translateY(1rem);transform: translateY(1rem);}}.flyIn {-webkit-animation-name: flyIn;animation-name: flyIn;-webkit-animation-duration: 4.5s;animation-duration: 4.5s;-webkit-animation-iteration-count: infinite;animation-iteration-count: infinite;-webkit-animation-delay: 0.1s;animation-delay: 0.1s;}.demo-list {margin: 0;padding: 0;}.demo-list li {list-style: none;margin: 0 0 2px;padding: 0;}.demo-list li a {display: block;border: 1px solid #b7c3de;padding: 10px 25px;color: #000;font-weight: 700;}@-webkit-keyframes modal-video {from {opacity: 0;}to {opacity: 1;}}@keyframes modal-video {from {opacity: 0;}to {opacity: 1;}}@-webkit-keyframes modal-video-inner {from {-webkit-transform: translate(0, 100px);transform: translate(0, 100px);}to {-webkit-transform: translate(0, 0);transform: translate(0, 0);}}@keyframes modal-video-inner {from {-webkit-transform: translate(0, 100px);transform: translate(0, 100px);}to {-webkit-transform: translate(0, 0);transform: translate(0, 0);}}.modal-video {position: fixed;top: 0;left: 0;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.5);z-index: 1000000;cursor: pointer;opacity: 1;-webkit-animation-timing-function: ease-out;animation-timing-function: ease-out;-webkit-animation-duration: 0.3s;animation-duration: 0.3s;-webkit-animation-name: modal-video;animation-name: modal-video;-webkit-transition: opacity 0.3s ease-out;transition: opacity 0.3s ease-out;}.modal-video-close {opacity: 0;}.modal-video-close .modal-video-movie-wrap {-webkit-transform: translate(0, 100px);transform: translate(0, 100px);}.modal-video-body {max-width: 940px;width: 100%;height: 100%;margin: 0 auto;display: table;}.modal-video-inner {display: table-cell;vertical-align: middle;width: 100%;height: 100%;}.modal-video-movie-wrap {width: 100%;height: 0;position: relative;padding-bottom: 56.25%;background-color: #333;-webkit-animation-timing-function: ease-out;animation-timing-function: ease-out;-webkit-animation-duration: 0.3s;animation-duration: 0.3s;-webkit-animation-name: modal-video-inner;animation-name: modal-video-inner;-webkit-transform: translate(0, 0);transform: translate(0, 0);-webkit-transition: -webkit-transform 0.3s ease-out;transition: -webkit-transform 0.3s ease-out;transition: transform 0.3s ease-out;transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;}.modal-video-movie-wrap iframe {position: absolute;top: 0;left: 0;width: 100%;height: 100%;}.modal-video-close-btn {position: absolute;z-index: 2;top: -35px;right: -35px;display: inline-block;width: 35px;height: 35px;overflow: hidden;border: none;background: 0 0;}.modal-video-close-btn:before {-webkit-transform: rotate(45deg);transform: rotate(45deg);}.modal-video-close-btn:after {-webkit-transform: rotate(-45deg);transform: rotate(-45deg);}.modal-video-close-btn:after, .modal-video-close-btn:before {content: '';position: absolute;height: 2px;width: 100%;top: 50%;left: 0;background: #fff;border-radius: 5px;margin-top: -6px;}.select2-container {-webkit-box-sizing: border-box;box-sizing: border-box;display: inline-block;margin: 0;position: relative;vertical-align: middle;}.select2-container .select2-selection--single {-webkit-box-sizing: border-box;box-sizing: border-box;cursor: pointer;display: block;height: 28px;-moz-user-select: none;-ms-user-select: none;user-select: none;-webkit-user-select: none;}.select2-container .select2-selection--single .select2-selection__rendered {display: block;padding-left: 8px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}.select2-container .select2-selection--single .select2-selection__clear {position: relative;}.select2-container[dir='rtl'] .select2-selection--single .select2-selection__rendered {padding-right: 8px;padding-left: 20px;}.select2-container .select2-selection--multiple {-webkit-box-sizing: border-box;box-sizing: border-box;cursor: pointer;display: block;min-height: 32px;-moz-user-select: none;-ms-user-select: none;user-select: none;-webkit-user-select: none;}.select2-container .select2-selection--multiple .select2-selection__rendered {display: inline-block;overflow: hidden;padding-left: 8px;text-overflow: ellipsis;white-space: nowrap;}.select2-container .select2-search--inline .select2-search__field {-webkit-box-sizing: border-box;box-sizing: border-box;border: none;font-size: 100%;margin-top: 5px;padding: 0;}.select2-dropdown {background-color: #fff;border-radius: 4px;-webkit-box-sizing: border-box;box-sizing: border-box;display: block;position: absolute;left: -100000px;width: 100%;z-index: 1051;}.select2-results {display: block;}.select2-results__options {list-style: none;margin: 0;padding: 0;}.select2-results__option {padding: 6px;-moz-user-select: none;-ms-user-select: none;user-select: none;-webkit-user-select: none;}.select2-results__option[aria-selected] {cursor: pointer;}.select2-container--open .select2-dropdown {left: 0;}.select2-container--open .select2-dropdown--above {border-bottom: none;border-bottom-left-radius: 0;border-bottom-right-radius: 0;}.select2-container--open .select2-dropdown--below {border-top: none;border-top-left-radius: 0;border-top-right-radius: 0;}.select2-search--dropdown {display: block;padding: 4px;}.select2-search--dropdown .select2-search__field {padding: 4px;width: 100%;-webkit-box-sizing: border-box;box-sizing: border-box;}.select2-search--dropdown.select2-search--hide {display: none;}.select2-close-mask {border: 0;margin: 0;padding: 0;display: block;position: fixed;left: 0;top: 0;min-height: 100%;min-width: 100%;height: auto;width: auto;opacity: 0;z-index: 99;background-color: #fff;filter: alpha(opacity=0);}.select2-hidden-accessible {border: 0 !important;clip: rect(0 0 0 0) !important;-webkit-clip-path: inset(50%) !important;clip-path: inset(50%) !important;height: 1px !important;overflow: hidden !important;padding: 0 !important;position: absolute !important;width: 1px !important;white-space: nowrap !important;}.select2-container--classic .select2-results > .select2-results__options, .select2-container--default .select2-results > .select2-results__options {max-height: 200px;overflow-y: auto;}.select2-container--default .select2-selection--single {background-color: #fff;border-radius: 4px;}.select2-container--default .select2-selection--single .select2-selection__rendered {color: #444;}.select2-container--default .select2-selection--single .select2-selection__clear {cursor: pointer;float: right;font-weight: 700;}.select2-container--default .select2-selection--single .select2-selection__arrow {height: 26px;position: absolute;width: 20px;}.select2-container--default .select2-selection--single .select2-selection__arrow b {border-color: #888 transparent transparent;border-style: solid;border-width: 5px 4px 0;height: 0;left: 50%;margin-left: -4px;margin-top: -2px;position: absolute;top: 50%;width: 0;}.select2-container--default[dir='rtl'] .select2-selection--single .select2-selection__clear {float: left;}.select2-container--default[dir='rtl'] .select2-selection--single .select2-selection__arrow {left: 1px;right: auto;}.select2-container--default.select2-container--disabled .select2-selection--single {background-color: #eee;cursor: default;}.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {display: none;}.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {border-color: transparent transparent #888;border-width: 0 4px 5px;}.select2-container--default .select2-selection--multiple {background-color: #fff;border: 1px solid #aaa;border-radius: 4px;cursor: text;}.select2-container--default .select2-selection--multiple .select2-selection__rendered {-webkit-box-sizing: border-box;box-sizing: border-box;list-style: none;margin: 0;padding: 0 5px;width: 100%;}.select2-container--default .select2-selection--multiple .select2-selection__rendered li {list-style: none;}.select2-container--default .select2-selection--multiple .select2-selection__clear {cursor: pointer;float: right;font-weight: 700;margin-top: 5px;margin-right: 10px;padding: 1px;}.select2-container--default .select2-selection--multiple .select2-selection__choice {background-color: #e4e4e4;border: 1px solid #aaa;border-radius: 4px;cursor: default;float: left;margin-right: 5px;margin-top: 5px;padding: 0 5px;}.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {color: #999;cursor: pointer;display: inline-block;font-weight: 700;margin-right: 2px;}.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {color: #333;}.select2-container--default[dir='rtl'] .select2-selection--multiple .select2-search--inline, .select2-container--default[dir='rtl'] .select2-selection--multiple .select2-selection__choice {float: right;}.select2-container--default[dir='rtl'] .select2-selection--multiple .select2-selection__choice {margin-left: 5px;margin-right: auto;}.select2-container--default[dir='rtl'] .select2-selection--multiple .select2-selection__choice__remove {margin-left: 2px;margin-right: auto;}.select2-container--default.select2-container--focus .select2-selection--multiple {border: 1px solid #000;outline: 0;}.select2-container--default.select2-container--disabled .select2-selection--multiple {background-color: #eee;cursor: default;}.select2-container--default.select2-container--disabled .select2-selection__choice__remove {display: none;}.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple, .select2-container--default.select2-container--open.select2-container--above .select2-selection--single {border-top-left-radius: 0;border-top-right-radius: 0;}.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple, .select2-container--default.select2-container--open.select2-container--below .select2-selection--single {border-bottom-left-radius: 0;border-bottom-right-radius: 0;}.select2-container--default .select2-search--dropdown .select2-search__field {border: 1px solid #aaa;}.select2-container--default .select2-search--inline .select2-search__field {background: 0 0;border: none;outline: 0;-webkit-box-shadow: none;box-shadow: none;}.select2-container--default .select2-results__option[role='group'] {padding: 0;}.select2-container--default .select2-results__option[aria-disabled='true'] {color: #999;}.select2-container--default .select2-results__option .select2-results__option {padding-left: 1em;}.select2-container--default .select2-results__option .select2-results__option .select2-results__group {padding-left: 0;}.select2-container--default .select2-results__option .select2-results__option .select2-results__option {margin-left: -1em;padding-left: 2em;}.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {margin-left: -2em;padding-left: 3em;}.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {margin-left: -3em;padding-left: 4em;}.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {margin-left: -4em;padding-left: 5em;}.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {margin-left: -5em;padding-left: 6em;}.select2-container--default .select2-results__option--highlighted[aria-selected] {color: #fff;}.select2-container--default .select2-results__group {cursor: default;display: block;padding: 6px;}.select2-container--classic .select2-selection--single {background-color: #f7f7f7;border: 1px solid #aaa;border-radius: 4px;outline: 0;background-image: -webkit-gradient( linear, left top, left bottom, color-stop(50%, #fff), to(#eee) );background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);background-repeat: repeat-x;filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);}.select2-container--classic .select2-selection--single:focus {border: 1px solid #5897fb;}.select2-container--classic .select2-selection--single .select2-selection__rendered {color: #444;line-height: 28px;}.select2-container--classic .select2-selection--single .select2-selection__clear {cursor: pointer;float: right;font-weight: 700;margin-right: 10px;}.select2-container--classic .select2-selection--single .select2-selection__placeholder {color: #999;}.select2-container--classic .select2-selection--single .select2-selection__arrow {background-color: #ddd;border: none;border-left: 1px solid #aaa;border-top-right-radius: 4px;border-bottom-right-radius: 4px;height: 26px;position: absolute;top: 1px;right: 1px;width: 20px;background-image: -webkit-gradient( linear, left top, left bottom, color-stop(50%, #eee), to(#ccc) );background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);background-repeat: repeat-x;filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0);}.select2-container--classic .select2-selection--single .select2-selection__arrow b {border-color: #888 transparent transparent;border-style: solid;border-width: 5px 4px 0;height: 0;left: 50%;margin-left: -4px;margin-top: -2px;position: absolute;top: 50%;width: 0;}.select2-container--classic[dir='rtl'] .select2-selection--single .select2-selection__clear {float: left;}.select2-container--classic[dir='rtl'] .select2-selection--single .select2-selection__arrow {border: none;border-right: 1px solid #aaa;border-radius: 4px 0 0 4px;left: 1px;right: auto;}.mfp-bg, .mfp-container, .mfp-wrap {left: 0;top: 0;height: 100%;width: 100%;}.select2-container--classic.select2-container--open .select2-selection--single {border: 1px solid #5897fb;}.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {background: 0 0;border: none;}.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {border-color: transparent transparent #888;border-width: 0 4px 5px;}.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {border-top: none;border-top-left-radius: 0;border-top-right-radius: 0;background-image: -webkit-gradient( linear, left top, left bottom, from(white), color-stop(50%, #eee) );background-image: linear-gradient(to bottom, #fff 0, #eee 50%);background-repeat: repeat-x;filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0);}.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {border-bottom: none;border-bottom-left-radius: 0;border-bottom-right-radius: 0;background-image: -webkit-gradient( linear, left top, left bottom, color-stop(50%, #eee), to(white) );background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);background-repeat: repeat-x;filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0);}.select2-container--classic .select2-selection--multiple {background-color: #fff;border: 1px solid #aaa;border-radius: 4px;cursor: text;outline: 0;}.select2-container--classic .select2-selection--multiple:focus {border: 1px solid #5897fb;}.select2-container--classic .select2-selection--multiple .select2-selection__rendered {list-style: none;margin: 0;padding: 0 5px;}.select2-container--classic .select2-selection--multiple .select2-selection__clear {display: none;}.select2-container--classic .select2-selection--multiple .select2-selection__choice {background-color: #e4e4e4;border: 1px solid #aaa;border-radius: 4px;cursor: default;float: left;margin-right: 5px;margin-top: 5px;padding: 0 5px;}.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {color: #888;cursor: pointer;display: inline-block;font-weight: 700;margin-right: 2px;}.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {color: #555;}.select2-container--classic[dir='rtl'] .select2-selection--multiple .select2-selection__choice {float: right;margin-left: 5px;margin-right: auto;}.select2-container--classic[dir='rtl'] .select2-selection--multiple .select2-selection__choice__remove {margin-left: 2px;margin-right: auto;}.select2-container--classic.select2-container--open .select2-selection--multiple {border: 1px solid #5897fb;}.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {border-top: none;border-top-left-radius: 0;border-top-right-radius: 0;}.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {border-bottom: none;border-bottom-left-radius: 0;border-bottom-right-radius: 0;}.select2-container--classic .select2-search--dropdown .select2-search__field {border: 1px solid #aaa;outline: 0;}.select2-container--classic .select2-search--inline .select2-search__field {outline: 0;-webkit-box-shadow: none;box-shadow: none;}.select2-container--classic .select2-dropdown {background-color: #fff;border: 1px solid transparent;}.select2-container--classic .select2-dropdown--above {border-bottom: none;}.select2-container--classic .select2-dropdown--below {border-top: none;}.select2-container--classic .select2-results__option[role='group'] {padding: 0;}.select2-container--classic .select2-results__option[aria-disabled='true'] {color: grey;}.select2-container--classic .select2-results__option--highlighted[aria-selected] {background-color: #3875d7;color: #fff;}.select2-container--classic .select2-results__group {cursor: default;display: block;padding: 6px;}.select2-container--classic.select2-container--open .select2-dropdown {border-color: #5897fb;}.mfp-bg {z-index: 1042;overflow: hidden;position: fixed;background: #0b0b0b;opacity: 0.8;}.mfp-arrow, .mfp-arrow:focus, .mfp-arrow:hover, .mfp-close, .mfp-close:focus, .mfp-close:hover {opacity: 1;}.mfp-wrap {z-index: 1043;position: fixed;outline: 0 !important;}.mfp-container {position: absolute;padding: 0 8px;-webkit-box-sizing: border-box;box-sizing: border-box;}.mfp-container:before {display: inline-block;height: 100%;vertical-align: middle;}.mfp-align-top .mfp-container:before {display: none;}.mfp-content {position: relative;display: inline-block;vertical-align: middle;margin: 0 auto;text-align: left;z-index: 1045;}.mfp-close, .mfp-preloader {text-align: center;position: absolute;}.mfp-ajax-holder .mfp-content, .mfp-inline-holder .mfp-content {width: 100%;cursor: auto;}.mfp-ajax-cur {cursor: progress;}.mfp-zoom {cursor: pointer;cursor: -webkit-zoom-in;cursor: zoom-in;}.mfp-auto-cursor .mfp-content {cursor: auto;}.mfp-arrow, .mfp-close, .mfp-counter, .mfp-preloader {-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}.mfp-loading.mfp-figure {display: none;}.mfp-hide {display: none !important;}.mfp-preloader {color: #ccc;top: 50%;width: auto;margin-top: -0.8em;left: 8px;right: 8px;z-index: 1044;}.mfp-preloader a {color: #ccc;}.mfp-close, .mfp-preloader a:hover {color: #fff;}.mfp-s-error .mfp-content, .mfp-s-ready .mfp-preloader {display: none;}button.mfp-arrow, button.mfp-close {overflow: visible;cursor: pointer;background: 0 0;border: 0;display: block;outline: 0;padding: 0;z-index: 1046;-webkit-box-shadow: none;box-shadow: none;-ms-touch-action: manipulation;touch-action: manipulation;}button::-moz-focus-inner {padding: 0;border: 0;}.mfp-close {width: 44px;height: 44px;line-height: 44px;right: 0;top: 0;text-decoration: none;padding: 0 0 18px 10px;font-style: normal;font-size: 28px;font-family: Arial, Baskerville, monospace;}.mfp-close:active {top: 1px;}.mfp-close-btn-in .mfp-close {color: #333;}.mfp-iframe-holder .mfp-close, .mfp-image-holder .mfp-close {color: #fff;right: -6px;text-align: right;padding-right: 6px;width: 100%;}.mfp-counter {position: absolute;top: 0;right: 0;line-height: 18px;white-space: nowrap;}.mfp-figure, img.mfp-img {line-height: 0;}.mfp-arrow {position: absolute;margin: -55px 0 0;top: 50%;padding: 0;}.mfp-arrow:after, .mfp-arrow:before {display: block;width: 0;height: 0;position: absolute;left: 0;top: 0;margin-top: 35px;margin-left: 35px;border: inset transparent;}.mfp-arrow:after {border-top-width: 13px;border-bottom-width: 13px;top: 8px;}.mfp-arrow-left {left: 0;}.mfp-arrow-left:after {border-right: 17px solid #fff;margin-left: 31px;}.mfp-arrow-left:before {margin-left: 25px;border-right: 27px solid #3f3f3f;}.mfp-arrow-right {right: 0;}.mfp-arrow-right:after {border-left: 17px solid #fff;margin-left: 39px;}.mfp-arrow-right:before {border-left: 27px solid #3f3f3f;}.mfp-iframe-holder {padding-top: 40px;padding-bottom: 40px;}.mfp-iframe-holder .mfp-content {line-height: 0;width: 100%;max-width: 900px;}.mfp-image-holder .mfp-content, img.mfp-img {max-width: 100%;}.mfp-iframe-holder .mfp-close {top: -40px;}.mfp-iframe-scaler {width: 100%;height: 0;overflow: hidden;padding-top: 56.25%;}.mfp-iframe-scaler iframe {position: absolute;display: block;top: 0;left: 0;width: 100%;height: 100%;-webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);background: #000;}.mfp-figure:after, img.mfp-img {width: auto;height: auto;display: block;}img.mfp-img {-webkit-box-sizing: border-box;box-sizing: border-box;padding: 40px 0;margin: 0 auto;}.mfp-figure:after {position: absolute;left: 0;top: 40px;bottom: 40px;right: 0;z-index: -1;-webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);background: #444;}.mfp-figure small {color: #bdbdbd;display: block;font-size: 12px;line-height: 14px;}.mfp-figure figure {margin: 0;}.mfp-bottom-bar {position: absolute;top: 100%;left: 0;width: 100%;cursor: auto;}.mfp-title {text-align: left;line-height: 18px;color: #f3f3f3;word-wrap: break-word;padding-right: 36px;}.mfp-gallery .mfp-image-holder .mfp-figure {cursor: pointer;}@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {.mfp-img-mobile .mfp-image-holder {padding-left: 0;padding-right: 0;}.mfp-img-mobile img.mfp-img {padding: 0;}.mfp-img-mobile .mfp-figure:after {top: 0;bottom: 0;}.mfp-img-mobile .mfp-figure small {display: inline;margin-left: 5px;}.mfp-img-mobile .mfp-bottom-bar {background: rgba(0, 0, 0, 0.6);bottom: 0;margin: 0;top: auto;padding: 3px 5px;position: fixed;-webkit-box-sizing: border-box;box-sizing: border-box;}.mfp-img-mobile .mfp-bottom-bar:empty {padding: 0;}.mfp-img-mobile .mfp-counter {right: 5px;top: 3px;}.mfp-img-mobile .mfp-close {top: 0;right: 0;width: 35px;height: 35px;line-height: 35px;background: rgba(0, 0, 0, 0.6);position: fixed;text-align: center;padding: 0;}}@media all and (max-width: 900px) {.mfp-arrow {-webkit-transform: scale(0.75);transform: scale(0.75);}.mfp-arrow-left {-webkit-transform-origin: 0;transform-origin: 0;}.mfp-arrow-right {-webkit-transform-origin: 100%;transform-origin: 100%;}.mfp-container {padding-left: 6px;padding-right: 6px;}}.mfp-zoom-out-cur {cursor: default;}.mfp-bottom-bar {margin-top: -20px;}.mfp-heading {color: #fff;font-size: 18px;margin-bottom: 5px;}.mfp-author {font-size: 12px;}.mfp-author strong {margin-right: 5px;}.mfp-counter {color: #fff;font-size: 14px;}.mfp-zoom-out-cur .mfp-image-holder .mfp-close {cursor: pointer;color: #fff;}.mfp-arrow {width: 30px;height: 30px;margin-top: -15px;}.mfp-arrow:active, .mfp-arrow:focus {margin-top: -15px;outline: 0;}.mfp-arrow:after {display: none;}.mfp-arrow:before {position: absolute;display: block;font-family: FontAwesome;font-size: 24px;line-height: 28px;color: #fff;margin-top: 0;border: none;opacity: 1;}.mfp-arrow.mfp-arrow-left {left: 30px;}@media (max-width: 991px) {.mfp-container {padding-left: 30px;padding-right: 30px;}.mfp-arrow.mfp-arrow-left {left: 5px;}}.mfp-arrow.mfp-arrow-left:before {margin-left: 0;content: '\f177';}.mfp-arrow.mfp-arrow-right {left: auto;right: 30px;}.mfp-arrow.mfp-arrow-right:before {margin-left: 5px;content: '\f178';}.header {position: fixed;width: 100%;transition: all 0.3s ease-in-out !important;z-index: 1020;}.header .wrap-core-nav-list {position: initial;}@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {.header .wrap-core-nav-list {position: static;}}@media (max-width: 991px) {.mfp-arrow.mfp-arrow-right {right: 5px;}.header {padding: 20px 0;}.header:hover {background: 0 0;-webkit-box-shadow: none;box-shadow: none;}}.header:hover .attributes li, .header:hover .menu li a {padding: 25px 0 !important;}.header:hover .nav-header .brand {margin-top: 20px !important;}@media (max-width: 991px) {.header:hover .attributes li {padding: 0 !important;}.header:hover .nav-header .brand {margin-top: 0 !important;}}.header__white {background: #fff;-webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);}.header__inner, .header__inner:hover {background: #fff;-webkit-box-shadow: none;box-shadow: none;}.header__inner.header__sticky:hover {-webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);}.header__inner .attributes li, .header__inner .menu li a {padding: 25px 0 !important;}.header__inner .nav-header .brand {margin-top: 20px !important;}.header__sticky {background: #fff;-webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);}@media (max-width: 991px) {.header__inner .attributes li, .header__inner .menu li a {padding: 0 !important;}.header__inner .nav-header .brand {margin-top: 0 !important;}.header__sticky:hover {background: #fff;padding: 20px 0;-webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);}}.header__sticky .attributes li, .header__sticky .menu li a {padding: 25px 0 !important;}.header__sticky .nav-header .brand {margin-top: 20px !important;}@media (max-width: 991px) {.header__sticky .attributes li, .header__sticky .menu li a {padding: 0 !important;}.header__sticky .nav-header .brand {margin-top: 0 !important;}.header .attributes {margin-right: 0 !important;}}.header .menu li {padding: 0 15px;}.header .menu li a {display: inline-block;color: #2c2d2d;font-size: 14px;line-height: normal;font-family: 'Montserrat Bold', sans-serif;padding: 40px 0;text-decoration: none;}.header .menu li a:hover, .header .menu li.active a {color: #1e5ee5;}.header .menu li.dropdown a::after, .header .menu li.megamenu a::after {content: '\f0d7';font-family: FontAwesome;font-size: 11px;margin-left: 5px;padding-top: 4px;}.header .toggle-bar.core-nav-toggle {float: right;font-size: 24px;}.header .attributes {margin-left: 10px;}.header .attributes li {-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;padding: 40px 0;}.header .attributes li a.btn-header {margin-top: -6px;}@media (max-width: 991px) {.header .attributes li {padding: 0 !important;}.header .attributes li.header__button {display: none;}}.header .attributes li.header__download-icon {display: none;padding: 0;}.header .attributes li.header__download-icon a {padding: 0;font-size: 22px;line-height: auto;color: #1e5ee5;margin-top: 3px;}.header .nav-header .brand {display: inline-block;margin-top: 33px;}@media (max-width: 991px) {.header .attributes li.header__download-icon {display: inline-block;}.header .nav-header .brand {margin-top: 0;}}.header__mobile {position: fixed;width: 100%;display: none;background: #fff;padding: 20px 0;margin-bottom: 10px;-webkit-box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.12);box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.12);text-align: center;top: 0;z-index: 2;}.header__mobile-brand {display: inline-block;width: 100px;height: 25px;background-size: 100px 25px;text-indent: -99999px;}.header__mobile-toggle {position: absolute;top: 23px;font-size: 18px !important;right: 15px;color: #d7deee;}.header__mobile-toggle:hover {color: #1e5ee5;}@media (max-width: 991px) {.header__mobile {display: block;}}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu {margin-top: 0;border-radius: 0;-webkit-box-shadow: none;box-shadow: none;border: 1px solid #d7deee;border-bottom: none;}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li {padding: 0;}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li a {display: block;padding: 15px 20px !important;font-family: Montserrat, sans-serif;border-bottom: 1px solid #d7deee;}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li a::after {display: none;margin-left: 0;padding-top: 0;}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li a:hover {background: #f8faff;}@media (max-width: 991px) {.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li a:hover {background: 0 0;}.core-responsive-slide.open {margin-left: 100%;margin-right: -100%;}}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li.dropdown {position: relative;}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu li.dropdown::after {position: absolute;content: '\f0d7';font-family: FontAwesome;font-size: 11px;top: 50%;right: 20px;margin-top: -12px;}.masthead__browser-wrapper .owl-carousel:after, .masthead__browser-wrapper .owl-carousel:before, .masthead__style-overlay, .section__divider::before, .section__heading:before {content: '';}.core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu .dropdown > .dropdown-menu {top: 0;left: auto;right: 100% !important;}.header .menu li.active .dropdown-menu li a {color: #000;}.header .menu li.active .dropdown-menu li a:hover {color: #1e5ee5;}.dropdown-overlay {position: fixed;width: 100%;height: 100%;display: none;background: #fff;left: 0;top: 0;opacity: 0;}@media (max-width: 991px) {.core-responsive-slide .core-nav .wrap-core-nav-list {padding-top: 65px;background: #fff;width: 100%;border: none;z-index: 1070;margin-left: -100%;}.header .core-nav .wrap-core-nav-list.right .menu, .header:hover .core-nav .wrap-core-nav-list.right .menu, .header__inner .core-nav .wrap-core-nav-list.right .menu, .header__sticky .core-nav .wrap-core-nav-list.right .menu {padding: 0 15px !important;}.header .core-nav .wrap-core-nav-list.right .menu li, .header:hover .core-nav .wrap-core-nav-list.right .menu li, .header__inner .core-nav .wrap-core-nav-list.right .menu li, .header__sticky .core-nav .wrap-core-nav-list.right .menu li {float: left;border-bottom: 1px solid #d7deee;padding: 10px 0 !important;}.header .core-nav .wrap-core-nav-list.right .menu li:last-child, .header:hover .core-nav .wrap-core-nav-list.right .menu li:last-child, .header__inner .core-nav .wrap-core-nav-list.right .menu li:last-child, .header__sticky .core-nav .wrap-core-nav-list.right .menu li:last-child {border-bottom: none;margin-bottom: 5px;}.header .core-nav .wrap-core-nav-list.right .menu li a, .header:hover .core-nav .wrap-core-nav-list.right .menu li a, .header__inner .core-nav .wrap-core-nav-list.right .menu li a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li a {font-size: 13px;padding: 0 !important;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown::after, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown::after, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown::after, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown::after {display: none;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu {position: relative;border: none;display: block !important;margin: 10px 0 0 !important;padding: 0 !important;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li {border: none;padding: 0 !important;margin-bottom: 5px;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li:last-child, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li:last-child, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li:last-child, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li:last-child {border-bottom: none;margin-bottom: 0;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li a, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li a, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li a {padding: 0 !important;font-size: 13px !important;border: none;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown {margin-top: 5px !important;padding-top: 10px !important;border-top: 1px solid #d7deee;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown a, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown a, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown a {font-family: 'Montserrat Bold', sans-serif;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a {font-family: Montserrat, sans-serif;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown a, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown a, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown a {font-family: 'Montserrat Bold', sans-serif;}.header .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li.dropdown .dropdown-menu li a {font-family: Montserrat, sans-serif;}}.header .menu .megamenu__widget-group-link span, .header .menu .megamenu__widget-list li a, .masthead__content-subtitle {font-family: Montserrat, sans-serif;}@media (max-width: 991px) {.header .core-nav .wrap-core-nav-list.right .menu li.dropdown a::after, .header .core-nav .wrap-core-nav-list.right .menu li.megamenu a::after, .header:hover .core-nav .wrap-core-nav-list.right .menu li.dropdown a::after, .header:hover .core-nav .wrap-core-nav-list.right .menu li.megamenu a::after, .header__inner .core-nav .wrap-core-nav-list.right .menu li.dropdown a::after, .header__inner .core-nav .wrap-core-nav-list.right .menu li.megamenu a::after, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.dropdown a::after, .header__sticky .core-nav .wrap-core-nav-list.right .menu li.megamenu a::after {display: none;}.header .menu .megamenu__widget, .header:hover .menu .megamenu__widget, .header__inner .menu .megamenu__widget, .header__sticky .menu .megamenu__widget {float: left;width: 100%;margin-top: 10px !important;}.header .menu .megamenu__widget-lis, .header:hover .menu .megamenu__widget-lis, .header__inner .menu .megamenu__widget-lis, .header__sticky .menu .megamenu__widget-lis {margin-top: 5px;margin-bottom: 5px;}}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content {margin-top: -2px;border: 1px solid #d7deee;-webkit-box-shadow: 0 7px 15px 0 rgba(0, 0, 0, 0.04);box-shadow: 0 7px 15px 0 rgba(0, 0, 0, 0.04);padding: 60px 0;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content .container {padding-right: 30%;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content:before {position: absolute;content: '';width: 35%;height: 100%;display: block;background-size: 100% auto;top: 0;right: 0;}@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content:before {background-position: 0 100px;}}@media (min-width: 992px) and (max-width: 1199px) {.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content .container {padding-right: 20%;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content:before {width: 20%;}}@media (max-width: 991px) {.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content:before {display: none;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content {padding: 0;display: block;-webkit-box-shadow: none;box-shadow: none;border: none;}.core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content .col-megamenu {padding-left: 0;padding-right: 0;}}.header .menu .megamenu__widget {display: block;position: relative;padding: 0;margin: 0;}.header .menu .megamenu__widget-title {text-transform: uppercase;font-size: 12px;line-height: normal;}@media (max-width: 991px) {.header .menu .megamenu__widget {margin-top: 20px;}.header .menu .megamenu__widget-title {margin-bottom: 10px;margin-top: 10px;}}.header .menu .megamenu__widget-group {margin-bottom: 20px;}@media (max-width: 991px) {.header .menu .megamenu__widget-group {margin-bottom: 10px;}}.header .menu .megamenu__widget-group-link {margin-bottom: 0;padding: 0 !important;color: #000 !important;}.header .menu .megamenu__widget-group-link::after, .header .menu .megamenu__widget-group-link::before {display: none;}.header .menu .megamenu__widget-group-link span {display: block;font-size: 12px;line-height: 18px;margin-top: 5px;color: #2c2d2d;}.header .menu .megamenu__widget-group-link:active span, .header .menu .megamenu__widget-group-link:focus span, .header .menu .megamenu__widget-group-link:hover span {color: #2c2d2d;}.header .menu .megamenu__widget-group:hover .megamenu__widget-group-link {color: #1e5ee5 !important;}.header .menu .megamenu__widget-list {float: none;display: block;margin: 0;padding: 0;}@media (max-width: 991px) {.header .menu .megamenu__widget-list {float: left;width: 100%;}}.header .menu .megamenu__widget-list li {float: none;display: block;list-style: none;margin: 0;padding: 0;}@media (max-width: 991px) {.header .menu .megamenu__widget-list li {border: none;float: left;width: 100%;padding: 0 !important;margin-top: 10px;}}.header .menu .megamenu__widget-list li a {position: relative;padding: 0 0 0 30px !important;margin-bottom: 20px;}.header .menu .megamenu__widget-list li a::after, .header .menu .megamenu__widget-list li a::before {display: none;}.header .menu .megamenu__widget-list li a .list--icon {color: #1e5ee5;position: absolute;top: 2px;left: 0;}.header .menu .megamenu__widget-list li:last-child a {margin-bottom: 0;}@media (max-width: 991px) {.header .menu .megamenu__widget-list li a {font-size: 15px;margin-bottom: 0;padding: 0 !important;}.header .menu .megamenu__widget-list li a .list--icon {display: none;}.header .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li, .header:hover .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li, .header__sticky .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li {border-bottom: none;padding: 0 !important;}.header .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li:last-child, .header:hover .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li:last-child, .header__sticky .core-nav .wrap-core-nav-list.right .menu li .megamenu__widget-list li:last-child {margin-bottom: 0 !important;}}.header:hover .menu li a.megamenu__widget-group-link {padding: 0 !important;}.masthead {position: relative;background: #edf3ff;padding: 100px 0 0px;}.masthead__white {background: #fff;}.masthead__style-overlay, .masthead__style-watter {background-repeat: no-repeat;background-size: 100% auto;}.masthead__fullscreen {width: 100%;height: 100vh;}.masthead__content {display: block;margin-bottom: 40px;}.masthead__content-mtop {margin-top: 80px;}@media (max-width: 991px) {.masthead {padding: 80px 0 40px;}.masthead__content {margin-bottom: 20px;}.masthead__content-mtop {margin-top: 0;}}.masthead__content-image {max-width: 100% !important;height: auto;margin-bottom: -3px;}.masthead__content-action {display: block;margin-top: 40px;}.masthead__content-action .btn {margin-right: 10px;margin-bottom: 10px;}@media (max-width: 991px) {.masthead__content-action .btn {margin-right: 5px;margin-bottom: 5px;}.masthead__content-action {margin-top: 20px;}}.masthead__offset-bottom {margin-bottom: -140px;}.masthead__style-watter {position: relative;background-image: url(../images/masthead-watter-bg2x.png);background-position: bottom left;}@media all and (-webkit-min-device-pixel-ratio: 1.5) {.masthead__style-watter {background-image: url(../images/masthead-watter-bg2x.png);}}@media (max-width: 991px) {.masthead__offset-bottom {margin-bottom: 0;}.masthead__style-watter {padding-bottom: 140px;text-align: center;}}@media (max-width: 767px) {.masthead__style-watter {padding-bottom: 100px;}}.masthead__style-watter--image {position: relative;display: block;text-align: center;padding-top: 180px;}.masthead__style-watter--icon {position: absolute;display: block;width: 100%;height: auto;z-index: 9;top: 0;-webkit-animation-duration: 4.5s;animation-duration: 4.5s;-webkit-transition: all 0.3s ease-in-out;transition: all 0.3s ease-in-out;}.masthead__style-overlay {position: absolute;background-position: top left;display: block;width: 520px;height: 596px;top: 0;right: 0;}@media all {.masthead__style-overlay {background-image: url(../images/masthead-overlay2x.png);}}@media all {.masthead__style-overlay {width: 16.25rem;height: 18.625rem;}}@media all {.masthead__style-watter--image {padding-top: 90px;}.masthead__style-overlay {display: block;}}.masthead__form {position: relative;display: block;background: #fff;margin-bottom: 20px;padding: 20px 20px 40px;border: 1px solid #bac6e1;border-radius: 4px;-webkit-box-shadow: 0 3px 30px 0 rgba(0, 0, 0, 0.1);box-shadow: 0 3px 30px 0 rgba(0, 0, 0, 0.1);}.masthead__form-action {position: relative;display: block;text-align: center;margin-top: 20px;margin-bottom: -60px;}.masthead__form-action .btn {display: inline-block;}.masthead__browser {display: block;position: relative;background-repeat: no-repeat;background-position: top left;background-size: 100% auto;overflow: hidden;}.masthead__browser-wrapper {padding: 30px 40px 0;}@media (max-width: 991px) {.masthead__browser {background-position: bottom left;}.masthead__browser-wrapper {padding: 15px 0 0;}}.masthead__browser-wrapper .owl-carousel {-webkit-box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 59px 0 rgba(0, 0, 0, 0.15);}.masthead__browser-wrapper .owl-carousel:before {position: absolute;width: 100%;height: 30px;background: #d7deee;border-radius: 4px 4px 0 0;top: -30px;left: 0;}.masthead__browser-wrapper .owl-carousel:after {position: absolute;width: 60px;height: 10px;background-repeat: no-repeat;background-position: top left;background-size: auto 100%;top: -20px;left: 15px;z-index: 2;}.masthead__video {position: relative;display: block;-webkit-box-shadow: 0 16px 74px 6px rgba(0, 0, 0, 0.1);box-shadow: 0 16px 74px 6px rgba(0, 0, 0, 0.1);margin-bottom: -80px;}.masthead__gradient {padding: 0;background: #5a25c2;background: -webkit-gradient(linear, left top, left bottom, from(#5a25c2), to(#5f63d6));background: linear-gradient(to bottom, #5a25c2 0, #5f63d6 100%);filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#5a25c2', endColorstr='#5f63d6', GradientType=0);}.features__wrapper:after, .features__wrapper:before {filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff', GradientType=1);}.masthead__gradient-content {background-repeat: repeat;background-position: top left;padding: 180px 0 80px;}@media (max-width: 991px) {.masthead__video {margin-bottom: -40px;}.masthead__gradient-content {padding: 80px 0 40px;}}.masthead__gradient .masthead__content, .masthead__gradient .masthead__content-title {color: #fff;}.innerhead {background: #edf3ff;padding: 180px 0 80px;}@media (max-width: 991px) {.innerhead {padding: 90px 0 40px;}}.section {position: relative;padding: 50px 0;}.section__divider {position: relative;display: block;width: 100%;height: auto;margin-bottom: 40px;}@media (max-width: 991px) {.section {padding: 40px 0;}.section__divider {margin-bottom: 20px;}}.section__divider::before {display: block;width: 720px;height: 186px;margin: 0 auto;background-repeat: no-repeat;background-position: bottom left;background-size: 100% auto;}@media (min-width: 992px) and (max-width: 1199px) {.section__divider::before {width: 550px;height: 142px;}}.section__purple {background: #403595;color: #fff;}.section__gray {background: #edf3ff;}.section__gray-watter--bottom, .section__gray-watter--top {background-repeat: no-repeat;background-size: 100% auto;background-color: #edf3ff;}@media (max-width: 991px) {.section__divider::before {display: none;}.section__gray-watter--top {padding: 380px 0 80px;}}.section__gray-watter--bottom {padding: 80px 0 380px;background-position: bottom center;}@media (max-width: 991px) {.section__gray-watter--bottom {padding: 40px 0 80px;}}.section__heading {display: block;position: relative;padding-bottom: 30px;margin-bottom: 40px;}@media (max-width: 991px) {.section__heading {padding-bottom: 15px;margin-bottom: 20px;}}.section__heading:before {position: absolute;display: block;background-size: cover;width: 85px;height: 8px;bottom: 0;left: 0;}.section__heading-center {text-align: center;}.section__heading-center:before {left: 50%;margin-left: -42.5px;}.section__column {display: block;position: relative;margin-bottom: 40px;}.section__column .column-icon {position: absolute;top: 0;}.section__column-left {padding-left: 80px;}@media (max-width: 991px) {.section__column {margin-bottom: 20px;}.section__column-left {padding-left: 50px;}}.section__column-left .column-icon {left: 0;}.section__column-top--left {border-bottom: 1px solid #d7deee;}.section__column-top--left-action {display: block;margin-bottom: 40px;}.section__column-top--left-action a {display: inline-block;}.section__contact {position: relative;}.section__contact::before, .section__contact:after {content: '';height: 100%;position: absolute;left: 0;}.section__contact::before {display: block;width: 20%;top: 0;background: #f4f7fc;}@media (max-width: 991px) {.section__column-top--left-action {margin-bottom: 20px;}.section__contact::before {display: none;}}.section__contact:after {display: block;width: 35%;bottom: 0;}@media (max-width: 991px) {.section__contact:after {display: none;}}.footer {position: relative;display: block;padding: 100px 0 0;}.footer__widget {margin-bottom: 60px;}@media (max-width: 991px) {.footer {padding: 50px 0 0;}.footer__widget {margin-bottom: 20px;}}.footer__widget-title {margin-bottom: 40px;}@media (max-width: 991px) {.footer__widget-title {margin-bottom: 10px;}}.footer__widget-logo {display: inline-block;margin-bottom: 40px;}.footer__widget-contact {display: block;position: relative;padding-left: 30px;}@media (max-width: 991px) {.footer__widget-logo {margin-bottom: 20px;}.footer__widget-contact {padding-left: 25px;}}.footer__widget-contact .fa {font-family: FontAwesome;position: absolute;left: 0;top: 0;}.footer__widget-linklist {margin: 0;padding: 0;}.footer__widget-linklist li {margin: 0 0 15px;padding: 0;list-style: none;}@media (max-width: 991px) {.footer__widget-linklist li {margin: 0 0 7.5px;}}.footer__widget-network, .footer__widget-network li {margin: 0;padding: 0;display: inline-block;}.footer__widget-linklist li a {color: #2c2d2d;}.footer__widget-linklist li a:hover {color: #0f44b2;}.footer__widget-network li {list-style: none;}.footer__widget-network-link {background: #eaeef4;display: inline-block;width: 40px;height: 40px;border-radius: 50%;text-align: center;font-size: 18px;line-height: 30px;color: #7483a3;overflow: hidden;padding-top: 5px;margin-right: 5px;}.footer__widget-network-link:hover {background: #403595 !important;color: #fff;}.footer__subfooter {display: block;border-top: 1px solid #d7deee;margin-top: 40px;padding: 40px 0 15px;}@media (max-width: 991px) {.footer__subfooter {margin-top: 30px;padding: 20px 0 15px;}.footer__subfooter .text-right {text-align: left !important;}}.footer__subfooter-liststyle {display: block;margin: 0;padding: 0;}.footer__subfooter-liststyle li {display: inline-block;list-style: none;margin: 0;padding: 0;}.footer__subfooter-liststyle li a {color: #2c2d2d;}.footer__subfooter-liststyle li a:hover {color: #0f44b2;}.footer__subfooter-liststyle li + li::before {display: inline-block;padding-right: 10px;content: '|';}.aside__right {padding-left: 30px;}.aside__left {padding-right: 30px;}.aside__widget {margin-bottom: 40px;}@media (max-width: 991px) {.aside__right {padding-left: 0;margin-top: 40px;}.aside__left {padding-right: 0;margin-top: 40px;}.aside__widget {margin-bottom: 20px;}}.aside__widget-title {position: relative;padding-bottom: 20px;margin-bottom: 30px;}.aside__widget-title.instagram:after, .aside__widget-title:before {position: absolute;content: '';left: 0;}.aside__widget-title:before {display: block;background-size: cover;width: 85px;height: 8px;bottom: 0;}.aside__widget-title.instagram {padding-left: 35px;}@media (max-width: 991px) {.aside__widget-title {padding-bottom: 0;margin-bottom: 15px;}.aside__widget-title:before {display: none;}.aside__widget-title.instagram {padding-left: 20px;}}.aside__widget-title.instagram:after {display: block;background-size: cover;width: 25px;height: 25px;top: 5px;}.aside__widget-recent--post {margin: 0;padding: 0;}.aside__widget-recent--post li {min-height: 55px;position: relative;list-style: none;margin: 0 0 20px;padding: 0 0 0 70px;}@media (max-width: 991px) {.aside__widget-title.instagram:after {width: 15px;height: 15px;}.aside__widget-recent--post li {margin: 0 0 10px;padding: 0 0 0 70px;}}.aside__widget-recent--post li:last-child {margin: 0;}.aside__widget-recent--post-thumb {position: absolute;width: 55px;height: 55px;top: 0;left: 0;}.aside__widget-recent--post-title {position: relative;line-height: 28px;font-family: Montserrat, sans-serif;margin: 0;}.aside__widget-recent--post-title a {color: #000;}.aside__widget-instagram {float: left;padding: 0;margin: -2px -2px 10px;width: 100%;}@media (max-width: 991px) {.aside__widget-instagram {margin: -2px -2px 5px;}}.aside__widget-instagram li {float: left;list-style: none;width: 25%;height: auto;margin: 0;padding: 2px;}.aside__widget-instagram li img {width: 100%;max-width: auto;height: auto;}.aside__widget-instagram--follow {display: block;text-align: center;}.aside__widget-instagram--follow label {font-family: 'Montserrat Bold', sans-serif;margin: 0;}.aside__widget-categories {margin: 0;padding: 0 0 0 20px;}@media (max-width: 991px) {.aside__widget-categories {padding: 0 0 0 10px;}}.aside__widget-tags {margin: 0;padding: 0;}.aside__widget-tags li {list-style: none;display: inline-block;margin: 0 5px 5px 0;padding: 0;}.aside__widget-tags li a {border-radius: 20px;background: #d7deee;color: #000;padding: 2px 15px;}@media (max-width: 991px) {.aside__widget-tags li a {padding: 2px 7.5px;}}.aside__widget-tags li a:active, .aside__widget-tags li a:focus, .aside__widget-tags li a:hover {text-decoration: none;background: #1e5ee5;color: #fff;}.section__cta {background: center center no-repeat #0060fe;padding: 100px 0;color: #fff;text-align: center;}@media (max-width: 991px) {.section__cta {padding: 50px 0;}}.section__cta-column {position: relative;border-radius: 20px;z-index: 1000;padding: 40px 0;-webkit-box-shadow: 0 30px 59px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 30px 59px 0 rgba(0, 0, 0, 0.15);}.section__cta-subscribe {position: relative;display: block;margin-top: 40px;}@media (max-width: 991px) {.section__cta-column {border-radius: 10px;padding: 20px 0;-webkit-box-shadow: 0 15x 30px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 15x 30px 0 rgba(0, 0, 0, 0.15);}.section__cta-subscribe {margin-top: 0;}}.section__cta-subscribe-input {font-family: Montserrat, sans-serif;width: 100%;background: 0 0;padding: 20px 0;border: none;color: #b7c3de;border-bottom: 1px solid #695faf;}.section__cta-subscribe-input:focus {outline: 0;border-color: #fff;}.section__cta-subscribe-input::-webkit-input-placeholder {color: #b7c3de;}.section__cta-subscribe-input::-ms-input-placeholder {color: #b7c3de;}.section__cta-subscribe-input::placeholder {color: #b7c3de;}.section__cta-subscribe-button {position: absolute;right: 0;top: 0;}.section__cta-offset {margin-bottom: -100px;}@media (max-width: 991px) {.section__cta-subscribe-input {padding: 0 0 10px;}.section__cta-subscribe-button {position: relative;display: block;width: 100%;}.section__cta-offset {margin-bottom: -50px;}}.back-to-top {position: fixed;z-index: 99;bottom: 50px;right: 50px;width: 50px;height: 50px;color: #000;border-radius: 50%;-moz-border-radius: 50%;-webkit-border-radius: 50%;border: 2px solid #edf3ff;background-color: #edf3ff;text-align: center;font-size: 12px;cursor: pointer;padding-top: 8px;display: none;-webkit-box-shadow: 0 15px 16px -5px rgba(150, 155, 181, 0.55);box-shadow: 0 15px 16px -5px rgba(150, 155, 181, 0.55);transition: all 0.3s ease-in-out;}.back-to-top:active, .back-to-top:focus {color: #000;background-color: #edf3ff;border-color: #edf3ff;}.back-to-top:hover {background: #d7deee;border-color: #d7deee;}.back-to-top.active {display: block;}.back-to-top__icon {display: block;margin-top: -5px;font-size: 24px;}.back-to-top__text {display: block;font-size: 12px;line-height: 12px;margin-top: -2px;}@media (max-width: 767px) {.back-to-top {right: 5vw;bottom: 5vw;}}.accordion__group {margin-bottom: 30px;}.accordion__link {position: relative;border: none;display: block;width: 100%;background: #f1f5fd;padding: 20px 60px 20px 30px;text-align: left;color: #000;font-size: 18px;cursor: pointer;}.accordion__link:focus {outline: 0;}.accordion__link:after {position: absolute;content: '\f068';font-family: FontAwesome;top: 50%;right: 30px;margin-top: -11px;}@media (max-width: 991px) {.accordion__group {margin-bottom: 15px;}.accordion__link {padding: 10px 35px 10px 15px;}.accordion__link:after {font-size: 14px;right: 15px;}}.accordion__link.collapsed:after {content: '\f067';}.btn-link::after, .form__subscribe-button:after {content: '\f178';}.accordion__collapse {background: #f1f5fd;margin-top: 2px;}.accordion__collapse .card-body {padding: 30px;}.form {display: block;margin-bottom: 40px;}@media (max-width: 991px) {.accordion__collapse .card-body {padding: 15px;}.form {margin-bottom: 20px;}}.form button:focus, .form input:focus, .form select:focus, .form textarea:focus {outline: 0;}.form-control {background: #fafbfc;border: 1px solid #bac6e1;padding: 20px 15px;-webkit-box-shadow: inset 1px 1px 5px 0 rgba(0, 0, 0, 0.05);box-shadow: inset 1px 1px 5px 0 rgba(0, 0, 0, 0.05);}.form-control:focus {outline: 0;-webkit-box-shadow: none;box-shadow: none;border: 1px solid #1e5ee5;}.form__subscribe {position: relative;display: block;}.form__subscribe-rounded {border-radius: 80px;}.form__subscribe-shadow {-webkit-box-shadow: 4px 6px 0 rgba(0, 0, 0, 0.15);box-shadow: 4px 6px 0 rgba(0, 0, 0, 0.15);}.form__subscribe-input {background: #fff;width: 100%;border: 2px solid;padding: 20px;line-height: normal;}@media (max-width: 991px) {.form__subscribe-input {line-height: 24px;padding: 7.5px 10px;}}.form__subscribe-input-blue {border-color: #1e5ee5;}.form__subscribe-input-green {border-color: #4aa47b;}.form__subscribe-input-yellow {border-color: #f9d750;}.form__subscribe-input-purple {border-color: #403595;}.form__subscribe-input-red {border-color: #e24c43;}.form__subscribe-input-orange {border-color: #f26822;}.form__subscribe-button {line-height: 18px;position: absolute;top: 0;right: 0;border-radius: 0 80px 80px 0;padding: 20px;border: 2px solid;transition: all 0.3s ease-in-out;}.btn-link, .form__subscribe-button:after {position: relative;}@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {.form__subscribe-button {padding: 20px 20px 18px;}}.form__subscribe-button:hover::after {right: -5px;}.form__subscribe-button:after {font-family: FontAwesome;display: inline-block;margin-left: 10px;top: 1px;right: 0;transition: all 0.3s ease-in-out;}.btn, .btn-link, .form-group label {font-family: 'Montserrat Bold', sans-serif;}.form__subscribe-button-blue {background: #1e5ee5;border-color: #1e5ee5;color: #fff;}.form__subscribe-button-green {background: #4aa47b;border-color: #4aa47b;color: #fff;}.form__subscribe-button-yellow {background: #f9d750;border-color: #f9d750;color: #fff;}.form__subscribe-button-purple {background: #403595;border-color: #403595;color: #fff;}.form__subscribe-button-red {background: #e24c43;border-color: #e24c43;color: #fff;}.form__subscribe-button-orange {background: #f26822;border-color: #f26822;color: #fff;}.form-group {display: block;margin-bottom: 20px;}@media (max-width: 991px) {.form__subscribe-button {line-height: 24px;padding: 7.5px 10px;}.form__subscribe-button:after {right: 5px;}.form-group {margin-bottom: 10px;}}.form-action {display: block;margin-bottom: 20px;margin-top: 10px;}@media (max-width: 991px) {.form-action {margin-top: 5px;margin-bottom: 10px;}}.select2-container {width: 100% !important;}.select2-container--default .select2-selection--single {font-size: 12px;line-height: 22px;background: #fafbfc;border: 1px solid #bac6e1;padding: 20px 15px;-webkit-box-shadow: inset 1px 1px 5px 0 rgba(0, 0, 0, 0.05);box-shadow: inset 1px 1px 5px 0 rgba(0, 0, 0, 0.05);color: #2c2d2d;width: 100%;height: calc(2.25rem + 2px);}.select2-container--default .select2-selection--single:focus {outline: 0;}.select2-container--default .select2-selection--single .select2-selection__rendered {padding: 0;font-size: 12px;line-height: 22px;margin-top: -10px;}.select2-container--default .select2-selection--single .select2-selection__placeholder {color: #6c757d;}.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple:focus, .select2-container--default.select2-container--open.select2-container--above .select2-selection--single:focus {outline: 0;}.select2-dropdown {border: 1px solid #bac6e1;}.select2-container--default .select2-results__option[aria-selected='true'] {background-color: #d7deee;}.select2-container--default .select2-results__option--highlighted[aria-selected] {background-color: #1e5ee5;}.select2-container--default .select2-selection--single .select2-selection__arrow {top: 8px;right: 10px;}button:active, button:focus, button:hover {outline: 0;-webkit-box-shadow: none !important;box-shadow: none !important;}.btn {padding: 15px 30px;font-size: 16px;line-height: normal;border: 1px solid transparent;border-radius: 2px;}@media (max-width: 991px) {.btn {font-size: 14px;padding: 12.5px 20px;}}.btn:active, .btn:focus, .btn:hover {outline: 0;-webkit-box-shadow: none !important;box-shadow: none !important;text-decoration: none;}.btn-rounded {border-radius: 80px;}.btn-sm {font-size: 14px;line-height: 14px;padding: 10px 20px;}@media (max-width: 991px) {.btn-sm {font-size: 12px;line-height: 12px;padding: 10px 15px;}.btn-xs, .header .attributes li a.btn-header {font-size: 12px;line-height: 12px;padding: 8px 20px;}}.btn-xs, .header .attributes li a.btn-header {font-size: 12px;line-height: 12px;padding: 8px 20px;}.btn-lg {font-size: 24px;line-height: normal;padding: 20px 40px;}@media (max-width: 991px) {.btn-lg {font-size: 18px;padding: 15px 25px;}}.btn-icon-left .fa {margin-right: 5px;}.btn-icon-right .fa {margin-left: 5px;}.btn-default {background: #2c2d2d;color: #fff;}.btn-default:active, .btn-default:focus, .btn-default:hover {background: #000;color: #fff;text-decoration: none;}.btn-primary, .header .attributes li a.btn-header {background: #1e5ee5;border-color: #1e5ee5;color: #fff;}.btn-primary:active, .btn-primary:focus, .btn-primary:hover, .header .attributes li a.btn-header:active, .header .attributes li a.btn-header:focus, .header .attributes li a.btn-header:hover {background: #0f44b2;border-color: #0f44b2;color: #fff;text-decoration: none;}.btn-green {background: #4aa47b;border-color: #4aa47b;color: #fff;}.btn-green:active, .btn-green:focus, .btn-green:hover {background: #2c7855;border-color: #2c7855;color: #fff;text-decoration: none;}.btn-purple {background: #403595;border-color: #403595;color: #fff;}.btn-purple:active, .btn-purple:focus, .btn-purple:hover {background: #271e6f;border-color: #271e6f;color: #fff;text-decoration: none;}.btn-red {background: #e24c43;border-color: #e24c43;color: #fff;}.btn-red:active, .btn-red:focus, .btn-red:hover {background: #ab2921;border-color: #ab2921;color: #fff;text-decoration: none;}.btn-yellow {background: #f9d750;border-color: #f9d750;color: #fff;}.btn-yellow:active, .btn-yellow:focus, .btn-yellow:hover {background: #d0af2d;border-color: #d0af2d;color: #fff;text-decoration: none;}.btn-orange {background: #f26822;border-color: #f26822;color: #fff;}.btn-orange:active, .btn-orange:focus, .btn-orange:hover {background: #c74e11;border-color: #c74e11;color: #fff;text-decoration: none;}.btn-white {background: #fff;border-color: #fff;color: #2c2d2d;}.btn-white:active, .btn-white:focus, .btn-white:hover {background: #edf3ff;border-color: #edf3ff;color: #2c2d2d;text-decoration: none;}.btn-stroke {background: 0 0;}.btn-stroke.btn-default {border-color: #2c2d2d;color: #2c2d2d;}.btn-stroke.btn-default:active, .btn-stroke.btn-default:focus, .btn-stroke.btn-default:hover {background: #2c2d2d;color: #fff;text-decoration: none;}.btn-stroke.btn-primary, .header .attributes li a.btn-stroke.btn-header {border-color: #1e5ee5;color: #1e5ee5;}.btn-stroke.btn-primary:active, .btn-stroke.btn-primary:focus, .btn-stroke.btn-primary:hover, .header .attributes li a.btn-stroke.btn-header:active, .header .attributes li a.btn-stroke.btn-header:focus, .header .attributes li a.btn-stroke.btn-header:hover {background: #1e5ee5;color: #fff;text-decoration: none;}.btn-stroke.btn-green {border-color: #4aa47b;color: #4aa47b;}.btn-stroke.btn-green:active, .btn-stroke.btn-green:focus, .btn-stroke.btn-green:hover {background: #4aa47b;color: #fff;text-decoration: none;}.btn-stroke.btn-purple {border-color: #403595;color: #403595;}.btn-stroke.btn-purple:active, .btn-stroke.btn-purple:focus, .btn-stroke.btn-purple:hover {background: #403595;color: #fff;text-decoration: none;}.btn-stroke.btn-red {border-color: #e24c43;color: #e24c43;}.btn-stroke.btn-red:active, .btn-stroke.btn-red:focus, .btn-stroke.btn-red:hover {background: #e24c43;color: #fff;text-decoration: none;}.btn-stroke.btn-yellow {border-color: #f9d750;color: #f9d750;}.btn-stroke.btn-yellow:active, .btn-stroke.btn-yellow:focus, .btn-stroke.btn-yellow:hover {background: #f9d750;color: #fff;text-decoration: none;}.btn-stroke.btn-orange {border-color: #f26822;color: #f26822;}.btn-stroke.btn-orange:active, .btn-stroke.btn-orange:focus, .btn-stroke.btn-orange:hover {background: #f26822;color: #fff;text-decoration: none;}.btn-stroke.btn-white {border-color: #fff;color: #fff;}.btn-stroke.btn-white:active, .btn-stroke.btn-white:focus, .btn-stroke.btn-white:hover {background: #fff;color: #fff;text-decoration: none;}.btn-link {color: #2c2d2d;padding-right: 20px;transition: all 0.3s ease-in-out;}.btn-link:active, .btn-link:focus, .btn-link:hover {color: #0f44b2;}.btn-link:hover::after {right: -5px;}.btn-link::after {position: absolute;font-family: FontAwesome;transition: all 0.3s ease-in-out;right: 0;top: -1.5px;}.pricing__content p, .pricing__content-currency, .pricing__content-package, .pricing__content-price, .pricing__popular .pricing__content p, .pricing__popular .pricing__content-currency, .pricing__popular .pricing__content-package {font-family: 'Montserrat Light', sans-serif;}.btn-link.btn-primary, .header .attributes li a.btn-link.btn-header {background: 0 0;color: #1e5ee5;}.btn__play {display: inline-block;width: 80px;height: 80px;border: 1px solid transparent;border-radius: 50%;background: #1e5ee5;color: #fff;text-align: center;font-size: 24px;line-height: 24px;overflow: hidden;padding-top: 25px;}.btn__play:active, .btn__play:focus, .btn__play:hover {background: #0f44b2;color: #fff;}.btn__play-stroke {background: 0 0;color: #1e5ee5;border: 1px solid #1e5ee5;}.btn__play-stroke:active, .btn__play-stroke:focus, .btn__play-stroke:hover {background: #0f44b2;color: #fff;border: 1px solid #0f44b2;}.btn__play-white {background: #fff;color: #1e5ee5;}.btn__play-white.btn__play-stroke {background: 0 0;color: #fff;border: 1px solid #fff;}.btn__play-white.btn__play-stroke:active, .btn__play-white.btn__play-stroke:focus, .btn__play-white.btn__play-stroke:hover {background: #fff;color: #1e5ee5;border: 1px solid #fff;}.column__icon {display: block;position: relative;margin-bottom: 30px;}@media (max-width: 991px) {.btn__play {font-size: 18px;line-height: 18px;width: 60px;height: 60px;padding-top: 20px;}.column__icon {margin-bottom: 15px;}}.column__icon .text-normal {line-height: 24px;}.column__icon-left {padding-left: 40px;}@media (max-width: 991px) {.column__icon-left {padding-left: 30px;}}.column__icon-left .fa {position: absolute;left: 0;top: 0;}.column__boxed {position: relative;background: #fff;border-radius: 5px;text-align: left;padding: 30px 30px 30px 110px;}.pricing__column, .pricing__grid, .pricing__grid-header {display: block;text-align: center;}.column__boxed-title {margin-bottom: 10px;}.column__boxed-icon {position: absolute;top: 30px;left: 30px;}@media (max-width: 991px) {.column__boxed {padding: 15px 15px 15px 60px;}.column__boxed-title {margin-bottom: 5px;}.column__boxed-icon {top: 20px;left: 15px;}}.pricing {float: left;width: 100%;position: relative;margin-bottom: 60px;}.pricing__column {position: relative;border: 1px solid transparent;border-radius: 4px;float: left;width: 31%;margin-top: 20px;padding: 40px 20px;}.pricing__column.pp-fist {border-right: none;border-top-right-radius: 0;border-bottom-right-radius: 0;}@media (max-width: 991px) {.pricing {margin-bottom: 30px;}.pricing__column {width: 100%;margin-top: 0;padding: 30px 10px;margin-bottom: 20px;}.pricing__column:last-child {margin-bottom: 0;}.pricing__column.pp-fist {border-width: 1px;border-style: solid;border-top-right-radius: 4px;border-bottom-right-radius: 4px;}}.pricing__column.pp-last {border-left: none;border-top-left-radius: 0;border-bottom-left-radius: 0;}.pricing__heading h3 {margin-bottom: 10px;}@media (max-width: 991px) {.pricing__column.pp-last {border-width: 1px;border-style: solid;border-top-left-radius: 4px;border-bottom-left-radius: 4px;}.pricing__heading h3 {margin-bottom: 5px;}}.pricing__icon {font-size: 42px;margin-bottom: 10px;}@media (max-width: 991px) {.pricing__icon {font-size: 36px;margin-bottom: 5px;}}.pricing__content {margin-bottom: 20px;}@media (max-width: 991px) {.pricing__content {margin-bottom: 10px;}}.pricing__content p {margin-bottom: 5px;}.pricing__content-price {position: relative;padding-left: 10px;}.pricing__content-currency {position: absolute;line-height: auto;font-style: normal;margin-top: 5px;left: 0;}.pricing__popular {position: relative;width: 38%;margin-top: 0;padding: 60px 20px 50px;z-index: 4;border-radius: 8px;border-width: 3px;border-color: #1e5ee5;-webkit-box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);}.pricing__popular .pricing__icon {font-size: 72px;color: #1e5ee5;}@media (max-width: 991px) {.pricing__content p {margin-bottom: 0;}.pricing__popular {width: 100%;padding: 30px 10px;}.pricing__popular .pricing__icon {font-size: 60px;}}.pricing__popular .pricing__heading {margin-bottom: 20px;}.pricing__popular .pricing__content p {margin-bottom: 10px;}.pricing__popular .pricing__content-price {font-size: 54px;}.pricing__popular .pricing__action {position: absolute;width: 100%;left: 0;bottom: -30px;}.pricing__popular .btn {padding: 20px 45px;background: #fff;}.pricing__green {border-color: #4aa47b;}.pricing__green .pricing__content-package, .pricing__green .pricing__icon {color: #4aa47b;}@media (max-width: 991px) {.pricing__popular .pricing__heading {margin-bottom: 10px;}.pricing__popular .pricing__content p {margin-bottom: 5px;}.pricing__popular .pricing__content-price {font-size: 42px;}.pricing__popular .pricing__action {position: relative;left: auto;bottom: auto;}.pricing__popular .btn {padding: 12.5px 25px;}.pricing__green.pp-fist, .pricing__green.pp-last {border-color: #4aa47b;}.pricing__red.pp-fist, .pricing__red.pp-last {border-color: #e24c43;}}.pricing__red {border-color: #e24c43;}.pricing__red .pricing__content-package, .pricing__red .pricing__icon {color: #e24c43;}.pricing__orange {border-color: #f26822;}.pricing__orange .pricing__content-package, .pricing__orange .pricing__icon {color: #f26822;}@media (max-width: 991px) {.pricing__orange.pp-fist, .pricing__orange.pp-last {border-color: #f26822;}.pricing__purple.pp-fist, .pricing__purple.pp-last {border-color: #403595;}}.pricing__purple {border-color: #403595;}.pricing__purple .pricing__icon {color: #4aa47b;}.pricing__purple .pricing__content-package {color: #403595;}.pricing__yellow {border-color: #f9d750;}.pricing__yellow .pricing__content-package, .pricing__yellow .pricing__icon {color: #f9d750;}@media (max-width: 991px) {.pricing__yellow.pp-fist, .pricing__yellow.pp-last {border-color: #f9d750;}}.pricing__grid {position: relative;background: #fff;border: 1px solid #d7deee;-webkit-box-shadow: 0 3px 40px 0 rgba(0, 0, 0, 0.15);box-shadow: 0 3px 40px 0 rgba(0, 0, 0, 0.15);margin-bottom: 40px;}@media (max-width: 991px) {.pricing__grid {margin-bottom: 30px;}}.pricing__grid-header {position: relative;padding: 40px 0 60px;margin-bottom: 10px;background-repeat: no-repeat;background-position: bottom center;background-size: 100% auto;background-color: #d7deee;}@media (min-width: 768px) and (max-width: 991px) {.pricing__grid-header {padding: 40px 0 130px;}}@media (max-width: 767px) {.pricing__grid-header {padding: 20px 0;}}.pricing__grid-header-title {font-family: Montserrat, sans-serif;}@media (max-width: 991px) {.pricing__grid-header-title {margin-bottom: 20px;}}.pricing__grid-header--icon {font-family: FontAwesome;line-height: 48px;display: inline-block;width: 90px;height: 90px;overflow: hidden;background: #2c2d2d;border-radius: 50%;color: #fff;padding: 20px 0;margin-bottom: 0;}@media (max-width: 991px) {.pricing__grid-header--icon {padding: 10px 0;line-height: 42px;width: 60px;height: 60px;}}.pricing__grid-price {font-family: 'Montserrat Light', sans-serif;position: relative;display: block;margin-bottom: 60px;}.package__content ul li, .pricing__grid-content, .tabs__navigation-item--link {font-family: Montserrat, sans-serif;}.pricing__grid-price--number {position: relative;font-size: 54px;padding-left: 10px;}@media (max-width: 991px) {.pricing__grid-price--number {font-size: 42px;}}.pricing__grid-price--currency {position: absolute;font-style: normal;font-size: 18px;top: 10px;left: 0;}.pricing__grid-content {text-align: left;display: inline-block;padding: 40px 0;margin: 0;}@media (max-width: 991px) {.pricing__grid-content {padding: 20px 0;}}.pricing__grid-content ul {margin: 0;padding: 0;}.pricing__grid-content ul li {list-style: none;margin: 0 0 10px;padding: 0;}@media (max-width: 991px) {.pricing__grid-content ul li {margin: 0 0 5px;}}.pricing__grid-content ul li .fa {margin-right: 5px;}.pricing__grid-content ul li.included .fa {color: #4aa47b;}.pricing__grid-content ul li.unincluded .fa {color: #e24c43;}.pricing__grid-action {position: relative;display: block;text-align: center;margin-bottom: -30px;}@media (max-width: 991px) {.pricing__grid-action {margin-bottom: -20px;}}.pricing__grid-blue .pricing__grid-header-title {color: #fff;}.pricing__grid-blue .pricing__grid-header--icon {background: #fff;color: #1e5ee5;}.pricing__grid-blue .pricing__grid-price, .pricing__grid-orange .pricing__grid-header-title {color: #fff;}.pricing__grid-orange .pricing__grid-header--icon {background: #fff;color: #f26822;}.pricing__grid-green .pricing__grid-header-title, .pricing__grid-orange .pricing__grid-price {color: #fff;}.pricing__grid-green .pricing__grid-header--icon {background: #fff;color: #4aa47b;}.pricing__grid-green .pricing__grid-price, .pricing__grid-yellow .pricing__grid-header-title {color: #fff;}.pricing__grid-yellow .pricing__grid-header--icon {background: #fff;color: #f9d750;}.pricing__grid-red .pricing__grid-header-title, .pricing__grid-yellow .pricing__grid-price {color: #fff;}.pricing__grid-red .pricing__grid-header--icon {background: #fff;color: #e24c43;}.pricing__grid-purple .pricing__grid-header-title, .pricing__grid-red .pricing__grid-price {color: #fff;}.pricing__grid-purple .pricing__grid-header--icon {background: #fff;color: #403595;}.pricing__grid-purple .pricing__grid-price {color: #fff;}.package {text-align: center;}.package__heading {display: inline-block;width: 252px;height: 162px;background-size: cover;font-size: 38px;margin-left: 20px;}@media (max-width: 767px) {.package__heading {margin-left: 0;}}.package__heading .fa {margin-top: 72px;margin-left: -10px;}.package__heading-green .fa {color: #4aa47b;}.package__heading-primary .fa {color: #1e5ee5;}.package__heading-red .fa {color: #e24c43;}.package__heading-yellow .fa {color: #f9d750;}.package__heading-purple .fa {color: #403595;}.package__heading-orange .fa {color: #f26822;}.package__content {display: block;margin-bottom: 20px;}@media (max-width: 991px) {.package__content {margin-bottom: 10px;}}.package__content ul {display: inline-block;text-align: left;margin: 0;padding: 0;}.package__content ul li {margin: 0 0 10px;padding: 0;list-style: none;}.package__content ul li .fa {margin-right: 5px;}.package__content ul li.included .fa {color: #4aa47b;}.package__content ul li.unincluded {color: #b7c3de;}.package__content ul li.unincluded .fa {color: #e24c43;}.package__action {display: block;margin-bottom: 20px;}.modal {background: rgba(0, 0, 0, 0.5);}.modal-content {border: none;border-radius: 0;}.modal-content .modal-body {padding: 30px;}.modal-content .modal-body .close:hover {color: #1e5ee5;}.tabs__navigation-item--link, .tabs__navigation-item--link:hover {color: #2c2d2d;text-decoration: none;}.tabs__navigation {float: left;width: 100%;position: relative;background: #fff;-webkit-box-shadow: 0 6px 40px 3px rgba(0, 0, 0, 0.06);box-shadow: 0 6px 40px 3px rgba(0, 0, 0, 0.06);margin-bottom: 80px;justify-content: center;margin: auto;}.tabs__navigation-item {float: left;}@media (max-width: 991px) {.tabs__navigation {margin-bottom: 40px;}.tabs__navigation-item {width: 100% !important;}}.tabs__navigation-item--link {display: block;text-align: center;border-bottom: 3px solid transparent;margin-bottom: 0;padding: 20px 60px;}.tabs__navigation-item--link:hover .tabs__navigation-item--icon {color: #4aa47b;}.tabs__navigation-item--link:focus {color: #1e5ee5;text-decoration: none;}.tabs__navigation-item--link.active {color: #2c2d2d;border-bottom: 3px solid #4aa47b;}@media (max-width: 991px) {.tabs__navigation-item--link {padding: 30px 10px;}.tabs__navigation-item--link.active {background: 0 0;color: #2c2d2d;border-bottom: 3px solid transparent;}}.tabs__navigation-item--link.active .tabs__navigation-item--icon {color: #4aa47b;}.tabs__navigation-item--icon {font-family: FontAwesome;margin-bottom: 10px;top: 12px;color: #1e5ee5;margin-left: 0.5rem;}.tabs__navigation-item--icon1 {font-family: FontAwesome;margin-bottom: 10px;top: 12px;color: #1e5ee5;}.tabs__navigation-item--icon2 {font-family: FontAwesome;margin-bottom: 10px;top: 12px;color: #1e5ee5;}.tabs__navigation-item--icon3 {font-family: FontAwesome;margin-bottom: 10px;top: 12px;color: #1e5ee5;margin-right: 0.5rem;}.tabs__navigation .owl-nav .owl-next.disabled, .tabs__navigation .owl-nav .owl-prev.disabled {display: none;}@media (max-width: 991px) {.tabs__navigation-item--link.active .tabs__navigation-item--icon {color: #1e5ee5;}.tabs__navigation-item--icon {margin-bottom: 0;}}.tabs__navigation .owl-nav .owl-prev {left: 20px;}.tabs__navigation .owl-nav .owl-next {left: auto;right: 20px;}.tabs__content {float: left;width: 100%;}.blockquote {position: relative;width: 90%;margin: 0 auto 25px;}.blockquote blockquote {text-align: center;position: relative;font-family: Montserrat, sans-serif;font-style: italic;padding: 0 30px;}@media (max-width: 991px) {.blockquote {margin-bottom: 10px;}.blockquote blockquote {padding: 0 15px;}}.blockquote blockquote::after, .blockquote blockquote::before {position: absolute;content: '\f10d';font-family: FontAwesome;color: #1e5ee5;top: -10px;left: 0;}.blockquote blockquote::after {content: '\f10e';color: #4aa47b;top: auto;left: auto;right: 10px;bottom: -30px;}.testimoni {display: block;position: relative;}.testimoni__card {position: relative;border: 1px solid #d7deee;background: #f1f5fd;padding: 40px 20px 98px;margin-bottom: 30px;}@media (max-width: 991px) {.testimoni__card {padding: 20px 10px;margin-bottom: 15px;}}.testimoni__card .blockquote {margin-bottom: 40px;}.testimoni__card--author {position: absolute;width: 100%;border-top: 1px solid #d7deee;padding: 20px 20px 20px 94px;left: 0;bottom: 0;}@media (max-width: 991px) {.testimoni__card .blockquote {margin-bottom: 20px;}.testimoni__card--author {position: relative;padding: 10px 10px 0 70px;}}.testimoni__card--author-avatar {position: absolute;width: 54px;height: 54px;overflow: hidden;border-radius: 50%;top: 20px;left: 20px;}@media (max-width: 991px) {.testimoni__card--author-avatar {width: 48px;height: 48px;top: 10px;left: 10px;}}.testimoni__card--author-avatar img {width: 100%;height: auto;}.testimoni__card--author-name {margin-bottom: 5px;}@media (max-width: 991px) {.testimoni__card--author-name {margin-bottom: 2px;}}.testimoni blockquote {position: relative;font-family: Montserrat, sans-serif;font-style: italic;padding: 0 30px;margin-bottom: 40px;}.testimoni blockquote::after, .testimoni blockquote::before {position: absolute;content: '\f10d';font-family: FontAwesome;color: #1e5ee5;top: 0;left: 0;}.testimoni blockquote::after {content: '\f10e';color: #4aa47b;top: 75%;left: auto;right: 10px;}.testimoni__author {font-family: Montserrat, sans-serif;}.testimoni__avatar {display: inline-block;width: 86px;height: 86px;border-radius: 50%;overflow: hidden;border: 8px solid #edf3ff;}@media (max-width: 991px) {.testimoni blockquote {margin-bottom: 20px;padding: 0 15px;}.testimoni__avatar {width: 53px;height: 53px;border: 4px solid #edf3ff;}}.testimoni__avatar:hover {border-color: #1e5ee5;}.testimoni__avatar-image {width: 100%;height: auto;}.article__card {display: block;position: relative;background: #fff;margin-bottom: 10px;-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;border: 1px solid #d7deee;}@media (max-width: 991px) {.article__card {margin-bottom: 5px;}}.article__card-label {font-family: 'Montserrat Bold', sans-serif;display: block;margin-bottom: 10px;color: #2c2d2d;}@media (max-width: 991px) {.article__card-label {margin-bottom: 5px;}}.article__card-author p, .article__card-title {margin-bottom: 0;}.article__card-label:active, .article__card-label:focus, .article__card-label:hover {color: #0f44b2;}.article__card-title a {color: #2c2d2d;}.article__card-title a:active, .article__card-title a:focus, .article__card-title a:hover {color: #0f44b2;}.article__card-comment {font-family: 'Montserrat Light', sans-serif;position: absolute;top: 20px;right: 20px;color: #d7deee;margin-top: -5px;}.article__card-comment:active, .article__card-comment:focus, .article__card-comment:hover {text-decoration: none;color: #1e5ee5;}.article__card-heading {position: relative;padding: 20px;}@media (max-width: 991px) {.article__card-comment {margin-top: -10px;}.article__card-heading {padding: 10px;}}.article__card-footer {position: relative;padding: 20px;}@media (max-width: 991px) {.article__card-footer {padding: 10px;}}.article__card-author {position: relative;display: block;padding-left: 55px;}.article__card-author .fa {display: none;margin-right: 5px;}.article__card-author a {color: #2c2d2d;}.article__card-author a:active, .article__card-author a:focus, .article__card-author a:hover {color: #0f44b2;}.article__card-avatar {position: absolute;width: 40px !important;height: 40px !important;left: 0;top: 5px;}.article__card-action {position: absolute;top: 30px;right: 20px;}@media (max-width: 991px) {.article__card-author {padding-left: 40px;font-size: 12px;line-height: 16px;}.article__card-author strong {display: none;}.article__card-author .fa {display: inline-block;}.article__card-avatar {width: 30px !important;height: 30px !important;margin-top: -5px;}.article__card-action {top: 15px;right: 10px;}}.article__card .btn-link:after {top: 1px;}.article__recent {position: relative;display: block;}.article__recent-content {position: absolute;background: #fff;width: 90%;right: 0;bottom: 0;}.article__recent-content .article__card-footer {border-top: 1px solid #d7deee;}.section__gray .article__card {border: none;}.owl-active-nav {padding: 0 60px;}.owl-nav {position: absolute;width: 100%;top: 50%;left: 0;margin-top: -5px;z-index: 99999;}.owl-nav .owl-next, .owl-nav .owl-prev {position: absolute;display: block;width: 30px;height: 10px;left: -60px;}@media (max-width: 991px) {.owl-active-nav {padding: 0 40px;}.owl-nav .owl-next, .owl-nav .owl-prev {left: -40px;width: 25px;}}.owl-nav .owl-next:before, .owl-nav .owl-prev:before {position: absolute;content: '\f177';font-family: FontAwesome;font-size: 28px;line-height: 28px;top: -10px;left: -1px;color: #d7deee;-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;}.owl-nav .owl-next span, .owl-nav .owl-prev span {display: none;}.owl-nav .owl-next:hover:before, .owl-nav .owl-prev:hover:before {color: #1e5ee5;}.owl-nav .owl-next {left: auto;right: -60px;}@media (max-width: 991px) {.owl-nav .owl-next:before, .owl-nav .owl-prev:before {font-size: 24px;line-height: 24px;top: -8px;}.owl-nav .owl-next {right: -40px;}}.owl-nav .owl-next:before {content: '\f178';}.owl-dots {position: relative;text-align: center;padding: 0;margin-top: 20px;}.owl-dots .owl-dot {position: relative;display: inline-block;width: 12px;height: 12px;border: 1px solid #bfcae2 !important;border-radius: 50%;padding: 0 !important;margin: 0 2px !important;}.owl-dots .owl-dot span {position: absolute;display: inline-block;background: #bfcae2 !important;border-radius: 50%;width: 6px;height: 6px;top: 2px;left: 2px;}.owl-dots .owl-dot.active, .owl-dots .owl-dot:hover {border: 1px solid #1e5ee5 !important;}.owl-dots .owl-dot:hover span {background: #1e5ee5 !important;}.owl-dots .owl-dot.active {width: 16px;height: 16px;top: 2px !important;}.owl-dots .owl-dot.active span {background: #1e5ee5 !important;width: 8px;height: 8px;top: 3px;left: 3px;}.section__purple .owl-nav .owl-next:hover:before, .section__purple .owl-nav .owl-prev:hover:before {color: #f26822;}.owl-active-nav {position: relative;}.owl-active-nav--left .owl-nav {position: absolute;width: 110px;display: block;top: -55px;left: auto;right: 80px;background: red;}@media (min-width: 1200px) and (max-width: 1399px) {.owl-active-nav--left .owl-nav {right: 40px;}}@media (max-width: 991px) {.owl-active-nav--left .owl-nav {right: 15px;width: 85px;}}.owl-active-nav--left .owl-nav .owl-next, .owl-active-nav--left .owl-nav .owl-prev {position: absolute;display: block;width: 50px;height: 32px;left: 0;}.owl-active-nav--left .owl-nav .owl-next:before, .owl-active-nav--left .owl-nav .owl-prev:before {position: absolute;content: '\f177';font-family: FontAwesome;font-size: 28px;line-height: 28px;top: 0;left: 0;color: #d7deee;-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;border: 1px solid #d7deee;padding: 0 10px;}@media (max-width: 991px) {.owl-active-nav--left .owl-nav .owl-next, .owl-active-nav--left .owl-nav .owl-prev {width: 40px;height: 20px;}.owl-active-nav--left .owl-nav .owl-next:before, .owl-active-nav--left .owl-nav .owl-prev:before {font-size: 18px;line-height: 18px;}}.owl-active-nav--left .owl-nav .owl-next span, .owl-active-nav--left .owl-nav .owl-prev span {display: none;}.owl-active-nav--left .owl-nav .owl-next:hover:before, .owl-active-nav--left .owl-nav .owl-prev:hover:before {color: #1e5ee5;border: 1px solid #1e5ee5;}.owl-active-nav--left .owl-nav .owl-next {left: auto;right: 0;}.owl-active-nav--left .owl-nav .owl-next:before {content: '\f178';}.client {position: relative;display: block;text-align: center;}.client img {width: 145px !important;height: auto;margin: 0 auto;}.client_logo {-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;}.client_logo-hover {position: absolute;-webkit-transition: all 0.3s ease-in-out !important;transition: all 0.3s ease-in-out !important;opacity: 0;left: 50%;margin-left: -72.5px !important;}.client:hover .client_logo {opacity: 0;}.client:hover .client_logo-hover {opacity: 1;}.plyr__control--overlaid {background: #f26822;padding: 30px;-webkit-box-shadow: 0 5px 28px -5px rgba(190, 151, 151, 0.5);box-shadow: 0 5px 28px -5px rgba(190, 151, 151, 0.5);}@media (max-width: 991px) {.plyr__control--overlaid {padding: 15px;}}.plyr__control--overlaid:focus, .plyr__control--overlaid:hover {background: #c74e11;border: none;outline: 0;}.plyr--audio .plyr__control.plyr__tab-focus, .plyr--audio .plyr__control:hover, .plyr--audio .plyr__control[aria-expanded='true'], .plyr--video .plyr__control.plyr__tab-focus, .plyr--video .plyr__control:hover, .plyr--video .plyr__control[aria-expanded='true'] {background: #c74e11;-webkit-box-shadow: none;box-shadow: none;outline: 0;}.plyr--video .plyr__controls {background: 0 0;}.plyr--stopped .plyr__controls {opacity: 0;}.plyr--video, .plyr__video-wrapper {background: 0 0 !important;}.plyr__poster {background-color: transparent !important;background-position: top center;background-size: cover;}.video__background {position: relative;display: block;}.video__background-wrapper {position: absolute;top: 5%;margin-top: 17px;}.features, .features__wrapper {position: relative;display: block;}.video__background-image {width: 100%;}.features {margin-bottom: 30px;}.features__card {-webkit-box-shadow: 0 5px 30px 6px rgba(0, 0, 0, 0.06);box-shadow: 0 5px 30px 6px rgba(0, 0, 0, 0.06);padding: 40px 30px;border-radius: 10px;}.features__wrapper .item {padding: 30px 0;}.features__wrapper:after, .features__wrapper:before {content: '';position: absolute;top: 0;left: 0;width: 200px;height: 100%;z-index: 9;background: #fff;background: -webkit-gradient( linear, left top, right top, from(white), to(rgba(255, 255, 255, 0)) );background: linear-gradient(to right, #fff 0, rgba(255, 255, 255, 0) 100%);}
