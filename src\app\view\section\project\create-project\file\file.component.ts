import { Component, ElementRef, ViewChild } from '@angular/core';

@Component({
  selector: 'file',
  templateUrl: './file.component.html',
  styleUrls: ['./file.component.css'],
})
export class FileComponent {
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef;

  public file: any;

  constructor() {}

  onFileSelect() {
    const fileInput = this.fileInput.nativeElement;
    fileInput.onchange = () => {
      for (let index = 0; index < fileInput.files.length; index++) {
        const file = fileInput.files[index];
        this.file = file;
      }
    };
    fileInput.click();
  }
}
