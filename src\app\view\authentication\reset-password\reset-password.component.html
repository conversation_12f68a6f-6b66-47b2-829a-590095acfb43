<form [formGroup]="resetForm" class="signup-card-form">
  <mat-card class="signup-card">
    <mat-card-content>
      <div class="user-container">
        <mat-icon class="user-icon">lock_open</mat-icon>
      </div>
      <div class="text-uppercase text-center mt-4 font-weight-bolder fs-3">Account Recovery</div>
      <div class="mt-4">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label class="field-content">Enter your registered email</mat-label>
          <mat-icon matPrefix class="field-icon">mail</mat-icon>
          <input
            matInput
            placeholder="Enter your registered email"
            name="email"
            formControlName="email" />
          <mat-error *ngIf="hasError('email', 'required')">Email is required</mat-error>
          <mat-error *ngIf="hasError('email', 'pattern')">Invalid Email format</mat-error>
        </mat-form-field>
      </div>
    </mat-card-content>
    <div class="signup-btn-container">
      <button
        mat-button
        class="w-25 text-uppercase"
        (click)="submit()"
        [disabled]="!resetForm.valid || Isworking">
        <span *ngIf="!Isworking">Submit</span>
        <span *ngIf="Isworking">
          <div class="spinner-border" role="status" *ngIf="Isworking"></div>
        </span>
      </button>
    </div>
    <div class="action-container">
      Back to&nbsp;
      <a class="signup-text" [routerLink]="['/auth', 'login']"><b>Login</b></a>
    </div>
  </mat-card>
</form>
