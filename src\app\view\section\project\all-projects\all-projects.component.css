.project-container {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  position: relative;
  display: flow-root;
  background-color: var(--white);
}
#mapdashboard {
  height: 85vh;
  margin-left: 1rem;
  margin-right: 1rem;
}
@media (max-width: 767px) {
  #mapdashboard {
    height: calc(100vh - 120px);
    margin: 60px var(--spacing-sm) var(--spacing-sm);
  }
  .project-container {
    margin: var(--spacing-sm);
  }
  .project-container .image-card {
    height: 120px;
  }
  .project-container .details-card {
    margin: var(--spacing-sm);
  }
  .btn-icons-container {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: var(--spacing-sm);
    justify-content: center;
  }
  .mat-form-field {
    width: 100%;
    margin-top: var(--spacing-sm);
  }
}
.text {
  cursor: pointer;
  border-radius: 5px;
  background-color: #dbdbdb;
  color: gray;
  font-size: 76px;
  padding: 16px 32px;
}
.middle {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}
.project-container .image-card {
  position: relative;
  width: 100%;
  height: 150px;
}
.project-container .details-card {
  margin: 15px;
}
.project-title {
  font-weight: 600;
  letter-spacing: 1px;
  font-size: 15px;
  padding: 5px 0;
  text-transform: capitalize;
  overflow: hidden;
  text-overflow: ellipsis;
  transition:
    color 0.3s,
    font-size 0.3s,
    cursor 0.3s;
  color: var(--primary);
}
.details-card .project-title:hover {
  color: gray;
  font-size: 16px;
  cursor: pointer;
}
.details-card .project-date {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.details-card .project-capacity {
  display: flex;
  justify-content: space-between;
}
.details-card .project-capacity p {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}
.details-card .project-state {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: capitalize;
  padding: 5px 0;
}
.ForStatusSelect {
  position: fixed;
  right: -5px;
  top: 79px;
  width: 180px;
  position: absolute;
  z-index: 1000;
  align-content: left;
}
.image-card .btn-container {
  position: absolute;
  right: 20px;
  bottom: -10px;
}
.image-card .project-img {
  width: 100%;
  height: inherit;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.project-container .action-container {
  padding: 5px 0;
  float: right;
  position: relative;
  top: -15px;
}
button:focus {
  outline: none !important;
}
::ng-deep .mat-tab-label .mat-tab-label-content {
  font-size: 14px !important;
  font-weight: 600;
  color: #1b1b1b !important;
}
.status-indication {
  display: inline-flex;
  align-items: center;
}
.status-indication .indication-bar {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  padding-left: 5px;
}
.status-indication .indication-text {
  font-size: 13px;
  font-weight: 500;
}
.status-indication2 {
  display: inline-flex;
}
.status-indication2 .indication-bar2 {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  padding-left: 5px;
}
.status-indication2 .indication-text2 {
  font-size: 13px;
  font-weight: 500;
}
.status-indication1 {
  display: inline-flex;
}
.status-indication1 .indication-bar1 {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  padding-left: 5px;
}
.status-indication1 .indication-text1 {
  font-size: 13px;
  font-weight: 500;
  margin-left: 10px;
}
.custom-btn1 {
  background-color: var(--primary) !important;
}
.mat-form-field {
  margin-top: 24px;
}
.btn-icons-container {
  position: absolute;
  bottom: -15px;
  right: 10px;
  display: flex;
  justify-content: space-evenly;
}
.row1 {
  margin-left: -15px;
}
.col-sm-5 ng-star-inserted {
  margin-left: 20px;
  margin-right: 130px;
}
.cardd {
  width: 1155px;
  height: 700px;
}
.mat-button {
  background-color: var(--primary);
  border: none;
  color: blue;
  padding: 2px 12px;
  font-size: 13px;
  text-align: center;
  margin: 4px 2px;
  opacity: 1;
  transition: 0.3s;
}
.mat-button:hover {
  opacity: 1;
  background-color: var(--primary);
  border: none;
  color: var(--white);
  padding: 2px 15px;
  font-size: 13px;
  text-align: center;
  margin: 4px 2px;
  opacity: 0.6;
  transition: 0.3s;
}
.no-project {
  text-align: center;
  margin: 1.5rem;
  font-weight: 600;
  font-size: 18px;
}
.search-box-top {
  display: flex;
  justify-content: center;
  align-items: center;
  top: 5px;
  right: 60px;
  z-index: 1200;
  position: fixed;
}
.search-box-icon2 {
  position: fixed;
  right: 24%;
  top: 64px;
}
.search-box-top2 {
  position: fixed;
  top: 51px;
  height: 46px;
  width: 224px;
  border-radius: 9px;
  right: 23%;
  border-bottom: solid 5px darkcyan;
}
@media (min-width: 613px) and (max-width: 829px) {
  .search-box-top2 {
    right: 30%;
  }
  .search-box-icon2 {
    right: 31%;
  }
}
@media (min-width: 829px) {
  .grid-view4 {
    display: none;
  }
  .grid-view5 {
    display: none;
  }
}
@media (max-width: 829px) {
  .search-box-top {
    display: none;
  }
}
.myInput:focus {
  outline: none;
}
.filter2-box-icon {
  position: absolute;
  right: 300px;
}
@media (max-width: 700px) {
  .filter2-box-icon {
    display: none;
  }
}
.filter2-box-icon-mobile {
  position: fixed;
  z-index: 2000;
  display: flex;
  top: 20px;
  left: 60%;
  transform: translateX(-50%);
}
.modal-class {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}
.project-category {
  font-weight: 500;
  color: black;
  margin: 5px 0;
}
.myInput:focus {
  outline: none;
}
.button-view {
  top: 15px;
  left: 100px;
  z-index: 1200;
  position: fixed;
  display: flex;
  gap: 10px;
}
@media (max-width: 700px) {
  .button-view {
    top: 8px;
    left: 80px;
    z-index: 1200;
    position: fixed;
    display: flex;
    gap: 10px;
  }
}
@media (max-width: 700px) {
  #isVisibleMobile {
    display: none;
  }
}
.mat-mdc-row:hover .mat-mdc-cell {
  border-bottom: 1px solid black;
  border-top: 1px solid black;
  cursor: pointer;
}
::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}
.signup-card {
  position: absolute;
  margin-top: 80px !important;
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 50%;
  height: fit-content;
  border-radius: 5px;
  box-shadow: 6px 6px 33px var(--shadow);
  padding: 1rem;
}
@media (max-width: 960px) {
  .signup-card {
    width: 80%;
  }
}
@media (max-width: 600px) {
  .signup-card {
    width: 100%;
  }
}
