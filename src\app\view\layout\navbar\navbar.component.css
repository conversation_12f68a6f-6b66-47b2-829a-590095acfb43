::ng-deep .mat-toolbar-row,
.mat-toolbar-single-row {
  position: fixed;
  padding: 1.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: var(--z-navbar);
  background-color: var(--white);
  width: 100%;
  top: 0;
  left: 0;
}
.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}
.logo {
  width: 12.5rem;
  height: 3.125rem;
  padding: 4px;
}
.brand-text {
  font-size: var(--font-2xl);
  font-weight: 600;
  color: var(--primary);
  letter-spacing: 1px;
}
.menu-section {
  position: absolute;
  right: 100px;
  top: 8px;
}
.mobile-menu-toggle {
  display: none;
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  z-index: var(--z-mobile-toggle);
  padding: 8px;
  width: 40px;
  height: 40px;
}
.mobile-menu-toggle .hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.3s ease-in-out;
}
.mobile-menu-toggle .hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--primary);
  border-radius: 2px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}
.mobile-menu-toggle .hamburger span:nth-child(1) {
  top: 0;
}
.mobile-menu-toggle .hamburger span:nth-child(2) {
  top: 8px;
}
.mobile-menu-toggle .hamburger span:nth-child(3) {
  top: 16px;
}
.mobile-menu-toggle.active .hamburger span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}
.mobile-menu-toggle.active .hamburger span:nth-child(2) {
  opacity: 0;
  left: -60px;
}
.mobile-menu-toggle.active .hamburger span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}
@media (max-width: 767px) {
  ::ng-deep .mat-toolbar-row,
  .mat-toolbar-single-row {
    padding: var(--spacing-md);
    height: 56px;
  }
  .mobile-menu-toggle {
    display: block;
  }
  .menu-section {
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
  }
  .logo {
    width: 8rem;
    height: 2rem;
    margin-left: 50px;
  }
  .brand-text {
    margin-left: 50px;
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--primary);
  }
  .mobile-only {
    display: flex;
  }
  .desktop-only {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  ::ng-deep .mat-toolbar-row,
  .mat-toolbar-single-row {
    padding: var(--spacing-lg);
  }
  .logo {
    width: 10rem;
    height: 2.5rem;
  }
  .menu-section {
    right: 80px;
  }
}
