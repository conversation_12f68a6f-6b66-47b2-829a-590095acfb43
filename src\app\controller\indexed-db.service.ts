import { Injectable } from '@angular/core';
import 'idb';
import { DataService } from './data.service';

@Injectable({
  providedIn: 'root',
})
export class IndexedDbService {
  private readonly DB_NAME = 'carnot-datasee-ai';
  private readonly STORE_NAME = 'store';
  private db: IDBDatabase;
  private dbInitialized: boolean = false;

  constructor(private dataService: DataService) {
    this.initializeDb();
  }

  async initializeDb(): Promise<void> {
    if (this.dbInitialized) {
      return;
    }
    await new Promise<void>((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, 1);
      request.onupgradeneeded = event => {
        const db = (event.target as IDBOpenDBRequest).result;
        const objectStore = db.createObjectStore(this.STORE_NAME, { keyPath: 'key' });
        objectStore.createIndex('key', 'key', { unique: true });
      };
      request.onsuccess = event => {
        this.db = (event.target as IDBOpenDBRequest).result;
        this.dbInitialized = true;
        resolve();
      };
      request.onerror = event => {
        console.error('IndexedDB initialization error:', event);
        reject(event);
      };
      request.onblocked = () => {
        console.error('Database initialization blocked.');
      };
    });
  }

  async getItem(key: string): Promise<any> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.STORE_NAME], 'readonly');
      const objectStore = transaction.objectStore(this.STORE_NAME);
      const request = objectStore.get(key);
      request.onsuccess = () => {
        try {
          if (request.result) {
            const encryptedData = request.result.data;
            const decryptedData = this.dataService.decryptData(encryptedData);
            resolve(decryptedData);
          } else {
            resolve(null);
          }
        } catch (error) {
          console.error('Error during data decryption:', error);
          reject(error);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  async setItem(key: string, value: any): Promise<void> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      try {
        const data = this.dataService.encryptData(value);
        const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
        const objectStore = transaction.objectStore(this.STORE_NAME);
        const request = objectStore.put({ key, data });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      } catch (error) {
        console.error('Error during data encryption or storage:', error);
        reject(error);
      }
    });
  }

  async removeItem(key: string): Promise<void> {
    await this.initializeDb();
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.STORE_NAME], 'readwrite');
      const objectStore = transaction.objectStore(this.STORE_NAME);
      const request = objectStore.delete(key);
      request.onsuccess = () => {
        resolve();
      };
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  destroyDb(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close();
      }
      const request = indexedDB.deleteDatabase(this.DB_NAME);
      request.onsuccess = () => {
        this.dbInitialized = false;
        resolve();
      };
      request.onerror = event => {
        console.error('Error deleting database:', event);
        reject(event);
      };
      request.onblocked = () => {
        console.error('Database deletion blocked. Please close other tabs or reload this page.');
      };
    });
  }
}
