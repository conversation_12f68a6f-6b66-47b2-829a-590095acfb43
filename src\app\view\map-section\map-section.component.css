.bottom-container {
  right: 60px;
  display: flex;
  position: absolute;
  bottom: 0;
  padding: 10px;
}
.map-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
#elevation-div {
  background-color: red;
}
#share {
  position: absolute;
  right: 10px;
  z-index: var(--z-navbar);
}
::ng-deep .mat-select-panel-wrap {
  flex-basis: 105%;
  position: absolute;
  top: 42px;
  left: 11px;
}
::ng-deep mat-button {
  border: 0;
  border-color: transparent;
}
.optionheight {
  background-color: violet;
  border-radius: 10px;
}
.exam {
  position: fixed;
  right: 150px;
  top: 5px;
  font-size: 26px;
  font-weight: 700;
  margin: 5px;
}
.exam2 {
  position: fixed;
  right: 60px;
  top: 5px;
  background: white;
  padding: 5px;
  border-radius: 10px;
  border: solid 2px var(--primary);
  width: 150px;
}
@media (min-width: 500px) {
  #infs {
    visibility: hidden;
  }
}
@media (max-width: 500px) {
  .exam2 {
    width: 40%;
    margin-right: 20px;
  }
}
.location {
  position: fixed;
  right: 80px;
  top: 60px;
  width: 135px;
  z-index: var(--z-navbar);
  align-content: right;
}
.satellite_view {
  position: fixed;
  top: 360px;
  right: 10px;
  z-index: 1000;
}
.dtm_view {
  position: fixed;
  top: 280px;
  right: 145px;
  height: 119px;
  font-size: 14px;
  z-index: 1000;
  align-content: right;
  z-index: 1000;
}
.slope_view {
  position: fixed;
  top: 280px;
  right: 70px;
  height: 225px;
  font-size: 14px;
  z-index: 1000;
  align-content: right;
}
.header {
  right: 60px;
  top: 5px;
  padding: 0;
  width: 250px;
  position: absolute;
}
#marquee-cont {
  font-size: 25px;
  font-weight: 600;
}
.bottom-container .drawer {
  position: relative;
  margin: auto;
  width: 50%;
  margin-bottom: 0;
}
@media (max-width: 700px) {
  .bottom-container .drawer {
    position: relative;
    margin: auto;
    width: 150px;
    margin-right: 47px;
    margin-bottom: 5px;
  }
}
.details-section {
  cursor: pointer;
  margin: -10px;
  .mat-mdc-checkbox label {
    margin: 0;
    font-weight: 600;
    text-transform: uppercase;
  }
}
.btn {
  text-align: left;
}
.menu-header {
  color: #1b1b1b;
  font-size: 16px;
  font-weight: 500;
}
.stats-card {
  position: fixed;
  top: 10px;
  left: 65px;
  background-color: rgba(240, 248, 255, 0.123);
  padding: 20px;
  width: 20%;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  z-index: 1000;
}
.stats-card .details {
  margin: 10px 0;
  display: flex;
}
.details .key {
  width: 60%;
  font-weight: 500;
}
.details .value {
  width: 40%;
}
@media (max-width: 767px) {
  .bottom-container {
    display: block !important;
    right: 0;
    bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  .stats-card {
    position: fixed;
    top: 60px;
    left: var(--spacing-sm);
    width: calc(100% - 2 * var(--spacing-sm));
    max-width: 300px;
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
  }
  .stats-card .details {
    margin: var(--spacing-xs) 0;
    font-size: var(--font-sm);
  }
  .details .key {
    font-size: var(--font-xs);
  }
  .details .value {
    font-size: var(--font-xs);
  }
  .mapslayout {
    top: 120px;
    right: var(--spacing-sm);
    flex-direction: column;
    max-width: 200px;
  }
  .weatherlayout {
    top: 250px;
    right: var(--spacing-sm);
    max-width: 280px;
  }
  .location {
    right: var(--spacing-sm);
    top: 70px;
    width: 120px;
  }
  .exam2 {
    width: 60%;
    right: var(--spacing-sm);
    margin-right: 0;
  }
  .popup {
    width: 90%;
    right: var(--spacing-sm);
    margin-top: 60px;
  }
  #infsLegend {
    top: 380px;
    right: var(--spacing-sm);
    width: 44px;
    height: 44px;
  }
  #stacks {
    top: 320px;
    right: var(--spacing-sm);
    width: 44px;
    height: 44px;
  }
}
.mat-icon-button {
  width: 31px;
  height: 31px;
}
.example-additional-selection {
  opacity: 0.75;
  font-size: 0.75em;
}
select {
  border-radius: 5px;
  background-color: transparent;
  width: 95px;
  border-color: transparent;
}
#infsLegend {
  top: 430px;
  right: 10px;
  padding: 5px;
  background-color: var(--white);
  width: 40px;
  height: 40px;
  position: fixed;
  z-index: 1000;
  color: black;
  cursor: pointer;
  font-size: 25px;
  border-radius: 8px;
  border: solid 2px var(--primary);
}
@media (min-width: 500px) {
  #infs {
    visibility: hidden;
  }
}
::ng-deep .mat-select-value-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#stacks {
  top: 315px;
  right: 10px;
  padding: 5px;
  background-color: var(--white);
  width: 40px;
  height: 40px;
  position: fixed;
  z-index: 1000;
  color: black;
  cursor: pointer;
  font-size: 25px;
  border-radius: 8px;
  border: solid 2px var(--primary);
}
.img-items {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.img-dim {
  height: 50px;
  width: 50px;
  border-radius: 10px;
  border-style: solid;
  border-width: 3px;
}
.mapslayout {
  top: 155px;
  right: 55px;
  height: fit-content;
  position: fixed;
  z-index: var(--z-mobile-toggle);
  background-color: var(--white);
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  border-radius: 4px;
  border: solid 2px var(--primary);
}
@media (min-width: 320px) and (max-width: 750px) {
  .mapslayout {
    flex-direction: column;
  }
}
.weatherlayout {
  top: 300px;
  right: 55px;
  height: fit-content;
  position: fixed;
  z-index: 1200;
  background-color: var(--white);
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  border-radius: 4px;
  border: solid 2px var(--primary);
}
.drone-data-thermal {
  display: flex;
  flex-direction: column;
  border-right: solid 1px grey;
}
.base-map {
  display: flex;
  flex-direction: column;
  height: fit-content;
}
.base-map-img {
  display: flex;
  justify-content: space-evenly;
  z-index: 1000;
  gap: 10px;
  padding: 5px;
}
.span-font {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  margin: 5px 0;
}
hr {
  border: none;
  height: 1px;
  background-color: black;
}
.example-section {
  margin: 12px 0;
}
.example-margin {
  margin: 0 12px;
  background-color: var(--white);
}
ul {
  list-style-type: none;
  margin-top: 4px;
}
.mat-card {
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  position: relative;
  padding: 5px 10px;
  border-radius: 4px;
}
body {
  margin: 0;
  padding: 0;
  background-color: var(--white);
  overflow: hidden;
}
.box {
  position: absolute;
  top: 10%;
  left: 30%;
  bottom: -10px;
  transform: translate(-50%, -50%);
}
.box select {
  background-color: var(--white);
  color: black;
  padding: 5px;
  width: 145px;
  border: none;
  font-size: 20px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  appearance: button;
  outline: none;
  cursor: pointer;
}
.box::before {
  content: "\f13a";
  position: absolute;
  top: 0;
  right: 0;
  width: 20%;
  height: 100%;
  text-align: center;
  font-size: 28px;
  line-height: 45px;
  color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.1);
  pointer-events: none;
}
.box:hover::before {
  color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.2);
}
.box select option {
  padding: 12px 16px;
  cursor: pointer;
  color: grey;
}
select {
  border-radius: 5px;
  background-color: var(--white);
  width: 135px;
  border-color: black;
}
.special {
  font-weight: bold !important;
  color: #000 !important;
  background: var(--white) !important;
  text-transform: uppercase;
  border-radius: 20px !important;
}
.popup {
  margin-top: 50px;
  width: 25%;
  position: fixed;
  top: 0;
  right: 70px;
  z-index: 1000;
  border-radius: 10px;
}
.popup-card-error {
  margin-top: 50px;
  width: 25%;
  position: fixed;
  top: 0;
  right: 70px;
  z-index: 1000;
  border-radius: 10px;
}
@media (max-width: 420px) {
  .popup-card-error {
    margin-top: 50px;
    width: 80%;
    position: fixed;
    bottom: 0;
    right: 10px;
    z-index: 1000;
    height: 350px;
    border-radius: 10px;
    overflow: auto;
  }
}
@media (min-width: 420px) and (max-width: 800px) {
  .popup-card-error {
    width: 60%;
    position: fixed;
    bottom: 0;
    right: 10px;
    z-index: 1000;
    height: 350px;
    border-radius: 10px;
    overflow: auto;
  }
}
.item-value {
  word-break: break-all;
}
.dtmLegend {
  position: absolute;
  top: 400px;
  z-index: 1000;
  width: 100px;
  height: 240px;
  right: 40px;
  padding: 20px;
  border-radius: 10px;
  border-left: solid 4px var(--primary);
  background-color: var(--white);
  display: flex;
  cursor: move;
}
.dtmLegend.active {
  cursor: move;
  user-select: none;
}
.dtmgradient {
  z-index: 1000;
  width: 13px;
  height: 200px;
  background-image: linear-gradient(
    to bottom,
    red,
    rgb(255, 119, 0),
    yellow,
    rgb(28, 177, 28),
    skyblue,
    blue
  );
  border: solid 1px black;
  border-radius: 2px;
}
.dtmscale {
  z-index: 1000;
  width: 40px;
  height: 200px;
}
.dtmscale1 {
  z-index: 1000;
  width: 12px;
  height: 200px;
}
.refname {
  border-bottom: solid 1.5px black;
  height: 28px;
}
.refnaming {
  text-align: center;
  font-size: 12px;
  width: 40px;
  height: 26px;
  font-weight: 600;
}
.part2 {
  display: flex;
  width: 60px;
}
.slopeLegend {
  cursor: move;
  position: fixed;
  top: 348px;
  right: 150px;
  height: 290px;
  width: 120px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  z-index: 1000;
  border-left: solid 4px var(--primary);
  background-color: var(--white);
  border-radius: 10px;
  padding: 10px;
}
.slopeLegend.active {
  cursor: move;
  user-select: none;
}
.slopebox {
  width: 20px;
  height: 200px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  z-index: 1000;
}
.slopebox2 {
  width: 60px;
  height: 200px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.8px;
  z-index: 1000;
}
.refslope {
  width: 16px;
  height: 16px;
  background-color: red;
  border-radius: 2px;
  border: solid 1px black;
}
.legendDefects {
  cursor: move;
  margin-bottom: 10px;
  position: fixed;
  top: 250px;
  left: 80px;
  z-index: 1300;
  font-weight: 600;
  border-right: solid 4px var(--primary);
}
.refslopenaming {
  width: 80px;
  height: 24px;
  font-weight: 600;
}
.heading {
  height: 25px;
  text-align: center;
  font-weight: 500;
  font-size: small;
}
.wrapper {
  width: 200px;
  height: 80px;
  border: 2px solid var(--primary);
  padding: 10px;
  border-radius: 10px;
  z-index: 1000 !important;
  background-color: var(--white);
  position: fixed;
  top: 200px;
  right: 55px;
}
.wrapper input {
  box-shadow: 0 0 0 2px #ededed;
  cursor: pointer;
}
.opacityhead {
  font-weight: 600;
  font-size: 15px;
  place-items: center;
}
.opacity-head {
  display: flex;
  width: 180px;
  justify-content: space-between;
  margin-bottom: 8px;
}
.opacity-cad {
  font-size: small;
  font-weight: 600;
  width: fit-content;
  background-color: var(--primary);
  color: var(--white);
  padding: 3px 5px;
  border-radius: 4px;
  margin: auto;
}
#myRange {
  appearance: none;
  width: 100%;
  height: 3px;
  outline: none;
  border-radius: 3px;
}
#myRange::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 50%;
  z-index: var(--z-mobile-toggle);
  border: solid 3px var(--primary);
  background-color: var(--white);
}
#selector {
  height: 25px;
  width: 25px;
  position: absolute;
  transform: translateX(-50%) rotate(45deg);
  left: 50%;
  top: 23px;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-left-radius: 50%;
  background-color: var(--primary);
}
.custom-tooltip {
  font-size: 16px;
  background-color: var(--white);
  border: 1px solid #333333;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}
.tool-sidebar {
  width: 50px;
  height: 100%;
  background-color: var(--primary);
  color: var(--white);
  position: fixed;
  right: 0;
  top: 0;
  transition: transform 0.3s ease;
  transform: translateX(100%);
  &.open {
    transform: translateX(0);
  }
  mat-icon {
    cursor: pointer;
  }
  mat-nav-list {
    mat-list-item {
      display: flex;
      justify-content: center;
      cursor: pointer;
      padding: 10px;
      border-bottom: 1px solid gray;
      mat-icon {
        font-size: 24px;
        color: var(--white);
      }
    }
  }
}
.toggle-button {
  position: absolute;
  bottom: 15px;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  display: flex;
  color: var(--primary);
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  border: 2px solid;
  transition: all 0.3s ease;
}
.toggle-right.off {
  right: 5px;
}
.toggle-right {
  right: 35px;
}
.toggle-left.off {
  left: 5px;
}
.toggle-left {
  left: 45px;
}
@media print {
  @page {
    size: landscape;
  }
  body * {
    visibility: hidden;
  }
  #map,
  #map * {
    visibility: visible;
  }
  #map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
.background .opacity {
  opacity: 10 !important;
}
@media (max-width: 480px) {
  .exam {
    position: fixed;
    right: 5px;
    top: 5px;
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 2px !important;
    width: calc(100% - 60px);
    text-align: center;
    z-index: 1100;
  }
  .exam2 {
    position: fixed !important;
    right: 50px !important;
    top: 35px !important;
    background: white;
    padding: 3px !important;
    border-radius: 8px;
    border: solid 2px var(--primary);
    width: calc(100% - 60px) !important;
    max-width: 150px;
    z-index: 1100;
  }
  .mapslayout {
    top: 70px !important;
    right: 5px !important;
    left: 5px;
    width: calc(100% - 10px) !important;
    max-height: 40vh;
    overflow-y: auto;
    padding: 8px;
    flex-direction: column !important;
    z-index: 1200;
  }
  .base-map-img {
    flex-wrap: wrap;
    gap: 5px !important;
    padding: 3px !important;
  }
  .img-items {
    flex: 1;
    min-width: 60px;
  }
  .img-dim {
    height: 35px !important;
    width: 35px !important;
    border-width: 2px !important;
  }
  .span-font {
    font-size: 0.7rem !important;
    margin: 2px 0 !important;
  }
  .toggle-button {
    height: 30px !important;
    width: 30px !important;
    bottom: 10px !important;
    z-index: 1600;
  }
  .toggle-right {
    right: 5px !important;
  }
  .toggle-right.off {
    right: 5px !important;
  }
  .toggle-left {
    left: 5px !important;
  }
  .toggle-left.off {
    left: 5px !important;
  }
  .tool-sidebar {
    width: 45px !important;
    transform: translateX(100%);
    z-index: 1000;
  }
  .tool-sidebar.open {
    transform: translateX(0);
  }
  .tool-sidebar mat-nav-list mat-list-item {
    padding: 8px !important;
    min-height: 40px;
  }
  .tool-sidebar mat-nav-list mat-list-item mat-icon {
    font-size: 20px !important;
  }
  .popup-card-error {
    margin-top: 0 !important;
    width: calc(100% - 10px) !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 5px !important;
    right: 5px !important;
    top: auto !important;
    max-height: 50vh;
    border-radius: 10px 10px 0 0 !important;
    overflow-y: auto;
    z-index: 1400;
  }
  .weatherlayout {
    top: auto !important;
    bottom: 60px !important;
    right: 5px !important;
    left: 5px;
    width: calc(100% - 10px) !important;
    max-height: 30vh;
    overflow-y: auto;
    z-index: 1200;
  }
  .wrapper {
    width: calc(100% - 10px) !important;
    right: 5px !important;
    left: 5px;
    top: auto !important;
    bottom: 100px !important;
    z-index: 1300;
  }
  .legendDefects {
    top: auto !important;
    bottom: 60px !important;
    left: 5px !important;
    right: 5px;
    width: calc(100% - 10px) !important;
    max-height: 30vh;
    overflow-y: auto;
    z-index: 1300;
  }
  .dtmLegend {
    top: auto !important;
    bottom: 60px !important;
    right: 5px !important;
    width: 80px !important;
    height: 180px !important;
    padding: 10px !important;
    z-index: 1300;
  }
  .dtmgradient {
    height: 150px !important;
  }
  .dtmscale {
    height: 150px !important;
  }
  .slopeLegend {
    top: auto !important;
    bottom: 60px !important;
    right: 5px !important;
    width: 100px !important;
    height: 200px !important;
    padding: 8px !important;
    z-index: 1300;
  }
  .stats-card {
    top: 70px !important;
    left: 5px !important;
    right: 5px;
    width: calc(100% - 10px) !important;
    padding: 10px !important;
    max-height: 25vh;
    overflow-y: auto;
    z-index: 1100;
  }
  .bottom-container {
    right: 5px !important;
    left: 5px;
    width: calc(100% - 10px) !important;
    bottom: 5px !important;
    padding: 5px !important;
    z-index: 1100;
  }
  .location,
  .satellite_view,
  .dtm_view,
  .slope_view {
    display: none !important;
  }
  div[style*="margin-left:4rem"] {
    margin-left: 5px !important;
    width: calc(100% - 10px) !important;
    left: 5px !important;
    right: 5px !important;
  }
  mat-card[style*="width:25%"] {
    width: calc(100% - 10px) !important;
    right: 5px !important;
    left: 5px !important;
  }
  mat-card[style*="width:16%"] {
    width: calc(100% - 10px) !important;
    right: 5px !important;
    left: 5px !important;
    margin-left: 5px !important;
  }
  mat-card[style*="width:24%"] {
    width: calc(100% - 10px) !important;
    right: 5px !important;
    left: 5px !important;
    margin-left: 5px !important;
  }
}
@media (min-width: 481px) and (max-width: 767px) {
  .exam {
    right: 10px !important;
    font-size: 20px !important;
    width: auto;
  }
  .exam2 {
    right: 10px !important;
    width: 180px !important;
  }
  .mapslayout {
    top: 120px !important;
    right: 10px !important;
    max-width: 400px;
  }
  .toggle-button {
    height: 32px !important;
    width: 32px !important;
  }
  .popup-card-error {
    width: 70% !important;
    right: 15px !important;
    max-height: 60vh;
  }
  .tool-sidebar {
    width: 48px !important;
  }
  .legendDefects {
    left: 10px !important;
    max-width: 300px;
  }
  .dtmLegend,
  .slopeLegend {
    right: 10px !important;
  }
  mat-card[style*="width:25%"] {
    width: 60% !important;
    right: 10px !important;
  }
  mat-card[style*="width:16%"] {
    width: 50% !important;
    right: 10px !important;
    margin-left: 10px !important;
  }
  mat-card[style*="width:24%"] {
    width: 60% !important;
    right: 10px !important;
    margin-left: 10px !important;
  }
}
@media (max-width: 360px) {
  .exam {
    font-size: 14px !important;
    top: 2px !important;
    right: 2px !important;
    width: calc(100% - 50px) !important;
  }
  .exam2 {
    top: 25px !important;
    right: 2px !important;
    width: calc(100% - 50px) !important;
    padding: 2px !important;
  }
  .mapslayout {
    top: 55px !important;
    right: 2px !important;
    left: 2px !important;
    width: calc(100% - 4px) !important;
    padding: 5px !important;
  }
  .img-dim {
    height: 30px !important;
    width: 30px !important;
  }
  .span-font {
    font-size: 0.6rem !important;
  }
  .toggle-button {
    height: 28px !important;
    width: 28px !important;
  }
  .tool-sidebar {
    width: 40px !important;
  }
}
@media (max-width: 480px) {
  ::ng-deep .mat-mdc-select-panel {
    max-width: calc(100vw - 20px) !important;
    max-height: 50vh !important;
  }
  ::ng-deep .mat-mdc-select-value {
    font-size: 12px !important;
  }
  ::ng-deep .mat-mdc-card {
    padding: 8px !important;
  }
  ::ng-deep .mat-mdc-card-header {
    padding: 8px !important;
  }
  ::ng-deep .mat-mdc-card-content {
    padding: 8px !important;
    font-size: 12px !important;
  }
  ::ng-deep .mat-mdc-icon-button {
    width: 32px !important;
    height: 32px !important;
    padding: 4px !important;
  }
  ::ng-deep .mat-mdc-icon-button .mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
  ::ng-deep .mat-mdc-list-item {
    min-height: 40px !important;
    padding: 4px 8px !important;
  }
  ::ng-deep .mat-mdc-nav-list .mat-mdc-list-item {
    min-height: 40px !important;
  }
  ::ng-deep .mat-mdc-tooltip {
    font-size: 11px !important;
    padding: 4px 6px !important;
    max-width: 200px !important;
  }
}
@media (max-width: 480px) {
  body,
  html {
    overflow-x: hidden !important;
  }
  input,
  select,
  textarea {
    font-size: 16px !important;
  }
  button,
  .mat-mdc-button,
  .mat-mdc-icon-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }
  .scrollable-mobile {
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
