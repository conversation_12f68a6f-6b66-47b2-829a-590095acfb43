<div class="container box table-responsive">
  <p class="title">manage users</p>
  <table class="table table-bordered align-middle">
    <caption class="text-center">
      Total Users:
      {{ users_data.length }}
    </caption>
    <thead>
      <tr>
        <th>Full name</th>
        <th>Email</th>
        <th>Role</th>
        <th>Active</th>
        <th>Created On</th>
        <th>Edit</th>
      </tr>
    </thead>
    <tbody class="table-group-divider">
      <tr *ngFor="let user of users_data; let i = index">
        <td class="text-capitalize">{{ user.name }}</td>
        <td>{{ user.email }}</td>
        <td class="text-capitalize">{{ user.role ? user.role : '---' }}</td>
        <td>
          <div class="form-check form-switch toggle">
            <input
              class="form-check-input"
              type="checkbox"
              role="switch"
              name="enabled"
              [(ngModel)]="user.enabled"
              (change)="toggleUser(user)" />
          </div>
        </td>
        <td>{{ user.created_on | date: 'dd-MM-yyyy HH:mm:ss a' }}</td>
        <td>
          <button mat-mini-fab (click)="openDialog(user)" [disabled]="!user.enabled">
            <mat-icon>edit</mat-icon>
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
