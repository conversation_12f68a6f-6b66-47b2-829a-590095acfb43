const path = require('path');

module.exports = {
  mode: 'production',
  entry: {
    polyfills: path.resolve(__dirname, 'src/polyfills.ts'),
    app: path.resolve(__dirname, 'src/main.ts'),
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    modules: ['node_modules', 'src'],
    alias: {
      library: path.resolve(__dirname, 'src/app/library'),
      environment: path.resolve(__dirname, 'src/environments'),
      leaflet: path.resolve(__dirname, 'node_modules/leaflet/dist/leaflet.js'),
      togeojson: path.resolve(__dirname, 'node_modules/@tmcw/togeojson/'),
    },
  },
  target: 'web',
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
        options: {
          transpileOnly: true,
        },
      },
      // handle sass files
      {
        test: /\.scss$/,
        exclude: [/node_modules/, path.resolve(__dirname, 'src/styles.scss')],
        use: ['style-loader', 'css-loader', 'sass-loader'],
      },
      // handle css files
      {
        test: /\.css$/,
        exclude: [
          /node_modules/,
          path.resolve(__dirname, 'src/assets/css/style.min.css'),
          path.resolve(__dirname, 'src/app/'),
        ],
        include: [/leaflet-side-by-side/],
        use: ['style-loader', 'css-loader'],
      },
      // handle range.css and layout.css from leaflet-side-by-side module
      {
        test: /\.css$/,
        include: /leaflet-side-by-side/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
            },
          },
        ],
      },
      // handle postcss files
      {
        test: /\.postcss$/,
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
    ],
  },
};
