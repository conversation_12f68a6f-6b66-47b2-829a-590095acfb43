import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import 'leaflet';
import 'leaflet-kml';

@Component({
  selector: 'project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.css'],
})
export class ProjectDetailsComponent implements OnInit {
  // @ViewChild('stepper') stepper: MatStepper;
  @Input() get_dashboard_data: any;
  slides: any;
  tittle: any;
  map;
  base_ortho_layer;
  ortho_file_location;
  tab_name = 'thermography';

  @Input() main_data: any[] = []; // This should be your input data
  recent_3_projects: any = [];
  slideIndex: number = 1;

  project_name: string = '';
  recent_3_projects_name: string = '';
  recent_3_projects_date: string = '';
  project_number: string = '';
  project_id: string = '';
  center_val: string = '';
  workflow_status: string = '';
  // new_stepper_index: number = 0;

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.getProcessedData();
  }
  getProcessedData(): void {
    if (this.main_data.length) {
      this.recent_3_projects = this.main_data.map(row_data => ({
        name: row_data['name'],
        project_id: row_data['id'],
        date: Object.keys(row_data['date_status']),
        city: row_data['city'],
        status: Object.values(row_data['date_status']),
        center: row_data['center'],
        image: row_data['image'],
      }));
      this.sort3projects();
    }
  }

  sort3projects(): void {
    if (this.recent_3_projects.length > 0) {
      this.updateProjectDetails(this.recent_3_projects[0], 1);
    }
  }

  Clickable_recent3(index: number): void {
    this.slideIndex = index + 1;
    this.showSlides(this.slideIndex);
  }

  plusSlides(n: number): void {
    this.showSlides(this.slideIndex + n);
  }

  showSlides(n: number): void {
    const totalProjects = this.recent_3_projects.length;
    this.slideIndex = n > totalProjects ? 1 : n < 1 ? totalProjects : n;

    const currentProject = this.recent_3_projects[this.slideIndex - 1];
    this.updateProjectDetails(currentProject, this.slideIndex);
  }

  updateProjectDetails(project: any, slideIndex: number): void {
    this.project_name = project.name;
    this.project_id = project.project_id;
    this.center_val = project.center;
    this.recent_3_projects_name = project.name;
    this.recent_3_projects_date = project.date[0];
    this.project_number = `0${slideIndex}`;
    // this.workflow_status = project.status[0];

    // const statusToStepperIndexMap: { [key: string]: number } = {
    //   created: 1,
    //   ftp: 2,
    //   processing: 3,
    //   completed: 4,
    // };

    // this.new_stepper_index = statusToStepperIndexMap[this.workflow_status] || 1;
    // this.stepper.reset();
    // for (let s = 0; s < this.new_stepper_index; s++) {
    //   this.stepper.selectedIndex = s;
    // }
  }
  setCommonlocalStorage() {
    localStorage.setItem('date', this.recent_3_projects_date);
    localStorage.setItem('center', this.center_val);
    localStorage.setItem('project_id', this.project_id);
    localStorage.setItem('proj_name', this.project_name);
  }
  gotomap() {
    this.setCommonlocalStorage();
    this.router.navigate(['/map']);
  }
  gotoAnalytics() {
    this.setCommonlocalStorage();
    this.router.navigate(['app/analytics']);
  }
}
