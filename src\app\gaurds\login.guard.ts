import { AuthService } from '@/controller/auth.service';
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class LoginGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService
  ) {}
  async canActivate(): Promise<boolean> {
    const isLoggedIn = await this.authService.isLoggedIn();
    if (isLoggedIn) {
      return true; // User is logged in, allow access to the protected route
    } else {
      this.router.navigate(['/auth', 'login']);
      return false;
    }
  }
}
