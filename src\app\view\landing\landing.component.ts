import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import * as $ from 'jquery';
import { environment } from 'src/environments/environment';

@Component({
  standalone: true,
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css'],
  imports: [MatTabsModule, MatIconModule, CommonModule, RouterModule],
})
export class LandingComponent implements OnInit, OnDestroy {
  demo_video = environment.demo_video;

  isMenuActive: boolean = false;
  scroll = (event: any): void => {
    let previousHash: string;
    const number = event.srcElement.scrollTop;
    number > 150
      ? $('.js-header-scroll').addClass('header__sticky')
      : $('.js-header-scroll').removeClass('header__sticky');
    number > 200 ? $('.js-back-to-top').fadeIn() : $('.js-back-to-top').fadeOut();

    $('section').each(function () {
      const hash = $(this).attr('id');
      const Scroll = $(window).scrollTop() + 1;
      if (hash) {
        if (Scroll >= $('#' + hash).offset().top) {
          if (previousHash) {
            $('#menu-' + previousHash).removeClass('active');
          }
          $('#menu-' + hash).addClass('active');
          previousHash = hash;
        } else {
          $('#menu-' + hash).removeClass('active');
        }
      }
    });
  };
  ngOnInit(): void {
    window.addEventListener('scroll', this.scroll, true);
  }
  scrollToPosition(el: string) {
    const element = document.getElementById(el);
    element.scrollIntoView({
      behavior: 'smooth',
    });
  }
  ngOnDestroy() {
    window.removeEventListener('scroll', this.scroll, true);
  }
  toggleMenu() {
    this.isMenuActive = !this.isMenuActive;
  }
}
