export interface Drawtool {
  id: number;
  project: number;
  date: string;
  label: string;
  description: string;
  polygon: any;
  area: number;
  aoi_type: string;
  is_active: boolean;
  is_resolved: boolean;
  created_by: number;
  assigned_to: number | null;
  created_at: string;
}
export class DrawtoolC {
  id: number;
  project: number;
  date: string;
  label: string;
  description: string;
  polygon: any;
  area: number;
  aoi_type: string;
  is_active: boolean;
  is_resolved: boolean;
  created_by: number;
  assigned_to: number | null;
  created_at: string;

  constructor(obj?: DrawtoolC) {
    this.id = (obj && obj.id) || null;
    this.project = (obj && obj.project) || null;
    this.date = (obj && obj.date) || '';
    this.label = (obj && obj.label) || '';
    this.description = (obj && obj.description) || '';
    this.polygon = (obj && obj.polygon) || {};
    this.area = (obj && obj.area) || null;
    this.aoi_type = (obj && obj.aoi_type) || '';
    this.is_active = (obj && obj.is_active) || true;
    this.is_resolved = (obj && obj.is_resolved) || false;
    this.created_by = (obj && obj.created_by) || null;
    this.assigned_to = (obj && obj.assigned_to) || null;
    this.created_at = (obj && obj.created_at) || null;
  }
}
