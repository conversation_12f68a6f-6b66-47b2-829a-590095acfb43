<div class="container-responsive">
  <mat-card>
    <mat-card-content>
      <h4 class="step-heading text-center font-weight-bold heading-responsive">Create Project</h4>
      <div class="row flex-column-mobile">
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Project Name</mat-label>
            <input matInput placeholder="Enter Project Name" #pro_name />
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Plant Capacity (in MW)</mat-label>
            <input type="number" matInput placeholder="Enter Plant Size in MW" #plant_size />
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Plant Capacity (in MWp)</mat-label>
            <input
              type="number"
              matInput
              placeholder="Enter Plant Capacity in MWp"
              #plant_capacity />
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field color="primary" appearance="fill" class="w-100">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="picker1" #s_date />
            <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Type of Module</mat-label>
            <mat-select (selectionChange)="module($event.value)">
              <mat-option value="Polycrystalline">Polycrystalline</mat-option>
              <mat-option value="Thin films">Thin films</mat-option>
              <mat-option value="Monocrystalline">Monocrystalline</mat-option>
              <mat-option value="Bifacial">Bifacial</mat-option>
              <mat-option value="Mono-PERC">Mono-PERC</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Module Rating</mat-label>
            <input matInput placeholder="Module Rating" #Module_Rating />
          </mat-form-field>
        </div>
        <div class="col-md-2">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Manufacturer</mat-label>
            <input matInput placeholder="Manufacturer" #Manufacturer />
          </mat-form-field>
        </div>
        <div class="col-md-2">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Model No</mat-label>
            <input matInput placeholder="Model No" #Model_No />
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Type of Table</mat-label>
            <mat-select (selectionChange)="table($event.value)">
              <mat-option value="Fixed Structure">Fixed Structure</mat-option>
              <mat-option value="Tracker">Tracker</mat-option>
              <mat-option value="Fixed Structure & Tracker">Fixed Structure & Tracker</mat-option>
              <mat-option value="Fixed Structure & Rotate">Fixed Structure & Rotate</mat-option>
              <mat-option value="Seasonal Tilt">Seasonal Tilt</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Type of Inverter</mat-label>
            <mat-select (selectionChange)="inverter($event.value)">
              <mat-option value="Central Inverter">Central Inverters</mat-option>
              <mat-option value="String Inverter">String Inverter</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label class="field-content">Country</mat-label>
            <mat-select placeholder="Select Country" (selectionChange)="selectChange($event.value)">
              <mat-option *ngFor="let country of country_data" [value]="country.isoCode">
                {{ country.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label class="field-content">State</mat-label>
            <mat-select placeholder="Select State" (selectionChange)="selectState($event.value)">
              <mat-option *ngFor="let state of states_data" [value]="state.isoCode">
                {{ state.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-4">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label class="field-content">City</mat-label>
            <mat-select
              placeholder="Select City"
              (selectionChange)="selectCity($event.value); city($event.value)">
              <mat-option *ngFor="let city_data of cities_data" [value]="city_data.name">
                {{ city_data.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-md-12">
          <mat-form-field appearance="fill" class="w-100">
            <mat-label>Description</mat-label>
            <textarea #desc matInput placeholder="Write Description here...."></textarea>
          </mat-form-field>
        </div>
      </div>
    </mat-card-content>
    <mat-card-actions class="action text-center">
      <!-- <button mat-button class="btn stepper-action-btn text-uppercase" (click)="next(pro_name.value,plant_size.value,plant_capacity.value,s_date.value,desc.value)" >NEXT</button> -->
      <button
        disabled
        mat-button
        class="btn stepper-action-btn text-uppercase w-25"
        (click)="
          create_new_Project(
            pro_name.value,
            plant_size.value,
            plant_capacity.value,
            s_date.value,
            desc.value
          )
        ">
        SAVE
      </button>
    </mat-card-actions>
  </mat-card>
</div>
