<div class="compact-sidebar isclose" id="defectssidebar" *ngIf="isVisible">
  <div class="isVisible500">
    <button class="close-btn" (click)="toggleSidebarVisibility()">X</button>
  </div>
  <div class="sidebar-menu">
    <ng-container *ngFor="let i of defects; let k = index">
      <mat-card
        class="main-card"
        [style.background-color]="selectedTable === i ? 'lightgray' : 'white'">
        <div class="type">
          <p>
            Table No.:
            <span>{{ i.Table_No }}</span>
          </p>
          <p>
            Module No.:
            <span>{{ i.Module_No }}</span>
          </p>
          <p>
            Defect Type:
            <span>{{ i.Defect }}</span>
          </p>
        </div>
        <div class="type_btn">
          <mat-icon
            matTooltip="Click to locate defect on map"
            (click)="selectedMaplocation(i.Latitude, i.Longitude, i)">
            my_location
          </mat-icon>
          <mat-icon
            *ngIf="i.RGB_Image && i.Thermal_Image"
            matTooltip="Click to view RGB & thermal image"
            (click)="openDialog(imageDialog, i)">
            image_search
          </mat-icon>
        </div>
      </mat-card>
    </ng-container>
  </div>
</div>
<ng-template #imageDialog let-data let-dialogRef="dialogRef">
  <div class="image-modal-container">
    <div class="defect-info">
      <div class="info-item">
        <span class="info-label">Table No.:</span>
        <span class="info-value">{{ data.Table_No }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Module No.:</span>
        <span class="info-value">{{ data.Module_No }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Defect Type:</span>
        <span class="info-value">{{ data.Defect }}</span>
      </div>
    </div>
    <div class="images-container">
      <div class="image-wrapper">
        <h3 class="image-title">
          <button
            mat-button
            style="width: 40%"
            class="text-uppercase"
            (click)="downloadImage(data.RGB_Image)"
            [disabled]="!data.RGB_Image">
            <mat-icon>download</mat-icon>
            RGB Image
          </button>
        </h3>
        <div class="image-box">
          <img [src]="data.RGB_Image" alt="RGB Image" class="image" *ngIf="data.RGB_Image" />
          <div class="no-image" *ngIf="!data.RGB_Image">
            <mat-icon>image_not_supported</mat-icon>
            <p>No RGB Image</p>
          </div>
        </div>
      </div>
      <div class="image-wrapper">
        <h3 class="image-title">
          <button
            mat-button
            style="width: 40%"
            class="text-uppercase"
            (click)="downloadImage(data.Thermal_Image)"
            [disabled]="!data.Thermal_Image">
            <mat-icon>download</mat-icon>
            Thermal Image
          </button>
        </h3>
        <div class="image-box">
          <img
            [src]="data.Thermal_Image"
            alt="Thermal Image"
            class="image"
            *ngIf="data.Thermal_Image" />
          <div class="no-image" *ngIf="!data.Thermal_Image">
            <mat-icon>image_not_supported</mat-icon>
            <p>No Thermal Image</p>
          </div>
        </div>
      </div>
    </div>
    <div class="w-100 text-center my-2">
      <button mat-button (click)="dialogRef.close()" class="mt-3 text-uppercase" style="width: 10%">
        Close
      </button>
    </div>
  </div>
</ng-template>
