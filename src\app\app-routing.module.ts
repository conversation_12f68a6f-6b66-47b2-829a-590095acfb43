import { LoginGuard } from '@/gaurds/login.guard';
import { LogoutGuard } from '@/gaurds/logout.guard';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LandingComponent } from './view/landing/landing.component';
import { PageNotFoundComponent } from './view/page-not-found/page-not-found.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'landing',
    pathMatch: 'full',
  },
  {
    path: 'landing',
    component: LandingComponent,
  },
  {
    path: 'auth',
    canActivate: [LogoutGuard],
    loadChildren: () =>
      import('./view/authentication/authentication.module').then(m => m.AuthenticationModule),
  },
  {
    path: 'app',
    canActivate: [LoginGuard],
    loadChildren: () => import('./view/section/section.module').then(m => m.SectionModule),
  },
  {
    path: 'map',
    canActivate: [LoginGuard],
    loadChildren: () =>
      import('./view/map-section/map-section.module').then(m => m.MapSectionModule),
  },
  {
    path: '404',
    pathMatch: 'full',
    component: PageNotFoundComponent,
  },
  {
    path: '**',
    component: PageNotFoundComponent,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
